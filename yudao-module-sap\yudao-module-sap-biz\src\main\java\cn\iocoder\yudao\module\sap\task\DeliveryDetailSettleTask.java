package cn.iocoder.yudao.module.sap.task;

import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqDeliveryDetailDO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDeliveryDetailService;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailSettleService;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailSimpleDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailSettleParamsDTO;
import cn.iocoder.yudao.module.sap.enums.DeliveryDetailSettleStatus;
import cn.iocoder.yudao.module.sap.exception.TaskLockedException;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static cn.iocoder.yudao.module.sap.enums.DeliveryDetailSettleStatus.*;

/**
 * 发货明细结算 - 定时任务
 * 包含三个阶段的结算处理：主表结算、OMS明细结算、拆单明细结算
 * 
 * 所有结算任务共用同一个锁，保证结算任务不会同时执行，避免并发问题
 */
@Component
public class DeliveryDetailSettleTask {

    @Resource
    private DeliveryDetailSettleService deliveryDetailSettleService;

    @Resource
    private CqDeliveryDetailService cqDeliveryDetailService;
    
    @Resource
    private RedissonClient redissonClient;
    
    @Resource
    private cn.iocoder.yudao.module.sap.utils.DingDingUtils dingDingUtils;
    
    /**
     * 发货明细结算统一任务锁键名
     * 所有结算任务都使用此锁，保证任何时候只有一个结算任务在执行
     */
    private static final String DELIVERY_DETAIL_SETTLE_LOCK_KEY = "sap:task:delivery_detail_settle";

    /**
     * 执行主表结算
     * 处理结算状态为{@link DeliveryDetailSettleStatus#MAIN_PENDING_SETTLE}的发货明细
     */
    @XxlJob("jsp.executeMainSettle")
    @TenantIgnore
    public void executeMainSettle() {
        XxlJobHelper.log("[INFO] [executeMainSettle][开始执行主表结算]");
        
        // 创建Redis锁对象，使用统一锁
        RLock lock = redissonClient.getLock(DELIVERY_DETAIL_SETTLE_LOCK_KEY);
        
        boolean lockAcquired = false;
        try {
            // 尝试获取锁，等待10秒，启用自动续期
            lockAcquired = lock.tryLock(10, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                XxlJobHelper.log("[INFO] [executeMainSettle][获取锁失败，任务已被其他实例执行]");
                sendLockFailureNotification("发货明细结算-主表结算", "executeMainSettle", XxlJobHelper.getJobParam(), DELIVERY_DETAIL_SETTLE_LOCK_KEY);
                return;
            }
            
            XxlJobHelper.log("[INFO] [executeMainSettle][成功获取锁，开始执行任务]");
            
            try {
                // 获取XxlJob参数，支持JSON格式：{"billNos": ["单号1", "单号2", ...], "shopNos": ["店铺1", "店铺2", ...], "startDate": "2024-01-01", "endDate": "2024-01-31"}
                String param = XxlJobHelper.getJobParam();
                XxlJobHelper.log(String.format("[INFO] [executeMainSettle][接收到任务参数: %s]", param));

                // 解析参数
                DeliveryDetailSettleParamsDTO paramsDTO = deliveryDetailSettleService.parseSettleParams(param);
                
                // 查询待处理的单据（结算状态为1），使用优化的查询方法
                deliveryDetailSettleService.doMainSettle(paramsDTO);
                XxlJobHelper.log("[INFO] [executeMainSettle][主表结算处理完成]");

            } catch (Exception e) {
                XxlJobHelper.log("[ERROR] [executeMainSettle][执行主表结算任务异常] - " + e.getMessage());
            }
        } catch (InterruptedException e) {
            XxlJobHelper.log("[ERROR] [executeMainSettle][获取锁被中断] - " + e.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            XxlJobHelper.log("[ERROR] [executeMainSettle][执行任务发生异常] - " + e.getMessage());
        } finally {
            // 在finally块中释放锁，确保锁一定会被释放
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
                XxlJobHelper.log("[INFO] [executeMainSettle][释放锁成功]");
            }
        }
    }

    /**
     * 执行OMS明细结算
     * 处理结算状态为{@link DeliveryDetailSettleStatus#OMS_DETAIL_PENDING_SETTLE}的发货明细
     */
    @XxlJob("jsp.executeOMSDetailSettle")
    @TenantIgnore
    public void executeOMSDetailSettle() {
        XxlJobHelper.log("[INFO] [executeOMSDetailSettle][开始执行OMS明细结算]");
        
        // 创建Redis锁对象，使用统一锁
        RLock lock = redissonClient.getLock(DELIVERY_DETAIL_SETTLE_LOCK_KEY);
        
        boolean lockAcquired = false;
        try {
            // 尝试获取锁，等待10秒，启用自动续期
            lockAcquired = lock.tryLock(10, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                XxlJobHelper.log("[INFO] [executeOMSDetailSettle][获取锁失败，任务已被其他实例执行]");
                sendLockFailureNotification("发货明细结算-OMS明细结算", "executeOMSDetailSettle", XxlJobHelper.getJobParam(), DELIVERY_DETAIL_SETTLE_LOCK_KEY);
                return;
            }
            
            XxlJobHelper.log("[INFO] [executeOMSDetailSettle][成功获取锁，开始执行任务]");
            
            try {
                String param = XxlJobHelper.getJobParam();
                XxlJobHelper.log(String.format("[INFO] [executeOMSDetailSettle][接收到任务参数: %s]", param));

                // 解析参数
                DeliveryDetailSettleParamsDTO paramsDTO = deliveryDetailSettleService.parseSettleParams(param);
                
                // 查询待处理的单据（结算状态为2），使用优化的查询方法
                deliveryDetailSettleService.doOMSDetailSettle(paramsDTO);

                XxlJobHelper.log("[INFO] [executeOMSDetailSettle][OMS明细结算处理完成]");
                
            } catch (Exception e) {
                XxlJobHelper.log("[ERROR] [executeOMSDetailSettle][执行OMS明细结算任务异常] - " + e.getMessage());
            }
        } catch (InterruptedException e) {
            XxlJobHelper.log("[ERROR] [executeOMSDetailSettle][获取锁被中断] - " + e.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            XxlJobHelper.log("[ERROR] [executeOMSDetailSettle][执行任务发生异常] - " + e.getMessage());
        } finally {
            // 在finally块中释放锁，确保锁一定会被释放
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
                XxlJobHelper.log("[INFO] [executeOMSDetailSettle][释放锁成功]");
            }
        }
    }

    /**
     * 执行拆单明细结算
     * 处理结算状态为{@link DeliveryDetailSettleStatus#SPLIT_DETAIL_PENDING_SETTLE}的发货明细
     */
    @XxlJob("jsp.executeSplitDetailSettle")
    @TenantIgnore
    public void executeSplitDetailSettle() {
        XxlJobHelper.log("[INFO] [executeSplitDetailSettle][开始执行拆单明细结算]");
        
        // 创建Redis锁对象，使用统一锁
        RLock lock = redissonClient.getLock(DELIVERY_DETAIL_SETTLE_LOCK_KEY);
        
        boolean lockAcquired = false;
        try {
            // 尝试获取锁，等待10秒，启用自动续期
            lockAcquired = lock.tryLock(10, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                XxlJobHelper.log("[INFO] [executeSplitDetailSettle][获取锁失败，任务已被其他实例执行]");
                sendLockFailureNotification("发货明细结算-拆单明细结算", "executeSplitDetailSettle", XxlJobHelper.getJobParam(), DELIVERY_DETAIL_SETTLE_LOCK_KEY);
                return;
            }
            
            XxlJobHelper.log("[INFO] [executeSplitDetailSettle][成功获取锁，开始执行任务]");
            
            try {
                String param = XxlJobHelper.getJobParam();
                XxlJobHelper.log(String.format("[INFO] [executeSplitDetailSettle][接收到任务参数: %s]", param));
                
                // 解析参数
                DeliveryDetailSettleParamsDTO paramsDTO = deliveryDetailSettleService.parseSettleParams(param);
                
                // 查询待处理的单据（结算状态为3），使用优化的查询方法
                deliveryDetailSettleService.doSplitDetailSettle(paramsDTO);

                XxlJobHelper.log("[INFO] [executeSplitDetailSettle][拆单明细结算处理完成]");
                
            } catch (Exception e) {
                XxlJobHelper.log("[ERROR] [executeSplitDetailSettle][执行拆单明细结算任务异常] - " + e.getMessage());
            }
        } catch (InterruptedException e) {
            XxlJobHelper.log("[ERROR] [executeSplitDetailSettle][获取锁被中断] - " + e.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            XxlJobHelper.log("[ERROR] [executeSplitDetailSettle][执行任务发生异常] - " + e.getMessage());
        } finally {
            // 在finally块中释放锁，确保锁一定会被释放
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
                XxlJobHelper.log("[INFO] [executeSplitDetailSettle][释放锁成功]");
            }
        }
    }

    // /**
    //  * 执行全部发货明细结算
    //  * 依次执行主表、OMS明细、拆单明细结算的流程
    //  * 注意：使用统一锁，确保与其他结算任务互斥
    //  */
    // @XxlJob("jsp.executeAllSettle")
    // @TenantIgnore
    // public void executeAllSettle() {
    //     XxlJobHelper.log("[INFO] [executeAllSettle][开始执行全部发货明细结算]");
        
    //     // 创建Redis锁对象，使用统一锁
    //     RLock lock = redissonClient.getLock(DELIVERY_DETAIL_SETTLE_LOCK_KEY);
        
    //     boolean lockAcquired = false;
    //     try {
    //         // 尝试获取锁，等待10秒，启用自动续期
    //         lockAcquired = lock.tryLock(10, TimeUnit.SECONDS);
            
    //         if (!lockAcquired) {
    //             XxlJobHelper.log("[INFO] [executeAllSettle][获取锁失败，任务已被其他实例执行]");
    //             return;
    //         }
            
    //         XxlJobHelper.log("[INFO] [executeAllSettle][成功获取锁，开始执行任务]");
            
    //         // 获取XxlJob参数
    //         String param = XxlJobHelper.getJobParam();
    //         List<String> billNos = null;
    //         List<String> shopNos = null;
    //         LocalDate startDate = null;
    //         LocalDate endDate = null;
            
    //         if (StrUtil.isNotBlank(param)) {
    //             try {
    //                 JSONObject jsonParam = JSONUtil.parseObj(param);
    //                 billNos = jsonParam.getBeanList("billNos", String.class);
    //                 shopNos = jsonParam.getBeanList("shopNos", String.class);
                    
    //                 // 解析日期参数
    //                 String startDateStr = jsonParam.getStr("startDate");
    //                 String endDateStr = jsonParam.getStr("endDate");
    //                 if (StrUtil.isNotBlank(startDateStr)) {
    //                     startDate = LocalDate.parse(startDateStr);
    //                 }
    //                 if (StrUtil.isNotBlank(endDateStr)) {
    //                     endDate = LocalDate.parse(endDateStr);
    //                 }
                    
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][参数解析成功: billNos=%d, shopNos=%d, startDate=%s, endDate=%s]", 
    //                         billNos != null ? billNos.size() : 0, 
    //                         shopNos != null ? shopNos.size() : 0,
    //                         startDate, endDate));
    //             } catch (Exception e) {
    //                 XxlJobHelper.log(String.format("[WARN] [executeAllSettle][解析参数失败: %s] - %s", param, e.getMessage()));
    //             }
    //         }
            
    //         try {
    //             // 1. 处理主表结算 - 直接调用查询和处理方法，而不是调用executeMainSettle方法
    //             List<CqDeliveryDetailSimpleDTO> mainSettleDetails = cqDeliveryDetailService.getDeliveryDetailSimpleBySettleStatus(billNos, MAIN_PENDING_SETTLE.getValue(), shopNos, startDate, endDate);
                
    //             if (!mainSettleDetails.isEmpty()) {
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][找到%d个待处理的主表结算单据]", mainSettleDetails.size()));
    //                 int mainSuccessCount = 0;
    //                 for (CqDeliveryDetailSimpleDTO detail : mainSettleDetails) {
    //                     try {
    //                         deliveryDetailSettleService.doMainSettle(detail.getBillNo());
    //                         mainSuccessCount++;
    //                     } catch (Exception e) {
    //                         XxlJobHelper.log(String.format("[ERROR] [executeAllSettle][主表结算处理单据%s失败] - %s", detail.getBillNo(), e.getMessage()));
    //                     }
    //                 }
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][主表结算处理完成，共处理%d个单据，成功%d个]", mainSettleDetails.size(), mainSuccessCount));
    //             } else {
    //                 XxlJobHelper.log("[INFO] [executeAllSettle][没有需要处理的主表结算单据]");
    //             }
    //         } catch (Exception e) {
    //             XxlJobHelper.log("[ERROR] [executeAllSettle][主表结算异常] - " + e.getMessage());
    //         }
            
    //         try {
    //             // 2. 处理OMS明细结算 - 直接调用查询和处理方法，而不是调用executeOMSDetailSettle方法
    //             List<CqDeliveryDetailSimpleDTO> omsSettleDetails = cqDeliveryDetailService.getDeliveryDetailSimpleBySettleStatus(billNos, OMS_DETAIL_PENDING_SETTLE.getValue(), shopNos, startDate, endDate);
                
    //             if (!omsSettleDetails.isEmpty()) {
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][找到%d个待处理的OMS明细结算单据]", omsSettleDetails.size()));
    //                 int omsSuccessCount = 0;
    //                 for (CqDeliveryDetailSimpleDTO detail : omsSettleDetails) {
    //                     try {
    //                         deliveryDetailSettleService.doOMSDetailSettle(detail.getBillNo());
    //                         omsSuccessCount++;
    //                     } catch (Exception e) {
    //                         XxlJobHelper.log(String.format("[ERROR] [executeAllSettle][OMS明细结算处理单据%s失败] - %s", detail.getBillNo(), e.getMessage()));
    //                     }
    //                 }
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][OMS明细结算处理完成，共处理%d个单据，成功%d个]", omsSettleDetails.size(), omsSuccessCount));
    //             } else {
    //                 XxlJobHelper.log("[INFO] [executeAllSettle][没有需要处理的OMS明细结算单据]");
    //             }
    //         } catch (Exception e) {
    //             XxlJobHelper.log("[ERROR] [executeAllSettle][OMS明细结算异常] - " + e.getMessage());
    //         }
            
    //         try {
    //             // 3. 处理拆单明细结算 - 直接调用查询和处理方法，而不是调用executeSplitDetailSettle方法
    //             List<CqDeliveryDetailSimpleDTO> splitSettleDetails = cqDeliveryDetailService.getDeliveryDetailSimpleBySettleStatus(billNos, SPLIT_DETAIL_PENDING_SETTLE.getValue(), shopNos, startDate, endDate);
                
    //             if (!splitSettleDetails.isEmpty()) {
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][找到%d个待处理的拆单明细结算单据]", splitSettleDetails.size()));
    //                 int splitSuccessCount = 0;
    //                 for (CqDeliveryDetailSimpleDTO detail : splitSettleDetails) {
    //                     try {
    //                         deliveryDetailSettleService.doSplitDetailSettle(detail.getBillNo());
    //                         splitSuccessCount++;
    //                     } catch (Exception e) {
    //                         XxlJobHelper.log(String.format("[ERROR] [executeAllSettle][拆单明细结算处理单据%s失败] - %s", detail.getBillNo(), e.getMessage()));
    //                     }
    //                 }
    //                 XxlJobHelper.log(String.format("[INFO] [executeAllSettle][拆单明细结算处理完成，共处理%d个单据，成功%d个]", splitSettleDetails.size(), splitSuccessCount));
    //             } else {
    //                 XxlJobHelper.log("[INFO] [executeAllSettle][没有需要处理的拆单明细结算单据]");
    //             }
    //         } catch (Exception e) {
    //             XxlJobHelper.log("[ERROR] [executeAllSettle][拆单明细结算异常] - " + e.getMessage());
    //         }
            
    //     } catch (InterruptedException e) {
    //         XxlJobHelper.log("[ERROR] [executeAllSettle][获取锁被中断] - " + e.getMessage());
    //         Thread.currentThread().interrupt();
    //     } catch (Exception e) {
    //         XxlJobHelper.log("[ERROR] [executeAllSettle][执行任务发生异常] - " + e.getMessage());
    //     } finally {
    //         // 在finally块中释放锁，确保锁一定会被释放
    //         if (lockAcquired && lock.isHeldByCurrentThread()) {
    //             lock.unlock();
    //             XxlJobHelper.log("[INFO] [executeAllSettle][释放锁成功]");
    //         }
    //     }
        
    //     XxlJobHelper.log("[INFO] [executeAllSettle][结束执行全部发货明细结算]");
    // }
    
    /**
     * 发送锁获取失败通知到钉钉群
     * 
     * @param taskName 任务名称
     * @param methodName 方法名称
     * @param param 任务参数
     * @param lockKey 锁键名
     */
    private void sendLockFailureNotification(String taskName, String methodName, String param, String lockKey) {
        try {
            // 格式化当前时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 构建Markdown格式的通知消息
            String title = "⚠️ 任务锁获取失败通知";
            String content = String.format(
                "## ⚠️ 任务锁获取失败通知\n\n" +
                "**发生时间**: %s\n\n" +
                "**任务名称**: %s\n\n" +
                "**执行方法**: `%s`\n\n" +
                "**锁键名**: `%s`\n\n" +
                "**失败原因**: 锁已被其他实例占用，任务无法执行\n\n" +
                "**传入参数**: %s\n\n" +
                "---\n\n" +
                "**处理建议**: 请检查是否有其他实例正在执行相同任务，或考虑调整任务调度时间避免冲突。\n\n" +
                "*此消息由任务调度系统自动发送*",
                currentTime,
                taskName,
                methodName,
                lockKey,
                param != null && !param.trim().isEmpty() ? "```json\n" + param + "\n```" : "无参数"
            );
            
            // 发送钉钉消息到异常通知机器人
            boolean success = dingDingUtils.sendMarkdownMessage(title, content, "exception-notify");
            
            if (success) {
                XxlJobHelper.log(String.format("[INFO] [%s][锁获取失败通知发送成功]", methodName));
            } else {
                XxlJobHelper.log(String.format("[WARN] [%s][锁获取失败通知发送失败]", methodName));
            }
        } catch (Exception e) {
            XxlJobHelper.log(String.format("[ERROR] [%s][发送锁获取失败通知异常] - %s", methodName, e.getMessage()));
        }
    }
}

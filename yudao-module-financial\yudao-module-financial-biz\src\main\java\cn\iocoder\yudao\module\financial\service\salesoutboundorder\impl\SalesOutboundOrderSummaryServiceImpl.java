package cn.iocoder.yudao.module.financial.service.salesoutboundorder.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.financial.dal.dataobject.*;
import cn.iocoder.yudao.module.financial.dal.mysql.CggCustomerMappingMapper;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.CggSalePriceListMapper;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.SalesOutboundOrderEntryMapper;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.SalesOutboundOrderExcludeMaterialMapper;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.SalesOutboundOrderMapper;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.SalesOutboundOrderSummaryEntryMapper;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.SalesOutboundOrderSummaryMapper;
import cn.iocoder.yudao.module.financial.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.financial.service.outside.EasApiService;
import cn.iocoder.yudao.module.financial.service.cggOnlineFormErpWarehouseInfo.CggOnlineFormErpWarehouseInfoService;
import cn.iocoder.yudao.module.financial.service.salesoutboundorder.SalesOutboundOrderSummaryService;
import cn.iocoder.yudao.module.financial.service.salepricelist.CggSalePriceListService;
import cn.iocoder.yudao.module.financial.utils.DingTalkUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.dynamic.datasource.annotation.DS;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;

import cn.iocoder.yudao.module.financial.service.outside.EasApiService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import cn.iocoder.yudao.module.financial.enums.SalesOutboundOrderSummaryStatusEnum;
import cn.iocoder.yudao.module.financial.dal.mysql.erp.CggSalePriceListMapper;
import cn.iocoder.yudao.module.financial.utils.DingTalkUtils;

/**
 * 销售出库单汇总服务实现类
 * 
 * 汇总规则说明：
 * 1. 按下字段进行分组汇总：
 *    - 组织信息：orgNumber(组织编码)、orgName(织名称)
 *    - 客户信息：ydDzdkhName(对账客户名称)
 *    - 开票信息：invschemeName(库存事务名称)、invschemeNumber(库存事务码)
 *    - 业务日期：biztime
 *    - 物信息：materialNumber(物料码)、materialName(物料名称)
 *    - 仓库信息：warehouseNumber(仓库编码)、warehouseName(仓库名称)
 *    - 税率信息：taxrate(税率)
 * 
 * 2. 汇总计算：
 *    - 数量(qty)：同一分组内的数量累加
 *    - 含税金额(amountandtax)：同一分组内的含税金额累加
 *    - 含税单价(taxPrice)：使用单条记录的含税单价
 * 
 * 3. 排除规则：
 *    - 根据排除物料配置(SalesOutboundOrderExcludeMaterialDO)过滤不需要汇总的物料
 *    - 匹配条件：组织编码、对账客户信息、物信息完全匹配时行排除
 * 
 * 4. 汇总后处理：
 *    - 生成汇总单号：格式为"SOBOrderSum + 年月日时分秒"
 *    - 更新原始单据的汇总状态
 *    - 记录汇总时间和汇总单号
 */
@Service
@Slf4j
@DS("bidb")
public class SalesOutboundOrderSummaryServiceImpl implements SalesOutboundOrderSummaryService {

    /**
     * 默认组织编码
     */
    private static final String DEFAULT_ORG_CODE = "012801";
    private static final String DEFAULT_ZH_ORG_CODE = "012806";

    /**
     * 品牌分界日期 - 2025年7月10日
     * 此日期之前（含2025年7月9日）：包含"Yep"品牌
     * 此日期及之后：排除"Yep"品牌
     */
    private static final LocalDate BRAND_SPLIT_DATE = LocalDate.of(2025, 7, 10);

    /**
     * 完整品牌列表（包含Yep）
     */
    private static final List<String> FULL_BRAND_LIST = Arrays.asList(
        "健安适", "健视佳", "汤臣倍健", "Yep", "倍儿健", "舒百宁"
    );

    /**
     * 排除Yep的品牌列表
     */
    private static final List<String> BRAND_LIST_WITHOUT_YEP = Arrays.asList(
        "健安适", "健视佳", "汤臣倍健", "倍儿健", "舒百宁"
    );

    @Resource
    private SalesOutboundOrderMapper salesOutboundOrderMapper;

    @Resource
    private SalesOutboundOrderEntryMapper salesOutboundOrderEntryMapper;

    @Resource
    private SalesOutboundOrderSummaryMapper salesOutboundOrderSummaryMapper;

    @Resource
    private SalesOutboundOrderSummaryEntryMapper salesOutboundOrderSummaryEntryMapper;

    @Resource
    private SalesOutboundOrderExcludeMaterialMapper salesOutboundOrderExcludeMaterialMapper;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private EasApiService easApiService;

    @Resource
    private CggSalePriceListMapper salePriceListMapper;

    @Resource
    private CggOnlineFormErpWarehouseInfoService cggOnlineFormErpWarehouseInfoService;

    @Resource
    private CggCustomerMappingMapper customerMappingMapper;

    @Resource
    private CggSalePriceListService cggSalePriceListService;

    @Resource
    private DingTalkUtils dingTalkUtils;

    /**
     * 汇总并保存销售出库单数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * 
     * 处理流程：
     * 1. 查询日期范围内的销售出库单数据
     * 2. 获取销售出库单明细数据
     * 3. 获取排除物料配置
     * 4. 按分组字段进行汇总
     * 5. 生成汇总单据
     * 6. 更新原始单据状态
     * 7. 更新排除物料标记
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void summarizeAndSave(LocalDate startDate, LocalDate endDate) {
        // 1. 智能查询汇总数据
        log.info("[summarizeAndSave][开始智能查询汇总数据] startDate: {}, endDate: {}, 品牌分界日期: {}", 
            startDate, endDate, BRAND_SPLIT_DATE);
        List<Map<String, Object>> summaryData = executeSmartBrandQuery(startDate, endDate);
        log.info("[summarizeAndSave][智能查询汇总数据完成] 汇总记录数: {}", summaryData.size());

        if (summaryData.isEmpty()) {
            log.info("[summarizeAndSave][无数据需要汇总]");
            return;
        }

        // 2. 按汇总维度分组
        Map<SummaryKey, List<Map<String, Object>>> summaryGroups = summaryData.stream()
                .collect(Collectors.groupingBy(entry -> new SummaryKey(entry)));

        log.info("[summarizeAndSave][分组完成] 分组数: {}", summaryGroups.size());

        // 3. 保存汇总数据
        for (Map.Entry<SummaryKey, List<Map<String, Object>>> entry : summaryGroups.entrySet()) {
            // 创建汇总主表记录
            SalesOutboundOrderSummaryDO summary = toSummaryDO(entry.getKey());
            // 使用重试机制保存汇总单据
            saveSummaryWithRetry(summary);

            // 获取该分组下的所有原始订单ID
            List<Long> orderIds = entry.getValue().stream()
                    .map(item -> ((Number) item.get("orderId")).longValue())
                    .distinct()
                    .collect(Collectors.toList());

            // 设置汇总明细
            List<SalesOutboundOrderSummaryEntryDO> groupEntries = entry.getValue().stream()
                    .map(item -> {
                        SalesOutboundOrderSummaryEntryDO summaryEntry = new SalesOutboundOrderSummaryEntryDO();
                        summaryEntry.setSummaryId(summary.getId());
                        summaryEntry.setMaterialNumber((String) item.get("materialNumber"));
                        summaryEntry.setMaterialName((String) item.get("materialName"));
                        summaryEntry.setWarehouseNumber((String) item.get("warehouseNumber"));
                        summaryEntry.setWarehouseName((String) item.get("warehouseName"));
                        summaryEntry.setTaxrate((BigDecimal) item.get("taxrate"));
                        summaryEntry.setQty((BigDecimal) item.get("qty"));
                        summaryEntry.setAmountandtax((BigDecimal) item.get("amountandtax"));
                        summaryEntry.setTaxPrice((BigDecimal) item.get("taxPrice"));
                        summaryEntry.setIsExcluded(0);
                        summaryEntry.setDeleted(false);
                        return summaryEntry;
                    })
                    .collect(Collectors.toList());

            // 批量保存汇总明细
            log.info("[summarizeAndSave][开始保存汇总明细] summaryId: {}, entryCount: {}", summary.getId(), groupEntries.size());
            try {
                salesOutboundOrderSummaryEntryMapper.insertBatch(groupEntries);
            } catch (Exception e) {
                log.error("[summarizeAndSave][保存汇总明细失败] summaryId: {}, error: {}", summary.getId(), e.getMessage(), e);
                throw e;
            }
            log.info("[summarizeAndSave][保存汇总明细成功] summaryId: {}", summary.getId());

            // 4. 更新原始订单的汇总状态前，先校验数据
            log.info("[summarizeAndSave][始校验汇总数据] orderIds: {}", orderIds);

            // 获取原始订单的总数量
            BigDecimal originalTotalQty = entry.getValue().stream()
                    .map(item -> (BigDecimal) item.get("qty"))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 获取汇总明细的总数量
            BigDecimal summaryTotalQty = groupEntries.stream()
                    .map(SalesOutboundOrderSummaryEntryDO::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 比较数量是否一致
            if (originalTotalQty.compareTo(summaryTotalQty) != 0) {
                String errorMsg = String.format("汇总数据校验失败！原始订单总数量[%s]与汇总明细总数量[%s]不一致", 
                        originalTotalQty, summaryTotalQty);
                log.error("[summarizeAndSave][{}] orderIds: {}, summaryId: {}", 
                        errorMsg, orderIds, summary.getId());
                throw new RuntimeException(errorMsg);
            }

            log.info("[summarizeAndSave][汇总数据校验通过] orderIds: {}, originalTotalQty: {}, summaryTotalQty: {}", 
                    orderIds, originalTotalQty, summaryTotalQty);

            // 4. 更新原始订单的汇总状态
            log.info("[summarizeAndSave][开始更新原始订单汇总状态] orderIds: {}", orderIds);
            for (Long orderId : orderIds) {
                salesOutboundOrderMapper.updateSummaryInfo(
                        orderId,
                        summary.getSummaryOrderNo(),
                        summary.getBiztime()
                );
            }
            log.info("[summarizeAndSave][更新原始订单汇总状态完成]");
        }
    }

    /**
     * 将销出库单转换为汇总单主表对象
     *
     * @return 汇总单主表对象
     */
    private SalesOutboundOrderSummaryDO toSummaryDO(SummaryKey key) {
        SalesOutboundOrderSummaryDO summary = new SalesOutboundOrderSummaryDO();
        summary.setOrgNumber(key.orgNumber);
        summary.setOrgName(key.orgName);
        summary.setYdDzdkhName(key.ydDzdkhName);
        summary.setInvschemeName(key.invschemeName);
        summary.setYdDzdkhNumber(key.ydDzdkhNumber);
        summary.setInvschemeNumber(key.invschemeNumber);
        summary.setBiztime(key.biztime);
        // 设置业务类型为经销商2C订单退单
        summary.setBusinessType(BusinessTypeEnum.DISTRIBUTOR_2C_RETURN.getType());
        return summary;
    }

    /**
     * 生成汇总单号
     * 格式: SOB + 年月日时分秒 + 2位随机数
     * 
     * @return 汇总单号
     */
    private String generateSummaryOrderNo() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        // 生成2位随机数
        int randomNum = (int) (Math.random() * 90) + 10; // 生成10-99之间的随机数
        return "SOB" + now.format(formatter) + randomNum;
    }

    /**
     * 用于汇总分组的键类
     */
    private static class SummaryKey {
        private final String orgNumber;
        private final String orgName;
        private final String ydDzdkhName;
        private final String invschemeName;
        private final String invschemeNumber;
        private final LocalDate biztime;
        private final String ydDzdkhNumber;

        public SummaryKey(Map<String, Object> entry) {
            this.orgNumber = (String) entry.get("orgNumber");
            this.orgName = (String) entry.get("orgName");
            this.ydDzdkhName = (String) entry.get("ydDzdkhName");
            this.invschemeName = (String) entry.get("invschemeName");
            this.invschemeNumber = (String) entry.get("invschemeNumber");
            
            // 处理日期类型转换
            Object biztimeObj = entry.get("biztime");
            if (biztimeObj instanceof LocalDateTime) {
                this.biztime = ((LocalDateTime) biztimeObj).toLocalDate();
            } else if (biztimeObj instanceof LocalDate) {
                this.biztime = (LocalDate) biztimeObj;
            } else {
                throw new IllegalArgumentException("Unexpected biztime type: " + (biztimeObj != null ? biztimeObj.getClass() : "null"));
            }

            this.ydDzdkhNumber = (String) entry.get("ydDzdkhNumber");
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SummaryKey that = (SummaryKey) o;
            return Objects.equals(orgNumber, that.orgNumber) &&
                    Objects.equals(ydDzdkhName, that.ydDzdkhName) &&
                    Objects.equals(invschemeName, that.invschemeName) &&
                    Objects.equals(invschemeNumber, that.invschemeNumber) &&
                    Objects.equals(orgName, that.orgName) &&
                    Objects.equals(biztime, that.biztime);
        }

        @Override
        public int hashCode() {
            return Objects.hash(orgNumber, ydDzdkhName, invschemeName, invschemeNumber, orgName, biztime);
        }
    }

    // 在保存汇总单据时添加重试逻辑
    private static final int MAX_RETRY_TIMES = 3; // 最大重试次数

    private void saveSummaryWithRetry(SalesOutboundOrderSummaryDO summary) {
        int retryCount = 0;
        while (retryCount < MAX_RETRY_TIMES) {
            try {
                summary.setSummaryOrderNo(generateSummaryOrderNo());
                salesOutboundOrderSummaryMapper.insert(summary);
                return; // 保存成功，直接返回
            } catch (DuplicateKeyException e) {
                retryCount++;
                if (retryCount >= MAX_RETRY_TIMES) {
                    log.error("[saveSummaryWithRetry][保存汇总单据失败，重试次数已达上限] summary: {}", summary);
                    throw e;
                }
                log.warn("[saveSummaryWithRetry][单号重复，准备第{}次重试] summaryOrderNo: {}", retryCount + 1, summary.getSummaryOrderNo());
            }
        }
    }

    /**
     * 更新汇总单的价格信息
     * 
     * @param summary 汇总单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSummaryPrices(SalesOutboundOrderSummaryDO summary) {
        // 1. 查询明细
        List<SalesOutboundOrderSummaryEntryDO> entries = salesOutboundOrderSummaryEntryMapper
                .selectListBySummaryId(summary.getId());
        if (entries.isEmpty()) {
            throw new RuntimeException("汇总单明细为空");
        }

        // 打印每条记录的is_excluded值
        for (SalesOutboundOrderSummaryEntryDO entry : entries) {
            log.info("[updateSummaryPrices][明细数据] entryId:{}, materialNumber:{}, isExcluded:{}", 
                entry.getId(), entry.getMaterialNumber(), entry.getIsExcluded());
        }

        // 1.1 过滤掉已排除的明细
        entries = filterExcludedEntries(entries, summary.getSummaryOrderNo());
        if (entries.isEmpty()) {
            throw new RuntimeException("过滤排除物料后汇总单明细为空");
        }

        // 2. 检查价格
        Map<String, String> missingPriceMaterials = new HashMap<>();
        List<SalesOutboundOrderSummaryEntryDO> updatedEntries = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (SalesOutboundOrderSummaryEntryDO entry : entries) {
//            if (entry.getId().equals(4027L)) {
//                log.info("[updateSummaryPrices][处理特定明细] entryId:{}", entry.getId());
//            }

            // 2.1 查询价格
            CggSalePriceListDO priceInfo = getSalePrice(
                summary.getOrgNumber(), 
                entry.getMaterialNumber(), 
                summary.getBiztime()
            );
            
            if (priceInfo == null) {
                missingPriceMaterials.put(entry.getMaterialNumber(), entry.getMaterialName());
            } else {
                // 更新明细的价格信息
                entry.setTaxPrice(priceInfo.getCfPrice());
                // 重新计算含税金额 = 数量 * 含税单价
                BigDecimal amountandtax = entry.getQty().multiply(priceInfo.getCfPrice());
                entry.setAmountandtax(amountandtax);
                updatedEntries.add(entry);
                totalAmount = totalAmount.add(amountandtax);
            }
        }
        
        // 3. 检查是否所有物料都有价格
        if (!missingPriceMaterials.isEmpty()) {
            // 构建错误信息,格式为: 物料编码(物料名称)
            String errorDetails = missingPriceMaterials.entrySet().stream()
                .map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining(","));
            String errorMsg = String.format("[%s]未找到有效的价格信息", errorDetails);
            log.warn("[updateSummaryPrices][{}] summaryOrderNo:{}", errorMsg, summary.getSummaryOrderNo());
            throw new RuntimeException(errorMsg);
        }

        // 4. 批量更新明细价格信息
        if (!updatedEntries.isEmpty()) {
            try {
                // 批量更新明细
                for (SalesOutboundOrderSummaryEntryDO entry : updatedEntries) {
                    salesOutboundOrderSummaryEntryMapper.updateById(entry);
                }
                
                // 更新主表总金额
                SalesOutboundOrderSummaryDO updateSummary = new SalesOutboundOrderSummaryDO();
                updateSummary.setId(summary.getId());
                updateSummary.setTotalAmount(totalAmount);
                salesOutboundOrderSummaryMapper.updateById(updateSummary);
                
                log.info("[updateSummaryPrices][更新价格成功] summaryOrderNo:{}, entryCount:{}, totalAmount:{}", 
                    summary.getSummaryOrderNo(), updatedEntries.size(), totalAmount);
            } catch (Exception e) {
                log.error("[updateSummaryPrices][更新价格失败] summaryOrderNo:" + summary.getSummaryOrderNo(), e);
                throw new RuntimeException("更新价格失败", e);
            }
        }
    }

    /**
     * 构建推送到EAS的数据
     *
     * @param summary 汇总单数据
     * @return JSON格式的请求数据
     */
    private String buildPushData(SalesOutboundOrderSummaryDO summary) {
        // 1. 重新查询汇总单,确保获取最新数据
        summary = salesOutboundOrderSummaryMapper.selectById(summary.getId());
        if (summary == null) {
            throw new RuntimeException("汇总单不存在");
        }
        log.info("[buildPushData][重新查询汇总单] summaryOrderNo:{}, erpCustomerCode:{}", 
            summary.getSummaryOrderNo(), summary.getErpCustomerCode());

        // 2. 查询明细
        List<SalesOutboundOrderSummaryEntryDO> entries = salesOutboundOrderSummaryEntryMapper
                .selectListBySummaryId(summary.getId());
        if (entries.isEmpty()) {
            throw new RuntimeException("汇总单明细为空");
        }

        // 2.1 过滤掉已排除的明细
        entries = filterExcludedEntries(entries, summary.getSummaryOrderNo());
        if (entries.isEmpty()) {
            throw new RuntimeException("过滤排除物料后，汇总单明细为空");
        }

        // 3. 构建请求数据
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("billNumber", summary.getSummaryOrderNo());
        // 当汇总单号等于SOB25031110384287时，使用DEFAULT_ORG_CODE
        if ("SOB25031110384287".equals(summary.getSummaryOrderNo()) 
            || "SOB25031116491866".equals(summary.getSummaryOrderNo())
            ) {
            requestMap.put("inventoryOrgCode", DEFAULT_ORG_CODE);
            log.info("[buildPushData][特殊单号使用默认组织编码] summaryOrderNo:{}, inventoryOrgCode:{}", 
                summary.getSummaryOrderNo(), DEFAULT_ORG_CODE);
        } else {
            requestMap.put("inventoryOrgCode", DEFAULT_ZH_ORG_CODE); //summary.getOrgNumber());
        }
        requestMap.put("customerCode", summary.getErpCustomerCode());
        requestMap.put("headerRemark", "经销商销售出库单汇总");
        requestMap.put("inventoryOrgName", summary.getOrgName());
        requestMap.put("customerName", summary.getYdDzdkhName());
        // 获取库存事务编码
        String invschemeNumber = summary.getInvschemeNumber();
        if (StringUtils.isEmpty(invschemeNumber)) {
            log.error("[buildPushData][汇总单库存事务编码为空] summaryOrderNo:{}", summary.getSummaryOrderNo());
            throw new RuntimeException("汇总单库存事务编码为空");
        }

        // 根据库存事务编码判断是否红字
        switch (invschemeNumber.toUpperCase()) {
            case "210":  // 普通销售出库
                requestMap.put("isRed", "0");
                break;
            case "2101": // 普通销售退、补、换货
                requestMap.put("isRed", "1"); 
                break;
            default:
                log.error("[buildPushData][未知的库存事务编码] summaryOrderNo:{}, invschemeNumber:{}", 
                    summary.getSummaryOrderNo(), invschemeNumber);
                throw new RuntimeException("未知的库存事务编码:" + invschemeNumber);
        }
        requestMap.put("bizDate", summary.getBiztime().format(DateTimeFormatter.ISO_DATE));

        // 4. 建明细数据
        List<Map<String, Object>> billBodies = new ArrayList<>();
        for (SalesOutboundOrderSummaryEntryDO entry : entries) {
            // 校验仓库编码
            String erpWarehouseCode = entry.getErpWarehouseCode();
            if (StringUtils.isEmpty(erpWarehouseCode)) {
                log.error("[buildPushData][仓库编码为空] summaryOrderNo:{}, entryId:{}, materialNumber:{}, warehouseName:{}", 
                    summary.getSummaryOrderNo(), entry.getId(), entry.getMaterialNumber(), entry.getWarehouseName());
                throw new RuntimeException("仓库编码不能为空");
            }
            
            String warehouseCode = convertWarehouseCode(erpWarehouseCode);
            if (StringUtils.isEmpty(warehouseCode)) {
                log.error("[buildPushData][转换后的仓库编码为空] summaryOrderNo:{}, entryId:{}, erpWarehouseCode:{}, warehouseName:{}", 
                    summary.getSummaryOrderNo(), entry.getId(), erpWarehouseCode, entry.getWarehouseName());
                throw new RuntimeException("转换后的仓库编码不能为空");
            }

            Map<String, Object> billBody = new HashMap<>();
            billBody.put("materialCode", entry.getMaterialNumber());
            billBody.put("warehouseCode", warehouseCode); // 确保非空
            billBody.put("quantity", entry.getQty().abs().toString());
            billBody.put("taxPrice", entry.getTaxPrice().abs().toString());
            billBody.put("taxRate", entry.getTaxrate().abs().toString());
            billBody.put("bodyRemark", "物料出库");
            billBody.put("materialName", entry.getMaterialName());
            billBody.put("warehouseName", entry.getWarehouseName());
            billBodies.add(billBody);
            
            log.info("[buildPushData][构建明细数据] summaryOrderNo:{}, entryId:{}, materialNumber:{}, warehouseCode:{}, warehouseName:{}", 
                summary.getSummaryOrderNo(), entry.getId(), entry.getMaterialNumber(), warehouseCode, entry.getWarehouseName());
        }

        // 打印完整的请求数据
        log.info("[buildPushData][完整请求数据] requestMap:{}", JSON.toJSONString(requestMap, true));

        requestMap.put("billBodies", billBodies);

        // 5. 转换为JSON字符串
        return JSON.toJSONString(requestMap);
    }

    /**
     * 推送销售出库单到EAS
     */
    @Override
    public boolean pushToEAS(String summaryOrderNo) {
        log.info("[pushToEAS][开始] summaryOrderNo:{}", summaryOrderNo);
//        summaryOrderNo = "SOB24120120273577";
        // 1. 查询需要推送的记录
        List<SalesOutboundOrderSummaryDO> summaryList;
        if (StringUtils.isNotEmpty(summaryOrderNo)) {
            // 按汇总单号查询
            summaryList = salesOutboundOrderSummaryMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderSummaryDO>()
                    .eq(SalesOutboundOrderSummaryDO::getSummaryOrderNo, summaryOrderNo)
                    .in(SalesOutboundOrderSummaryDO::getPushStatus, 
                        Arrays.asList(
                            SalesOutboundOrderSummaryStatusEnum.WAIT_PUSH.getStatus(),
                            SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus()
                        ))
            );
            if (CollUtil.isEmpty(summaryList)) {
                log.error("[pushToEAS][未找到待推送的汇总单] summaryOrderNo:{}", summaryOrderNo);
                return false;
            }
        } else {
            // 查询所有待推送记录
            summaryList = salesOutboundOrderSummaryMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderSummaryDO>()
                    .in(SalesOutboundOrderSummaryDO::getPushStatus,
                        Arrays.asList(
                            SalesOutboundOrderSummaryStatusEnum.WAIT_PUSH.getStatus(),
                            SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus()
                        ))
            );
            if (CollUtil.isEmpty(summaryList)) {
                log.info("[pushToEAS][没有需要推送的记录]");
                return true;
            }
        }

        // 1.1 同步销售价格
        try {
            log.info("[pushToEAS][开始同步销售价格]");
            cggSalePriceListService.syncSalePriceListFromEAS();
            log.info("[pushToEAS][同步销售价格完成]");
        } catch (Exception e) {
            log.error("[pushToEAS][同步销售价格失败] error:{}", e.getMessage(), e);
            throw new RuntimeException("同步销售价格失败:" + e.getMessage());
        }

        // 2. 遍历推送
        boolean allSuccess = true;
        for (SalesOutboundOrderSummaryDO summary : summaryList) {
            // 清空推送相关字段
            summary.setPushMessage(null);
            summary.setRequestBody(null);
            summary.setResponseBody(null);
            
            String requestJson = null;
            try {                
                // 2.1 获取排除物料并更新明细
               updateExcludeMaterialStatus(summary);

                // 2.2 更新价格信息
                updateSummaryPrices(summary);
                
                // 2.3 获取并更新ERP客户编码
                updateErpCustomerCode(summary);
                
                // 2.4 获取并更新ERP仓库编码
                updateErpWarehouseCode(summary);
                
                // 2.5 构建推送数据
                requestJson = buildPushData(summary);
                log.info("[pushToEAS][请求报文] summaryOrderNo:{}, requestJson:{}", summary.getSummaryOrderNo(), requestJson);
                
                // 2.6 调用EAS接口
                String response = easApiService.sendEasSaleOutStockApiOfYPH(requestJson);
                log.info("[pushToEAS][响应报文] summaryOrderNo:{}, response:{}", summary.getSummaryOrderNo(), response);
                
                // 2.7 解析响应结果并更新状态
                JSONObject responseObj = JSON.parseObject(response);
                if (responseObj == null) {
                    throw new RuntimeException("解析响应结果失败,响应为空");
                }

                // 修改字段名为success,增空值判断
                Boolean success = responseObj.getBoolean("success"); 
                if (success == null) {
                    log.error("[pushToEAS][响应success字段为空] summaryOrderNo:{}, response:{}", 
                        summary.getSummaryOrderNo(), response);
                    success = false;
                }

                String message = responseObj.getString("message");
                if (StringUtils.isEmpty(message)) {
                    message = "未知错误";
                }

                // 2.8 更新推送状态和报文
                SalesOutboundOrderSummaryDO updateResp = new SalesOutboundOrderSummaryDO();
                updateResp.setId(summary.getId());
                updateResp.setPushTime(LocalDateTime.now());
                updateResp.setRequestBody(requestJson);
                updateResp.setResponseBody(response);
                
                if (success) {
                    updateResp.setPushStatus(SalesOutboundOrderSummaryStatusEnum.PUSHED.getStatus());
                    updateResp.setPushMessage("推送成功");
                    salesOutboundOrderSummaryMapper.updateById(updateResp);
                    log.info("[pushToEAS][推送成功] summaryOrderNo:{}", summary.getSummaryOrderNo());
                } else {
                    updateResp.setPushStatus(SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus());
                    updateResp.setPushMessage(message);
                    salesOutboundOrderSummaryMapper.updateById(updateResp);
                    log.error("[pushToEAS][推送失败] summaryOrderNo:{}, message:{}", summary.getSummaryOrderNo(), message);
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("[pushToEAS][推送异常] summaryOrderNo:" + summary.getSummaryOrderNo(), e);
                SalesOutboundOrderSummaryDO updateError = new SalesOutboundOrderSummaryDO();
                updateError.setId(summary.getId());
                updateError.setPushStatus(SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus());
                updateError.setPushTime(LocalDateTime.now());
                updateError.setRequestBody(requestJson != null ? requestJson : "构建请求报文失败");
                updateError.setResponseBody(e.getMessage());
                updateError.setPushMessage(e.getMessage());
                salesOutboundOrderSummaryMapper.updateById(updateError);
                allSuccess = false;
            }
        }
        
        log.info("[pushToEAS][结束] allSuccess:{}", allSuccess);
        return allSuccess;
    }

    /**
     * 获取物料在指定日期的销售价格
     * 
     * @param orgNumber 组织编码（已废弃）
     * @param materialNumber 物料编码
     * @param bizDate 业务日期
     * @return 价格信息
     */
    private CggSalePriceListDO getSalePrice(String orgNumber, String materialNumber, LocalDate bizDate) {
        log.info("[getSalePrice][开始查询] materialNumber:{}, bizDate:{}", materialNumber, bizDate);
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<CggSalePriceListDO> queryWrapper = new LambdaQueryWrapper<CggSalePriceListDO>()
                .eq(CggSalePriceListDO::getCode, DEFAULT_ORG_CODE + ".XSJMB0002")
                .eq(CggSalePriceListDO::getMatNumber, materialNumber)
                .le(CggSalePriceListDO::getEffectiveDate, bizDate)
                .and(w -> w.ge(CggSalePriceListDO::getExpirationDate, bizDate)
                         .or()
                         .isNull(CggSalePriceListDO::getExpirationDate))
                .orderByDesc(CggSalePriceListDO::getEffectiveDate)
                .last("LIMIT 1");

            // 执行查询
            CggSalePriceListDO result = salePriceListMapper.selectOne(queryWrapper);

            // 记录查询结果
            if (result == null) {
                // 如果没找到价格,查询是否存在该物料的任何价格记录
                Long count = salePriceListMapper.selectCount(
                    new LambdaQueryWrapper<CggSalePriceListDO>()
                        .eq(CggSalePriceListDO::getCode, DEFAULT_ORG_CODE + ".XSJMB0002")
                        .eq(CggSalePriceListDO::getMatNumber, materialNumber)
                );
                
                if (count > 0) {
                    // 存在价记但不在有效期内
                    log.warn("[getSalePrice][物料存在价格记录但不在有效期] materialNumber:{}, bizDate:{}, totalRecords:{}", 
                        materialNumber, bizDate, count);
                } else {
                    // 完全没有价格记录
                    log.warn("[getSalePrice][物料不存在任何价格记录] materialNumber:{}, bizDate:{}", 
                        materialNumber, bizDate);
                }
            } else {
                log.info("[getSalePrice][查询成功] materialNumber:{}, bizDate:{}, price:{}, effectiveDate:{}, expirationDate:{}", 
                    materialNumber, bizDate, result.getCfPrice(), 
                    result.getEffectiveDate(), result.getExpirationDate());
            }
            
            return result;
        } catch (Exception e) {
            log.error("[getSalePrice][查询异常] materialNumber:{}, bizDate:{}, error:{}", 
                materialNumber, bizDate, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 调用EAS创建形态转换单
     */
    @Override
    public boolean createEasFormTransform(String billNo, String easSrcNumber, String easTargetNumber, 
            String storageOrgUnitNumber, List<Map<String, Object>> inventoryList) {
        // 1. 构建请求参数
        Map<String, Object> requestMap = new HashMap<>();
        // 生成形态转换单号: FTB + 年月日时分秒 + 2位随机数
        String formTransformBillNo = generateFormTransformBillNo();
        requestMap.put("billNo", formTransformBillNo);
        requestMap.put("easSrcNumber", easSrcNumber);
        requestMap.put("easTargetNumber", easTargetNumber);
        requestMap.put("storageOrgUnitNumber", storageOrgUnitNumber);
        requestMap.put("inventory", inventoryList);

        // 2. 转换为JSON
        String requestJson = JSON.toJSONString(requestMap);
        log.info("[createEasFormTransform][开始调用EAS接口] 请求参数:{}", requestJson);

        try {
            // 3. 调用EAS接口
            String response = easApiService.sendEasFormTransformApi(requestJson);
            log.info("[createEasFormTransform][调用EAS接口完成] 应结果:{}", response);

            // 4. 解析响应结果
            JSONObject responseObj = JSON.parseObject(response);
            boolean success = responseObj.getBoolean("success");
            String message = responseObj.getString("message");

            if (!success) {
                log.error("[createEasFormTransform][创建形态转换单失败] billNo:{}, message:{}", formTransformBillNo, message);
            }
            return success;

        } catch (Exception e) {
            log.error("[createEasFormTransform][调用异常] billNo:" + formTransformBillNo, e);
            throw new RuntimeException("创建形态转换单失败:" + e.getMessage());
        }
    }

    /**
     * 生成形态转换单号
     * 格式: FTB + 年月日时分秒 + 2位随机数
     * 
     * @return 形态转换单号
     */
    private String generateFormTransformBillNo() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        // 生成2位随机数
        int randomNum = (int) (Math.random() * 90) + 10; // 生成10-99之间的随机数
        return "FTB" + now.format(formatter) + randomNum;
    }

    /**
     * 构建库存明细
     *
     * @param warehouseId 库ID
     * @param quantity 量
     * @return 库存明细
     */
    private Map<String, Object> buildInventoryDetail(String warehouseId, BigDecimal quantity) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("warehouseId", warehouseId);
        detail.put("quantity", quantity);
        return detail;
    }

    /**
     * 获取并更新ERP仓库编码
     *
     * @param summary 汇总单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateErpWarehouseCode(SalesOutboundOrderSummaryDO summary) {
        log.info("[updateErpWarehouseCode]始更新明细ERP仓库编码 - summaryOrderNo:{}", summary.getSummaryOrderNo());

        try {
            // 1. 获取汇总单明细
            List<SalesOutboundOrderSummaryEntryDO> entries = salesOutboundOrderSummaryEntryMapper
                    .selectListBySummaryId(summary.getId());
            if (entries.isEmpty()) {
                log.warn("[updateErpWarehouseCode]汇总单明细为空 - summaryOrderNo:{}", summary.getSummaryOrderNo());
                return;
            }

            // 1.1 过滤掉已排除的明细
            entries = filterExcludedEntries(entries, summary.getSummaryOrderNo());
            if (entries.isEmpty()) {
                return;
            }

            // 2. 遍历更新每条明细的ERP仓库编码
            for (SalesOutboundOrderSummaryEntryDO entry : entries) {
                // 获取ERP仓库编码
                String erpWarehouseCode = cggOnlineFormErpWarehouseInfoService.getErpWarehouseCode(
                        DEFAULT_ORG_CODE,
                    entry.getWarehouseNumber()
                );
                
                // 校验ERP仓库编码
                if (StringUtils.isEmpty(erpWarehouseCode)) {
                    String errorMsg = String.format("未找到对应的ERP仓库编码 - 组织编码:%s, OMS仓库编码:%s", 
                        summary.getOrgNumber(), entry.getWarehouseNumber());
                    log.error("[updateErpWarehouseCode][{}] summaryOrderNo:{}, entryId:{}", 
                        errorMsg, summary.getSummaryOrderNo(), entry.getId());
                    throw new RuntimeException(errorMsg);
                }
                
                // 更新明细的ERP仓库编码
                entry.setErpWarehouseCode(erpWarehouseCode);
                salesOutboundOrderSummaryEntryMapper.updateById(entry);
                
                // 验证更新结果
                SalesOutboundOrderSummaryEntryDO updated = salesOutboundOrderSummaryEntryMapper.selectById(entry.getId());
                log.info("[updateErpWarehouseCode]更新后验证 - entryId:{}, erpWarehouseCode:{}, dbErpWarehouseCode:{}", 
                    entry.getId(), erpWarehouseCode, updated.getErpWarehouseCode());
                
                log.info("[updateErpWarehouseCode]更新明细成功 - summaryOrderNo:{}, entryId:{}, warehouseNumber:{}, erpWarehouseCode:{}", 
                    summary.getSummaryOrderNo(), entry.getId(), entry.getWarehouseNumber(), erpWarehouseCode);
            }
            
        } catch (Exception e) {
            log.error("[updateErpWarehouseCode]处理异常 - summaryOrderNo:{}, error:{}", 
                summary.getSummaryOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取并更新ERP客户编码
     *
     * @param summary 汇总单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateErpCustomerCode(SalesOutboundOrderSummaryDO summary) {
        log.info("[updateErpCustomerCode]开始获取ERP客户编码 - summaryOrderNo:{}", summary.getSummaryOrderNo());
        
        try {
            // 1. 获取OMS客户编码
            String omsCustomerCode = summary.getYdDzdkhNumber();

            // 2. 查询客户映射
            CggCustomerMappingDO customerMapping = customerMappingMapper.selectByOrgAndOmsCustomer(
                DEFAULT_ORG_CODE, omsCustomerCode);
            
            // 3. 校验并更新ERP客户编码
            if (customerMapping == null) {
                String errorMsg = String.format("未找到对应的ERP客户编码 - 组织编码:%s, OMS客编码:%s",
                    DEFAULT_ORG_CODE, omsCustomerCode);
                log.error("[updateErpCustomerCode][{}] summaryOrderNo:{}", errorMsg, summary.getSummaryOrderNo());
                throw new RuntimeException(errorMsg);
            }
            
            // 4. 更新汇总单的ERP客户编码
            summary.setErpCustomerCode(customerMapping.getErpCustomerCode());
            salesOutboundOrderSummaryMapper.updateById(summary);
            log.info("[updateErpCustomerCode]更新成功 - summaryOrderNo:{}, erpCustomerCode:{}", 
                summary.getSummaryOrderNo(), customerMapping.getErpCustomerCode());
            
        } catch (Exception e) {
            log.error("[updateErpCustomerCode]处理异常 - summaryOrderNo:{}, error:{}", 
                summary.getSummaryOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取排除物料并更新明细状态
     *
     * @param summary 汇总单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateExcludeMaterialStatus(SalesOutboundOrderSummaryDO summary) {
        log.info("[updateExcludeMaterialStatus]开始更新明细排除状态 - summaryOrderNo:{}", summary.getSummaryOrderNo());

        try {
            // 1. 获取汇总单明细
            List<SalesOutboundOrderSummaryEntryDO> entries = salesOutboundOrderSummaryEntryMapper
                    .selectListBySummaryId(summary.getId());
            if (entries.isEmpty()) {
                log.warn("[updateExcludeMaterialStatus]总单明细为空 - summaryOrderNo:{}", summary.getSummaryOrderNo());
                return;
            }

            // 2. 获取排除物料配置
            List<SalesOutboundOrderExcludeMaterialDO> excludeMaterials = salesOutboundOrderExcludeMaterialMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderExcludeMaterialDO>()
                    .eq(SalesOutboundOrderExcludeMaterialDO::getPlatform, "麦优经销商一盘货")
                    .in(SalesOutboundOrderExcludeMaterialDO::getMaterialNumber, 
                        entries.stream()
                            .map(SalesOutboundOrderSummaryEntryDO::getMaterialNumber)
                            .collect(Collectors.toList()))
            );
            
            if (excludeMaterials.isEmpty()) {
                log.info("[updateExcludeMaterialStatus]无排除物料配置 - summaryOrderNo:{}, platform:{}", 
                    summary.getSummaryOrderNo(), summary.getInvschemeNumber());
                return;
            }

            // 3. 遍历更新每条明细的排除状态
            for (SalesOutboundOrderSummaryEntryDO entry : entries) {
                // 检查是否为排除物料
                boolean isExcluded = excludeMaterials.stream()
                    .anyMatch(exclude -> exclude.getMaterialNumber().equals(entry.getMaterialNumber()));
                
                if (isExcluded) {
                    // 更新明细的排除状态
                    entry.setIsExcluded(1);
                    salesOutboundOrderSummaryEntryMapper.updateById(entry);
                    
                    log.info("[updateExcludeMaterialStatus]更新明细排除状态成功 - summaryOrderNo:{}, entryId:{}, materialNumber:{}", 
                        summary.getSummaryOrderNo(), entry.getId(), entry.getMaterialNumber());
                }
            }
            
        } catch (Exception e) {
            log.error("[updateExcludeMaterialStatus]处理异常 - summaryOrderNo:{}, error:{}", 
                summary.getSummaryOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 过滤掉已排除的明细
     */
    private List<SalesOutboundOrderSummaryEntryDO> filterExcludedEntries(List<SalesOutboundOrderSummaryEntryDO> entries, String summaryOrderNo) {
        // 过滤掉已排除的明细
        List<SalesOutboundOrderSummaryEntryDO> filteredEntries = entries.stream()
                .filter(entry -> {
                    Integer isExcluded = entry.getIsExcluded();
                    log.info("[filterExcludedEntries][查排除状态] entryId:{}, materialNumber:{}, isExcluded:{}", 
                        entry.getId(), entry.getMaterialNumber(), isExcluded);
                    // 0或null表示不排除，1表示排除
                    return isExcluded == null || isExcluded == 0;
                })
                .collect(Collectors.toList());
        
        if (filteredEntries.isEmpty()) {
            log.info("[filterExcludedEntries]过滤排除物料后，汇总单细为空 - summaryOrderNo:{}, totalEntries:{}", 
                summaryOrderNo, entries.size());
        } else {
            log.info("[filterExcludedEntries]过滤排除物料完成 - summaryOrderNo:{}, beforeFilter:{}, afterFilter:{}", 
                summaryOrderNo, entries.size(), filteredEntries.size());
        }
        
        return filteredEntries;
    }

    /**
     * 判断是否被排除
     *
     * @param entry 明细条目
     * @return 是否被排除
     */
    private boolean isExcluded(SalesOutboundOrderSummaryEntryDO entry) {
        return entry != null && entry.getIsExcluded() != null && entry.getIsExcluded() == 1;
    }

    /**
     * 设置排除状态
     *
     * @param entry 明细条目
     * @param excluded 是否排除
     */
    private void setExcluded(SalesOutboundOrderSummaryEntryDO entry, boolean excluded) {
        if (entry != null) {
            entry.setIsExcluded(excluded ? 1 : 0);
        }
    }

    // /**
    //  * 更新排除状态
    //  *
    //  * @param entries 明细列表
    //  */
    // @Override
    // public void updateExcludeMaterialStatus(List<SalesOutboundOrderSummaryEntryDO> entries) {
    //     if (CollUtil.isEmpty(entries)) {
    //         return;
    //     }
    //     for (SalesOutboundOrderSummaryEntryDO entry : entries) {
    //         entry.setIsExcluded(1); // 设置为排除
    //     }
    // }

    /**
     * 转换仓库编码
     * 将012801开头的编码转换为012806开头
     */
    private String convertWarehouseCode(String warehouseCode) {
        if (StringUtils.isEmpty(warehouseCode)) {
            log.error("[convertWarehouseCode]仓库编码为空");
            return null;
        }
        
        // 检查是否以012801开头
        if (warehouseCode.startsWith("012801")) {
            int dotIndex = warehouseCode.indexOf('.');
            if (dotIndex > 0) {
                String newCode = "012806" + warehouseCode.substring(dotIndex);
                log.info("[convertWarehouseCode]转换仓库编码 - before:{}, after:{}", warehouseCode, newCode);
                return newCode;
            }
        }
        
        // 如果不是以012801开头,检是否需要添加012806前缀
        if (!warehouseCode.startsWith("012806")) {
            String newCode = "012806." + warehouseCode;
            log.info("[convertWarehouseCode]添加前缀 - before:{}, after:{}", warehouseCode, newCode);
            return newCode;
        }
        
        log.info("[convertWarehouseCode]仓库编码无需转换 - code:{}", warehouseCode);
        return warehouseCode;
    }

    @Override
    public void notifyPushFailedRecords() {
        log.info("[notifyPushFailedRecords][开始查询推送失败记录]");
        
        try {
            // 1. 获取本月和上月的时间范围
            LocalDate now = LocalDate.now();
            LocalDate currentMonthStart = now.withDayOfMonth(1);
            LocalDate lastMonthStart = currentMonthStart.minusMonths(1);
            
            // 2. 查询本月和上月的推送状态统计
            List<SalesOutboundOrderSummaryDO> currentMonthRecords = salesOutboundOrderSummaryMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderSummaryDO>()
                    .ge(SalesOutboundOrderSummaryDO::getBiztime, currentMonthStart)
                    .le(SalesOutboundOrderSummaryDO::getBiztime, now)
                    .in(SalesOutboundOrderSummaryDO::getPushStatus, 
                        Arrays.asList(
                            SalesOutboundOrderSummaryStatusEnum.PUSHED.getStatus(),
                            SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus()
                        ))
            );
            
            List<SalesOutboundOrderSummaryDO> lastMonthRecords = salesOutboundOrderSummaryMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderSummaryDO>()
                    .ge(SalesOutboundOrderSummaryDO::getBiztime, lastMonthStart)
                    .lt(SalesOutboundOrderSummaryDO::getBiztime, currentMonthStart)
                    .in(SalesOutboundOrderSummaryDO::getPushStatus, 
                        Arrays.asList(
                            SalesOutboundOrderSummaryStatusEnum.PUSHED.getStatus(),
                            SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus()
                        ))
            );
            
            // 3. 统计本月数据
            long currentSuccess = currentMonthRecords.stream()
                .filter(r -> r.getPushStatus().equals(SalesOutboundOrderSummaryStatusEnum.PUSHED.getStatus()))
                .count();
            long currentFail = currentMonthRecords.stream()
                .filter(r -> r.getPushStatus().equals(SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus()))
                .count();
            
            // 4. 统计上月数据
            long lastSuccess = lastMonthRecords.stream()
                .filter(r -> r.getPushStatus().equals(SalesOutboundOrderSummaryStatusEnum.PUSHED.getStatus()))
                .count();
            long lastFail = lastMonthRecords.stream()
                .filter(r -> r.getPushStatus().equals(SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus()))
                .count();
            
            // 5. 查询最近的推送失败记录
            List<SalesOutboundOrderSummaryDO> recentFailedRecords = salesOutboundOrderSummaryMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderSummaryDO>()
                    .eq(SalesOutboundOrderSummaryDO::getPushStatus, 
                        SalesOutboundOrderSummaryStatusEnum.PUSH_FAIL.getStatus())
                    .ge(SalesOutboundOrderSummaryDO::getBiztime, lastMonthStart)
                    .orderByDesc(SalesOutboundOrderSummaryDO::getPushTime)
                    .last("LIMIT 100")
            );

            if (CollUtil.isEmpty(recentFailedRecords)) {
                log.info("[notifyPushFailedRecords][无推送失败记录]");
                // 即使没有失败记录也发送统计信息
                sendStatisticsNotification(currentMonthStart, now, lastMonthStart, 
                    currentSuccess, currentFail, lastSuccess, lastFail);
                return;
            }

            // 在遍历前，按业务日期升序排序
            recentFailedRecords.sort(Comparator.comparing(SalesOutboundOrderSummaryDO::getBiztime));

            log.info("[notifyPushFailedRecords][查询到{}条推送失败记录]", recentFailedRecords.size());

            // 遍历发送钉钉通知
            int successCount = 0;
            int failCount = 0;
            for (SalesOutboundOrderSummaryDO record : recentFailedRecords) {
                try {
                    // 构建消息内容
                    StringBuilder message = new StringBuilder();
                    // 添加月度统计信息
                    message.append("# 销售出库单推送统计\n\n");
                    
                    // 本月统计
                    message.append("## 本月统计\n")
                        .append("**统计周期：** ").append(currentMonthStart.format(DateTimeFormatter.ISO_DATE))
                        .append(" ~ ").append(now.format(DateTimeFormatter.ISO_DATE)).append("\n\n")
                        .append("**推送成功：** ").append(currentSuccess).append(" 单\n\n")
                        .append("**推送失败：** ").append(currentFail).append(" 单\n\n")
                        .append("**成功率：** ").append(String.format("%.2f", 
                            (currentSuccess + currentFail) > 0 ? 
                            (currentSuccess * 100.0 / (currentSuccess + currentFail)) : 0))
                        .append("%\n\n");
                    
                    // 上月统计    
                    message.append("## 上月统计\n")
                        .append("**统计周期：** ").append(lastMonthStart.format(DateTimeFormatter.ISO_DATE))
                        .append(" ~ ").append(lastMonthStart.plusMonths(1).minusDays(1).format(DateTimeFormatter.ISO_DATE)).append("\n\n")
                        .append("**推送成功：** ").append(lastSuccess).append(" 单\n\n")
                        .append("**推送失败：** ").append(lastFail).append(" 单\n\n")
                        .append("**成功率：** ").append(String.format("%.2f", 
                            (lastSuccess + lastFail) > 0 ? 
                            (lastSuccess * 100.0 / (lastSuccess + lastFail)) : 0))
                        .append("%\n\n");
                    
                    // 添加失败记录详情
                    message.append("## 推送失败详情\n\n")
                        .append("**汇总单号：** ").append(record.getSummaryOrderNo()).append("\n\n")
                        .append("**客户名称：** ").append(record.getYdDzdkhName()).append("\n\n")
                        .append("**业务日期：** ").append(record.getBiztime().format(DateTimeFormatter.ISO_DATE)).append("\n\n")
                        .append("**推送时间：** ").append(record.getPushTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n")
                        .append("**失败原因：** ").append(record.getPushMessage());

                    // 发送通知
                    dingTalkUtils.sendMarkdownMessage(
                        "经销商销出推送EAS失败通知", 
                        message.toString(),
                        "sales-outbound"
                    );
                    
                    successCount++;
                    log.info("[notifyPushFailedRecords][发送钉钉通知成功] summaryOrderNo:{}", 
                        record.getSummaryOrderNo());
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("[notifyPushFailedRecords][发送钉钉通知异常] summaryOrderNo:" + 
                        record.getSummaryOrderNo(), e);
                }
            }
            
            // 7. 记录处理结果
            log.info("[notifyPushFailedRecords][处理完成] 总数:{}, 成功:{}, 失败:{}", 
                recentFailedRecords.size(), successCount, failCount);
            
        } catch (Exception e) {
            log.error("[notifyPushFailedRecords][处理异常]", e);
            throw new RuntimeException("处理推送失败记录异常", e);
        }
    }

    /**
     * 构建钉钉通知消息
     */
    private String buildDingTalkMessage(SalesOutboundOrderSummaryDO record) {
        return new StringBuilder()
            .append("### 销售出库单推送EAS失败\n\n")
            .append("**汇总单号:** ").append(record.getSummaryOrderNo()).append("\n\n")
            .append("**业务日期:** ").append(record.getBiztime().format(DateTimeFormatter.ISO_DATE)).append("\n\n")
            .append("**客户名称:** ").append(record.getYdDzdkhName()).append("\n\n")
            .append("**事务名称:** ").append(record.getInvschemeName()).append("\n\n")
            .append("**失败原因:** ").append(record.getPushMessage()).append("\n\n")
            .append("**推送时间:** ").append(record.getPushTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .toString();
    }

    /**
     * 查询排除物料信息并推送到钉钉
     */
    @Override
    public void notifyExcludeMaterials() {
        log.info("[notifyExcludeMaterials][开始查询排除物料信息]");
        
        try {
            // 1. 查询排除物料信息
            List<SalesOutboundOrderExcludeMaterialDO> excludeMaterials = salesOutboundOrderExcludeMaterialMapper.selectList(
                new LambdaQueryWrapper<SalesOutboundOrderExcludeMaterialDO>()
                    .select(SalesOutboundOrderExcludeMaterialDO::getMaterialNumber,
                           SalesOutboundOrderExcludeMaterialDO::getMaterialName)
                    .eq(SalesOutboundOrderExcludeMaterialDO::getDeleted, false)
                    .orderByDesc(SalesOutboundOrderExcludeMaterialDO::getCreateTime)
            );

            if (CollUtil.isEmpty(excludeMaterials)) {
                log.info("[notifyExcludeMaterials][无排除物料数据]");
                return;
            }

            log.info("[notifyExcludeMaterials][查询到{}条排除物料记录]", excludeMaterials.size());

            // 2. 构建消息内容
            StringBuilder message = new StringBuilder();
            message.append("# 经销商一盘货排除物料清单\n\n");
            message.append("| 序号 | 物料编码 | 物料名称 |\n");
            message.append("| --- | --- | --- |\n");

            // 3. 添加物料信息
            int index = 1;
            for (SalesOutboundOrderExcludeMaterialDO material : excludeMaterials) {
                message.append(String.format("| %d | %s | %s |\n",
                    index++,
                    material.getMaterialNumber(),
                    material.getMaterialName()
                ));
            }

            // 4. 添加统计信息
            message.append(String.format("\n\n**总计：** %d 个排除物料", excludeMaterials.size()));

            // 5. 发送钉钉通知
            dingTalkUtils.sendMarkdownMessage(
                "销售出库单排除物料清单", 
                message.toString(),
                "sales-outbound"
            );
            
            log.info("[notifyExcludeMaterials][推送排除物料清单成功] totalCount:{}", excludeMaterials.size());
            
        } catch (Exception e) {
            log.error("[notifyExcludeMaterials][推送排除物料清单异常]", e);
            throw new RuntimeException("推送排除物料清单异常", e);
        }
    }

    /**
     * 新增发送统计信息的方法
     */
    private void sendStatisticsNotification(LocalDate currentMonthStart, LocalDate now, 
        LocalDate lastMonthStart, long currentSuccess, long currentFail, 
        long lastSuccess, long lastFail) {
        try {
            // 构建消息内容
            StringBuilder message = new StringBuilder();
            message.append("# 销售出库单推送统计\n\n");
            
            // 本月统计
            message.append("## 本月统计\n")
                .append("**统计周期：** ").append(currentMonthStart.format(DateTimeFormatter.ISO_DATE))
                .append(" ~ ").append(now.format(DateTimeFormatter.ISO_DATE)).append("\n\n")
                .append("**推送成功：** ").append(currentSuccess).append(" 单\n\n")
                .append("**推送失败：** ").append(currentFail).append(" 单\n\n")
                .append("**成功率：** ").append(String.format("%.2f", 
                    (currentSuccess + currentFail) > 0 ? 
                    (currentSuccess * 100.0 / (currentSuccess + currentFail)) : 100))
                .append("%\n\n");
            
            // 上月统计    
            message.append("## 上月统计\n")
                .append("**统计周期：** ").append(lastMonthStart.format(DateTimeFormatter.ISO_DATE))
                .append(" ~ ").append(lastMonthStart.plusMonths(1).minusDays(1).format(DateTimeFormatter.ISO_DATE)).append("\n\n")
                .append("**推送成功：** ").append(lastSuccess).append(" 单\n\n")
                .append("**推送失败：** ").append(lastFail).append(" 单\n\n")
                .append("**成功率：** ").append(String.format("%.2f", 
                    (lastSuccess + lastFail) > 0 ? 
                    (lastSuccess * 100.0 / (lastSuccess + lastFail)) : 100))
                .append("%\n\n");

            // 如果没有失败记录,添加说明
            message.append("## 推送失败详情\n\n")
                .append("当前无推送失败记录");

            // 发送通知
            dingTalkUtils.sendMarkdownMessage(
                "销售出库单推送EAS统计通知", 
                message.toString(),
                "sales-outbound"
            );
            
            log.info("[sendStatisticsNotification][发送统计通知成功] currentSuccess:{}, currentFail:{}, lastSuccess:{}, lastFail:{}", 
                currentSuccess, currentFail, lastSuccess, lastFail);
            
        } catch (Exception e) {
            log.error("[sendStatisticsNotification][发送统计通知异常]", e);
            throw new RuntimeException("发送统计通知异常", e);
        }
    }

    /**
     * 查询类型枚举
     */
    private enum QueryType {
        BEFORE_SPLIT,  // 完全在分界日期之前
        AFTER_SPLIT,   // 完全在分界日期之后
        CROSS_SPLIT    // 跨越分界日期
    }

    /**
     * 判断查询日期范围的类型
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 查询类型枚举
     */
    private QueryType determineQueryType(LocalDate startDate, LocalDate endDate) {
        if (endDate.isBefore(BRAND_SPLIT_DATE)) {
            return QueryType.BEFORE_SPLIT;
        } else if (startDate.isAfter(BRAND_SPLIT_DATE) || startDate.equals(BRAND_SPLIT_DATE)) {
            return QueryType.AFTER_SPLIT;
        } else {
            return QueryType.CROSS_SPLIT;
        }
    }

    /**
     * 执行智能品牌查询
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 汇总数据列表
     */
    List<Map<String, Object>> executeSmartBrandQuery(LocalDate startDate, LocalDate endDate) {
        // 参数校验
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        QueryType queryType = determineQueryType(startDate, endDate);
        
        switch (queryType) {
            case BEFORE_SPLIT:
                log.info("[executeSmartBrandQuery][单次查询-分界前] startDate: {}, endDate: {}, 品牌列表: {}", 
                    startDate, endDate, FULL_BRAND_LIST);
                return salesOutboundOrderEntryMapper.selectSummaryEntries(startDate, endDate, FULL_BRAND_LIST);
                
            case AFTER_SPLIT:
                log.info("[executeSmartBrandQuery][单次查询-分界后] startDate: {}, endDate: {}, 品牌列表: {}", 
                    startDate, endDate, BRAND_LIST_WITHOUT_YEP);
                return salesOutboundOrderEntryMapper.selectSummaryEntries(startDate, endDate, BRAND_LIST_WITHOUT_YEP);
                
            case CROSS_SPLIT:
                return executeCrossSplitQuery(startDate, endDate);
                
            default:
                throw new IllegalArgumentException("未知的查询类型: " + queryType);
        }
    }

    /**
     * 执行跨分界日期查询
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 合并后的汇总数据列表
     */
    private List<Map<String, Object>> executeCrossSplitQuery(LocalDate startDate, LocalDate endDate) {
        LocalDate splitEndDate = BRAND_SPLIT_DATE.minusDays(1); // 2025-07-09
        
        log.info("[executeCrossSplitQuery][跨界查询开始] startDate: {}, endDate: {}, 分界点: {}", 
            startDate, endDate, BRAND_SPLIT_DATE);
        
        // 第一次查询：startDate 到 2025-07-09，使用完整品牌列表
        log.info("[executeCrossSplitQuery][第一次查询] startDate: {}, endDate: {}, 品牌列表: {}", 
            startDate, splitEndDate, FULL_BRAND_LIST);
        List<Map<String, Object>> beforeSplitData = salesOutboundOrderEntryMapper
            .selectSummaryEntries(startDate, splitEndDate, FULL_BRAND_LIST);
        
        // 第二次查询：2025-07-10 到 endDate，使用排除Yep的品牌列表
        log.info("[executeCrossSplitQuery][第二次查询] startDate: {}, endDate: {}, 品牌列表: {}", 
            BRAND_SPLIT_DATE, endDate, BRAND_LIST_WITHOUT_YEP);
        List<Map<String, Object>> afterSplitData = salesOutboundOrderEntryMapper
            .selectSummaryEntries(BRAND_SPLIT_DATE, endDate, BRAND_LIST_WITHOUT_YEP);
        
        // 合并查询结果
        List<Map<String, Object>> mergedData = new ArrayList<>();
        mergedData.addAll(beforeSplitData);
        mergedData.addAll(afterSplitData);
        
        log.info("[executeCrossSplitQuery][查询结果合并] 第一次查询记录数: {}, 第二次查询记录数: {}, 合并后总记录数: {}", 
            beforeSplitData.size(), afterSplitData.size(), mergedData.size());
        
        return mergedData;
    }

    /**
     * 根据日期获取对应的品牌列表
     * 
     * @param date 查询日期
     * @return 品牌列表
     */
    private List<String> getBrandListByDate(LocalDate date) {
        if (date.isBefore(BRAND_SPLIT_DATE)) {
            return FULL_BRAND_LIST;
        } else {
            return BRAND_LIST_WITHOUT_YEP;
        }
    }
}

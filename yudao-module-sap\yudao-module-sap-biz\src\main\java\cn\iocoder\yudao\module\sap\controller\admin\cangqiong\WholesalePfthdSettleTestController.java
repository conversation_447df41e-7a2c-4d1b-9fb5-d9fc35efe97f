package cn.iocoder.yudao.module.sap.controller.admin.cangqiong;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.WholesalePfthdSettleParamsDTO;
import cn.iocoder.yudao.module.sap.service.cangqiong.E3WholesalePfthdSettleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 批发退货单结算测试控制器
 * 用于测试批发退货单结算的三个阶段：主表结算、OMS明细结算、拆单明细结算
 */
@Tag(name = "管理后台 - 批发退货单结算测试")
@RestController
@RequestMapping("/sap/wholesale-pfthd-settle-test")
@Validated
@Slf4j
public class WholesalePfthdSettleTestController {

    @Resource
    private E3WholesalePfthdSettleService e3WholesalePfthdSettleService;

    /**
     * 测试主表结算
     * 处理结算状态为1（MAIN_PENDING_SETTLE）的批发退货单
     */
    @PostMapping("/main-settle")
    @Operation(summary = "测试主表结算", description = "测试批发退货单主表结算功能，处理结算状态为1的批发退货单")
    @PermitAll
    public CommonResult<String> testMainSettle(@RequestBody @Valid WholesalePfthdSettleParamsDTO params) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[testMainSettle][开始测试主表结算，参数：{}]", params);
            
            // 调用主表结算服务
            e3WholesalePfthdSettleService.doMainSettle(params);
            
            long endTime = System.currentTimeMillis();
            String successMsg = String.format("主表结算测试执行成功，耗时：%dms", endTime - startTime);
            log.info("[testMainSettle][{}]", successMsg);
            
            return success(successMsg);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            String errorMsg = String.format("主表结算测试执行失败，耗时：%dms，错误：%s", endTime - startTime, e.getMessage());
            log.error("[testMainSettle][{}]", errorMsg, e);
            
            return success(errorMsg);
        }
    }

    /**
     * 测试OMS明细结算
     * 处理结算状态为2（OMS_DETAIL_PENDING_SETTLE）的批发退货单
     */
    @PostMapping("/oms-detail-settle")
    @Operation(summary = "测试OMS明细结算", description = "测试批发退货单OMS明细结算功能，处理结算状态为2的批发退货单")
    @PermitAll
    public CommonResult<String> testOMSDetailSettle(@RequestBody @Valid WholesalePfthdSettleParamsDTO params) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[testOMSDetailSettle][开始测试OMS明细结算，参数：{}]", params);
            
            // 调用OMS明细结算服务
            e3WholesalePfthdSettleService.doOMSDetailSettle(params);
            
            long endTime = System.currentTimeMillis();
            String successMsg = String.format("OMS明细结算测试执行成功，耗时：%dms", endTime - startTime);
            log.info("[testOMSDetailSettle][{}]", successMsg);
            
            return success(successMsg);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            String errorMsg = String.format("OMS明细结算测试执行失败，耗时：%dms，错误：%s", endTime - startTime, e.getMessage());
            log.error("[testOMSDetailSettle][{}]", errorMsg, e);
            
            return success(errorMsg);
        }
    }

    /**
     * 测试拆单明细结算
     * 处理结算状态为3（SPLIT_DETAIL_PENDING_SETTLE）的批发退货单
     */
    @PostMapping("/split-detail-settle")
    @Operation(summary = "测试拆单明细结算", description = "测试批发退货单拆单明细结算功能，处理结算状态为3的批发退货单")
    @PermitAll
    public CommonResult<String> testSplitDetailSettle(@RequestBody @Valid WholesalePfthdSettleParamsDTO params) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[testSplitDetailSettle][开始测试拆单明细结算，参数：{}]", params);
            
            // 调用拆单明细结算服务
            e3WholesalePfthdSettleService.doSplitDetailSettle(params);
            
            long endTime = System.currentTimeMillis();
            String successMsg = String.format("拆单明细结算测试执行成功，耗时：%dms", endTime - startTime);
            log.info("[testSplitDetailSettle][{}]", successMsg);
            
            return success(successMsg);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            String errorMsg = String.format("拆单明细结算测试执行失败，耗时：%dms，错误：%s", endTime - startTime, e.getMessage());
            log.error("[testSplitDetailSettle][{}]", errorMsg, e);
            
            return success(errorMsg);
        }
    }
} 
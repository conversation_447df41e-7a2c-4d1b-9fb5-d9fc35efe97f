package cn.iocoder.yudao.module.sap.task;

import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.E3OrderSyncParamsDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.OrderDealInfoVO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliveryDetailSaveRespVO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDeliveryDetailService;
import cn.iocoder.yudao.module.sap.service.cangqiong.impl.DeliveryDetailServiceImpl;
import cn.iocoder.yudao.module.sap.utils.E3DateUtils;
import cn.iocoder.yudao.module.sap.utils.SqlResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * E3订单同步为发货明细的定时任务
 */
@Component
@Slf4j
public class E3OrderSyncTask {

    @Resource
    private DeliveryDetailServiceImpl deliveryDetailService;

    @Resource
    private org.redisson.api.RedissonClient redissonClient;

    @Resource
    private CqDeliveryDetailService cqDeliveryDetailService;
    
    /**
     * Redis键前缀 - 记录上次同步的日期
     */
    private static final String REDIS_KEY_LAST_PROCESSED_DATE = "E3OrderSyncTask:%s:lastProcessedDate";
    
    /**
     * 日期格式 - 用于Redis存储
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    // ALTER TABLE tk_yd_fhmx
    // ADD UNIQUE INDEX ux_tk_yd_fhmx_fbillno (fbillno);
    
    // ALTER TABLE tk_yd_wholesalenoticebill
    // ADD UNIQUE INDEX ux_tk_yd_wholesalenoticebill_fbillno (fbillno);
    
    // ALTER TABLE tk_yd_wholesalereturnbill
    // ADD UNIQUE INDEX ux_tk_yd_wholesalereturnbill_fbillno (fbillno);

    /**
     * 同步E3订单到发货明细
     */
    @XxlJob("jsp.syncE3OrdersToDeliveryDetails")
    @TenantIgnore
    public void syncE3OrdersToDeliveryDetailsJob() {
        try {
            log.info("[syncE3OrdersToDeliveryDetailsJob][开始同步E3订单到发货明细]");
            // 默认同步maiyou公司，订单号和商店编码为空，时间范围为上月到今天
            DeliveryDetailSaveRespVO result = deliveryDetailService.syncE3OrdersToDeliveryDetails("maiyou", null, null);
            log.info("[syncE3OrdersToDeliveryDetailsJob][同步完成，成功:{}，失败:{}]", result.getSuccessCount(), result.getFailCount());
        } catch (IOException e) {
            log.error("[syncE3OrdersToDeliveryDetailsJob][同步异常]", e);
        } catch (Exception e) {
            log.error("[syncE3OrdersToDeliveryDetailsJob][未知异常]", e);
        }
    }

    /**
     * 为麦优公司同步E3订单的定时任务 - 按天循环处理
     */
    @XxlJob("jsp.syncE3MaiyouOrdersBySql")
    @TenantIgnore
    public void syncE3MaiyouOrdersBySqlJob() {
        org.redisson.api.RLock lock = redissonClient.getLock("E3OrderSyncTask:syncE3MaiyouOrdersBySqlJob:lock");
        boolean locked = false;
        try {
            // 尝试获取锁，等待10秒，启用自动续期
            locked = lock.tryLock(10, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("[syncE3MaiyouOrdersBySqlJob][获取Redis分布式锁失败，任务已在执行中]");
                return;
            }
            log.info("[syncE3MaiyouOrdersBySqlJob][成功获取分布式锁，设置24小时超时时间]");
            
            final String companyCode = "maiyou";
            log.info("[syncE3MaiyouOrdersBySqlJob][开始通过SQL同步E3订单到发货明细 - {}公司]", companyCode);
            
            // 获取上次处理的日期，如果没有则使用固定开始日期
            String redisKey = String.format(REDIS_KEY_LAST_PROCESSED_DATE, companyCode);
            LocalDate lastProcessedDate = getLastProcessedDate(redisKey);
            
            // 计算当前日期
            LocalDate currentDate = LocalDate.now();
            
            // 开始按天同步数据
            if (!E3DateUtils.isProcessingNeeded(lastProcessedDate, currentDate)) {
                log.info("[syncE3MaiyouOrdersBySqlJob][已经同步到当前日期，无需处理 - {}公司]", companyCode);
                return;
            }
            
            // 开始按天循环处理
            processOrdersByDay(companyCode, false, lastProcessedDate, currentDate, redisKey);
            
        } catch (Exception e) {
            log.error("[syncE3MaiyouOrdersBySqlJob][同步异常 - maiyou公司]", e);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 为佰悦公司同步E3订单的定时任务 - 按天循环处理
     */
    @XxlJob("jsp.syncE3BaiyueOrdersBySql")
    @TenantIgnore
    public void syncE3BaiyueOrdersBySqlJob() {
        org.redisson.api.RLock lock = redissonClient.getLock("E3OrderSyncTask:syncE3BaiyueOrdersBySqlJob:lock");
        boolean locked = false;
        try {
            // 尝试获取锁，等待10秒，启用自动续期
            locked = lock.tryLock(10, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("[syncE3BaiyueOrdersBySqlJob][获取Redis分布式锁失败，任务已在执行中]");
                return;
            }
            log.info("[syncE3BaiyueOrdersBySqlJob][成功获取分布式锁，设置24小时超时时间]");
            
            final String companyCode = "baiyue";
            log.info("[syncE3BaiyueOrdersBySqlJob][开始通过SQL同步E3订单到发货明细 - {}公司]", companyCode);
            
            // 获取上次处理的日期，如果没有则使用固定开始日期
            String redisKey = String.format(REDIS_KEY_LAST_PROCESSED_DATE, companyCode);
            LocalDate lastProcessedDate = getLastProcessedDate(redisKey);
            
            // 计算当前日期
            LocalDate currentDate = LocalDate.now();
            
            // 开始按天同步数据
            if (!E3DateUtils.isProcessingNeeded(lastProcessedDate, currentDate)) {
                log.info("[syncE3BaiyueOrdersBySqlJob][已经同步到当前日期，无需处理 - {}公司]", companyCode);
                return;
            }
            
            // 开始按天循环处理
            processOrdersByDay(companyCode, true, lastProcessedDate, currentDate, redisKey);
            
        } catch (Exception e) {
            log.error("[syncE3BaiyueOrdersBySqlJob][同步异常 - baiyue公司]", e);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 获取上次处理的日期
     * 如果Redis中没有记录，则返回默认的开始日期
     * 如果Redis中有记录，则返回上次处理日期的下一天
     * 
     * @param redisKey Redis键，用于存储上次处理的日期
     * @return 需要开始处理的日期
     */
    private LocalDate getLastProcessedDate(String redisKey) {
        String dateStr = (String) redissonClient.getBucket(redisKey).get();
        
        if (dateStr == null) {
            // 使用默认的开始日期
            LocalDate defaultStartDate = LocalDate.of(2025, 3, 1);
            log.info("[getLastProcessedDate][Redis中未找到上次处理时间，使用固定开始日期:{}]", 
                    E3DateUtils.formatLocalDate(defaultStartDate));
            return defaultStartDate;
        } else {
            // 从字符串转换为LocalDate并直接返回
            LocalDate lastDate = LocalDate.parse(dateStr, DATE_FORMATTER);
            log.info("[getLastProcessedDate][从Redis获取上次处理时间:{}]", 
                    E3DateUtils.formatLocalDate(lastDate));
            return lastDate;
        }
    }
    
    /**
     * 更新已处理的日期到Redis
     * 
     * @param redisKey Redis键
     * @param date 已处理的日期
     */
    private void updateLastProcessedDate(String redisKey, LocalDate date) {
        String dateStr = date.format(DATE_FORMATTER);
        redissonClient.getBucket(redisKey).set(dateStr);
        log.info("[updateLastProcessedDate][更新Redis中的处理时间:{}]", 
                E3DateUtils.formatLocalDate(date));
    }
    
    /**
     * 按天处理订单数据
     * 
     * @param companyCode 公司代码
     * @param useAltDb 是否使用备用数据库
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param redisKey Redis键，用于记录处理进度
     */
    private void processOrdersByDay(String companyCode, boolean useAltDb, LocalDate startDate, LocalDate endDate, String redisKey) {
        int totalProcessedDays = 0;
        int totalSuccessCount = 0;
        int totalFailCount = 0;
        
        log.info("[processOrdersByDay][开始按天处理订单, 公司:{}, 开始日期:{}, 结束日期:{}]", 
                companyCode, E3DateUtils.formatLocalDate(startDate), 
                E3DateUtils.formatLocalDate(endDate));
        
        // 检查是否需要处理
        if (!E3DateUtils.isProcessingNeeded(startDate, endDate)) {
            log.info("[processOrdersByDay][已经同步到当前日期，无需处理 - {}公司]", companyCode);
            return;
        }
        
        // 使用LocalDate进行循环
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            log.info("[processOrdersByDay][处理第{}天数据, 公司:{}, 日期:{}]", 
                    totalProcessedDays + 1, companyCode, 
                    E3DateUtils.formatLocalDate(currentDate));
            
            try {
                // 调用内部方法处理单天数据，使用LocalDate版本
                int[] results = syncE3OrdersBySqlForDay(companyCode, useAltDb, currentDate);
                int successCount = results[0];
                int failCount = results[1];
                
                totalSuccessCount += successCount;
                totalFailCount += failCount;
                
                // 更新处理进度到Redis
                updateLastProcessedDate(redisKey, currentDate);
                
                log.info("[processOrdersByDay][完成第{}天数据处理, 公司:{}, 日期:{}, 成功:{}, 失败:{}]", 
                        totalProcessedDays + 1, companyCode, 
                        E3DateUtils.formatLocalDate(currentDate), 
                        successCount, failCount);
                
            } catch (Exception e) {
                log.error("[processOrdersByDay][处理第{}天数据异常, 公司:{}, 日期:{}]", 
                        totalProcessedDays + 1, companyCode, 
                        E3DateUtils.formatLocalDate(currentDate), e);
                
                totalFailCount++;
            }
            
            totalProcessedDays++;
            
            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }
        
        log.info("[processOrdersByDay][按天处理订单完成, 公司:{}, 处理天数:{}, 总成功:{}, 总失败:{}]", 
                companyCode, totalProcessedDays, totalSuccessCount, totalFailCount);
    }
    
    /**
     * 创建命名的线程工厂
     * 
     * @param namePrefix 线程名称前缀
     * @return 线程工厂
     */
    private ThreadFactory createNamedThreadFactory(String namePrefix) {
        return new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
                thread.setDaemon(true);
                return thread;
            }
        };
    }
    
    /**
     * 处理单天数据并返回处理结果
     * 
     * @param companyCode 公司代码
     * @param useAltDb 是否使用备用数据库
     * @param date 处理日期
     * @return 包含成功和失败数量的数组 [successCount, failCount]
     */
    private int[] syncE3OrdersBySqlForDay(String companyCode, boolean useAltDb, LocalDate date) {
        // 将日期转换为时间戳范围
        long dayStartTimestamp = E3DateUtils.localDateToStartTimestamp(date);
        long dayEndTimestamp = E3DateUtils.localDateToEndTimestamp(date);
        
        return syncE3OrdersBySqlForDay(companyCode, useAltDb, dayStartTimestamp, dayEndTimestamp);
    }
    
    /**
     * 处理单天数据并返回处理结果 - 使用批处理并发方式
     * 
     * @param companyCode 公司代码
     * @param useAltDb 是否使用备用数据库
     * @param dayStartTimestamp 日期开始时间戳
     * @param dayEndTimestamp 日期结束时间戳
     * @return 包含成功和失败数量的数组 [successCount, failCount]
     */
    private int[] syncE3OrdersBySqlForDay(String companyCode, boolean useAltDb, long dayStartTimestamp, long dayEndTimestamp) {
        // 分页查询参数
        int pageSize = 50000;
        int offset = 0;
        int totalOrders = 0;
        int totalSuccessCount = 0;
        int totalFailCount = 0;
        
        // 构建基础SQL查询
        String baseSql = buildOrderQuerySql(dayStartTimestamp, dayEndTimestamp);
        
        // 分页获取订单号并立即处理
        while (true) {
            String paginatedSql = baseSql + " LIMIT " + offset + ", " + pageSize;
            log.info("[syncE3OrdersBySqlForDay][执行SQL分页查询，公司:{}, 日期:{}, offset={}, pageSize={}]", 
                    companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)), offset, pageSize);

            SqlResult result = cn.iocoder.yudao.module.sap.utils.E3SqlApiUtils.sqlExec(
                    paginatedSql,
                    new com.alibaba.fastjson.TypeReference<SqlResult>() {},
                    useAltDb
            );

            // 检查查询结果
            if (result == null || result.getData() == null || result.getData().isEmpty()) {
                log.info("[syncE3OrdersBySqlForDay][分页查询无更多数据，公司:{}, 日期:{}, 结束]", 
                        companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)));
                break;
            }

            // 提取订单号和交易号到OrderDealInfoVO
            List<OrderDealInfoVO> pageOrderInfoList = new ArrayList<>();
            for (Map<String, Object> row : result.getData()) {
                Object orderSnObj = row.get("order_sn");
                Object dealCodeObj = row.get("deal_code");
                if (orderSnObj != null) {
                    OrderDealInfoVO orderInfo = new OrderDealInfoVO();
                    orderInfo.setOrderNo(orderSnObj.toString());
                    orderInfo.setDealCode(dealCodeObj != null ? dealCodeObj.toString() : null);
                    pageOrderInfoList.add(orderInfo);
                }
            }

            totalOrders += pageOrderInfoList.size();
            
            // 立即同步当前页的订单数据
            if (!pageOrderInfoList.isEmpty()) {
                log.info("[syncE3OrdersBySqlForDay][当前页获取订单数:{}, 公司:{}, 日期:{}, 开始同步]", 
                        pageOrderInfoList.size(), companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)));
                
                List<OrderDealInfoVO> orderDealInfoList = cqDeliveryDetailService.getOrderNoAndDealCodeByDeliveryDate(
                        E3DateUtils.timestampToLocalDate(dayStartTimestamp), 
                        E3DateUtils.timestampToLocalDate(dayEndTimestamp),
                        false);

                // 记录过滤前的订单数量
                int beforeFilterSize = pageOrderInfoList.size();
                
                // 创建已存在订单的组合键集合 (orderNo::dealCode)
                Set<String> existingOrderKeys = orderDealInfoList.stream()
                        .map(order -> order.getOrderNo() + "::" + (order.getDealCode() == null ? "" : order.getDealCode()))
                        .collect(java.util.stream.Collectors.toSet());
                
                // 过滤pageOrderInfoList，保留不在existingOrderKeys中的订单
                List<OrderDealInfoVO> filteredList = pageOrderInfoList.stream()
                        .filter(order -> !existingOrderKeys.contains(order.getOrderNo() + "::" + (order.getDealCode() == null ? "" : order.getDealCode())))
                        .collect(java.util.stream.Collectors.toList());
                
                // 记录过滤结果
                int afterFilterSize = filteredList.size();
                int removedCount = beforeFilterSize - afterFilterSize;
                log.info("[syncE3OrdersBySqlForDay][过滤已存在订单完成，原始数量:{}, 过滤后数量:{}, 移除数量:{}]", 
                        beforeFilterSize, afterFilterSize, removedCount);
                
                // 更新pageOrderInfoList为过滤后的列表，并去重
                pageOrderInfoList = filteredList.stream()
                        .distinct()
                        .collect(java.util.stream.Collectors.toList());
                log.info("[syncE3OrdersBySqlForDay][订单列表去重完成，去重前数量:{}, 去重后数量:{}]", 
                        filteredList.size(), pageOrderInfoList.size());

                // 按orderNo升序排列
                pageOrderInfoList.sort(Comparator.comparing(OrderDealInfoVO::getOrderNo));
                log.debug("[syncE3OrdersBySqlForDay][订单列表已按orderNo升序排列]");
                
                // 使用批处理并发处理订单
                int[] batchResults = processOrdersInBatches(companyCode, pageOrderInfoList, dayStartTimestamp);
                int pageSuccessCount = batchResults[0];
                int pageFailCount = batchResults[1];
                
                totalSuccessCount += pageSuccessCount;
                totalFailCount += pageFailCount;
                
                log.info("[syncE3OrdersBySqlForDay][当前页同步完成，公司:{}, 日期:{}, 订单数:{}, 成功:{}, 失败:{}]", 
                        companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)), 
                        pageOrderInfoList.size(), pageSuccessCount, pageFailCount);
            } else {
                log.info("[syncE3OrdersBySqlForDay][当前页未获取到任何订单，公司:{}, 日期:{}, 跳过同步]", 
                        companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)));
            }
            
            // 判断是否继续分页
            if (result.getData().size() < pageSize) {
                log.info("[syncE3OrdersBySqlForDay][当前页数据量不足pageSize，公司:{}, 日期:{}, 结束翻页]", 
                        companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)));
                break;
            }
            offset += pageSize;
        }

        log.info("[syncE3OrdersBySqlForDay][日期数据同步完成，公司:{}, 日期:{}, 总订单数:{}, 总成功:{}, 总失败:{}]", 
                companyCode, E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)), 
                totalOrders, totalSuccessCount, totalFailCount);
        
        return new int[] {totalSuccessCount, totalFailCount};
    }
    
    /**
     * 批量并发处理订单列表
     * 
     * @param companyCode 公司代码
     * @param orderInfoList 订单信息列表
     * @param dayStartTimestamp 日期开始时间戳（用于日志记录）
     * @return 包含成功和失败数量的数组 [successCount, failCount]
     */
    private int[] processOrdersInBatches(String companyCode, List<OrderDealInfoVO> orderInfoList, long dayStartTimestamp) {
        int totalSuccessCount = 0;
        int totalFailCount = 0;
        
        // 批处理参数
        final int batchSize = 100; // 每批处理的订单数
        final int maxThreads = 3; // 最大并发线程数
        
        // 创建线程池
        ThreadFactory threadFactory = createNamedThreadFactory("order-sync-worker-");
        ExecutorService executor = Executors.newFixedThreadPool(maxThreads, threadFactory);
        
        try {
            // 分批处理
            for (int i = 0; i < orderInfoList.size(); i += batchSize) {
                final int endIndex = Math.min(i + batchSize, orderInfoList.size());
                List<OrderDealInfoVO> batch = orderInfoList.subList(i, endIndex);
                
                log.info("[processOrdersInBatches][开始处理批次 {}/{}, 批次大小: {}, 公司:{}]", 
                    (i / batchSize) + 1, 
                    (orderInfoList.size() + batchSize - 1) / batchSize, 
                    batch.size(),
                    companyCode);
                
                // 线程安全的计数器
                AtomicInteger batchSuccessCount = new AtomicInteger(0);
                AtomicInteger batchFailCount = new AtomicInteger(0);
                
                // 创建当前批次的并发任务
                List<CompletableFuture<Void>> futures = batch.stream()
                    .map(orderInfo -> CompletableFuture.runAsync(() -> {
                        try {
                            DeliveryDetailSaveRespVO syncResult = deliveryDetailService.syncE3OrdersToDeliveryDetails(
                                    companyCode, orderInfo.getOrderNo(), null);
                            batchSuccessCount.addAndGet(syncResult.getSuccessCount());
                            batchFailCount.addAndGet(syncResult.getFailCount());
                        } catch (Exception e) {
                            batchFailCount.incrementAndGet();
                            log.error("[processOrdersInBatches][同步订单{}失败，公司:{}, 日期:{}]", 
                                    orderInfo.getOrderNo(), companyCode, 
                                    E3DateUtils.formatLocalDate(E3DateUtils.timestampToLocalDate(dayStartTimestamp)), e);
                        }
                    }, executor))
                    .collect(Collectors.toList());
                
                // 等待当前批次全部完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                
                // 累加结果
                int batchSuccess = batchSuccessCount.get();
                int batchFail = batchFailCount.get();
                totalSuccessCount += batchSuccess;
                totalFailCount += batchFail;
                
                log.info("[processOrdersInBatches][批次 {}/{} 处理完成, 公司:{}, 成功: {}, 失败: {}]", 
                    (i / batchSize) + 1, 
                    (orderInfoList.size() + batchSize - 1) / batchSize, 
                    companyCode,
                    batchSuccess, batchFail);
            }
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                // 等待线程池优雅关闭，最多等待30秒
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("[processOrdersInBatches][线程池未能在30秒内完全关闭]");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[processOrdersInBatches][等待线程池关闭被中断]", e);
            }
        }
        
        return new int[] {totalSuccessCount, totalFailCount};
    }
    
    /**
     * 通过SQL同步E3订单的内部实现方法
     * 
     * @param companyCode 公司代码
     * @param useAltDb 是否使用备用数据库
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void syncE3OrdersBySqlInternal(String companyCode, boolean useAltDb, LocalDate startDate, LocalDate endDate) {
        // 分页查询参数
        int pageSize = 2000;
        int offset = 0;
        int totalOrders = 0;
        int totalSuccessCount = 0;
        int totalFailCount = 0;
        
        // 构建基础SQL查询
        String baseSql = buildOrderQuerySql(startDate, endDate);
        
        // 分页获取订单号并立即处理
        while (true) {
            String paginatedSql = baseSql + " LIMIT " + offset + ", " + pageSize;
            log.info("[syncE3OrdersBySqlInternal][执行SQL分页查询，公司:{}, 日期范围:{}-{}, offset={}, pageSize={}]", 
                    companyCode, E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate), offset, pageSize);

            SqlResult result = cn.iocoder.yudao.module.sap.utils.E3SqlApiUtils.sqlExec(
                    paginatedSql,
                    new com.alibaba.fastjson.TypeReference<SqlResult>() {},
                    useAltDb
            );

            // 检查查询结果
            if (result == null || result.getData() == null || result.getData().isEmpty()) {
                log.info("[syncE3OrdersBySqlInternal][分页查询无更多数据，公司:{}, 日期范围:{}-{}, 结束]", 
                        companyCode, E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate));
                break;
            }

            // 提取订单号和交易号到OrderDealInfoVO
            List<OrderDealInfoVO> pageOrderInfoList = new ArrayList<>();
            for (java.util.Map<String, Object> row : result.getData()) {
                Object orderSnObj = row.get("order_sn");
                Object dealCodeObj = row.get("deal_code");
                if (orderSnObj != null) {
                    OrderDealInfoVO orderInfo = new OrderDealInfoVO();
                    orderInfo.setOrderNo(orderSnObj.toString());
                    orderInfo.setDealCode(dealCodeObj != null ? dealCodeObj.toString() : null);
                    pageOrderInfoList.add(orderInfo);
                }
            }

            totalOrders += pageOrderInfoList.size();
            
            // 立即同步当前页的订单数据
            if (!pageOrderInfoList.isEmpty()) {
                log.info("[syncE3OrdersBySqlInternal][当前页获取订单数:{}, 公司:{}, 日期范围:{}-{}, 开始同步]", 
                        pageOrderInfoList.size(), companyCode, 
                        E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate));
                
                // 按orderNo升序排列
                pageOrderInfoList.sort(Comparator.comparing(OrderDealInfoVO::getOrderNo));
                log.debug("[syncE3OrdersBySqlInternal][订单列表已按orderNo升序排列]");
                
                // 使用批处理并发方式处理订单
                int[] batchResults = processOrdersInBatches(companyCode, pageOrderInfoList, 
                        E3DateUtils.localDateToStartTimestamp(startDate));
                int pageSuccessCount = batchResults[0];
                int pageFailCount = batchResults[1];
                
                totalSuccessCount += pageSuccessCount;
                totalFailCount += pageFailCount;
                
                log.info("[syncE3OrdersBySqlInternal][当前页同步完成，公司:{}, 日期范围:{}-{}, 订单数:{}, 成功:{}, 失败:{}]", 
                        companyCode, E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate),
                        pageOrderInfoList.size(), pageSuccessCount, pageFailCount);
            } else {
                log.info("[syncE3OrdersBySqlInternal][当前页未获取到任何订单，公司:{}, 日期范围:{}-{}, 跳过同步]", 
                        companyCode, E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate));
            }
            
            // 判断是否继续分页
            if (result.getData().size() < pageSize) {
                log.info("[syncE3OrdersBySqlInternal][当前页数据量不足pageSize，公司:{}, 日期范围:{}-{}, 结束翻页]", 
                        companyCode, E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate));
                break;
            }
            offset += pageSize;
        }

        log.info("[syncE3OrdersBySqlInternal][所有页同步完成，公司:{}, 日期范围:{}-{}, 总订单数:{}, 总成功:{}, 总失败:{}]", 
                companyCode, E3DateUtils.formatLocalDate(startDate), E3DateUtils.formatLocalDate(endDate),
                totalOrders, totalSuccessCount, totalFailCount);
    }
    
    /**
     * 构建查询订单的SQL
     * 优化：直接使用时间戳比较，避免在索引字段上使用函数导致索引失效
     */
    private String buildOrderQuerySql(long startTimestamp, long endTimestamp) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT\n");
        sqlBuilder.append("  order_info.order_sn,\n");
        sqlBuilder.append("  order_info.deal_code \n");
        sqlBuilder.append("FROM\n");
        sqlBuilder.append("  order_info\n");
        sqlBuilder.append("LEFT JOIN kehu ON order_info.sd_id = kehu.id\n");
        sqlBuilder.append("WHERE\n");
        sqlBuilder.append("  order_info.shipping_status = '7'\n");
        sqlBuilder.append("  AND order_info.shipping_time_ck >= ").append(startTimestamp).append("\n");
        sqlBuilder.append("  AND order_info.shipping_time_ck <= ").append(endTimestamp).append("\n");
        // sqlBuilder.append("  AND kehu.khdm LIKE '%temp%'\n");
//        sqlBuilder.append("  AND order_sn = '125040845106169' ");
        // sqlBuilder.append("ORDER BY order_info.shipping_time_ck ASC");
        return sqlBuilder.toString();
    }

    /**
     * 构建查询订单的SQL，使用LocalDate参数
     * 优化：直接使用时间戳比较，避免在索引字段上使用函数导致索引失效
     */
    private String buildOrderQuerySql(LocalDate startDate, LocalDate endDate) {
        // 转换为时间戳
        long startTimestamp = E3DateUtils.localDateToStartTimestamp(startDate);
        long endTimestamp = E3DateUtils.localDateToEndTimestamp(endDate);
        
        return buildOrderQuerySql(startTimestamp, endTimestamp);
    }

    /**
     * 通过JSON参数同步E3订单到发货明细
     * 参数必须包含companyCode和orderNo
     */
    @XxlJob("jsp.syncE3OrdersByJsonParams")
    @TenantIgnore
    public void syncE3OrdersByJsonParams() {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("[syncE3OrdersByJsonParams][接收到任务参数:{}]", jobParam);
            
            // 参数检查
            if (StringUtils.isEmpty(jobParam)) {
                log.error("[syncE3OrdersByJsonParams][参数不能为空，必须提供companyCode和orderNo]");
                return;
            }
            
            // 解析参数
            E3OrderSyncParamsDTO paramsDTO = deliveryDetailService.parseE3OrderSyncParams(jobParam);
            
            // 必填参数验证
            if (StringUtils.isEmpty(paramsDTO.getCompanyCode())) {
                log.error("[syncE3OrdersByJsonParams][companyCode为必填参数]");
                return;
            }
            
            if (StringUtils.isEmpty(paramsDTO.getOrderNo())) {
                log.error("[syncE3OrdersByJsonParams][orderNo为必填参数]");
                return;
            }
            
            log.info("[syncE3OrdersByJsonParams][开始同步E3订单到发货明细, companyCode:{}, orderNo:{}, storeCode:{}]", 
                    paramsDTO.getCompanyCode(), paramsDTO.getOrderNo(), paramsDTO.getStoreCode());
            
            // 执行同步
            DeliveryDetailSaveRespVO result = deliveryDetailService.syncE3OrdersToDeliveryDetails(
                    paramsDTO.getCompanyCode(), paramsDTO.getOrderNo(), paramsDTO.getStoreCode());
            
            log.info("[syncE3OrdersByJsonParams][同步完成，成功:{}，失败:{}]", 
                    result.getSuccessCount(), result.getFailCount());
        } catch (IOException e) {
            log.error("[syncE3OrdersByJsonParams][同步IO异常]", e);
        } catch (Exception e) {
            log.error("[syncE3OrdersByJsonParams][未知异常]", e);
        }
    }
}

package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import lombok.Data;

/**
 * 批发退货单结算更新DTO
 */
@Data
public class WholesaleReturnBillSettleUpdateDTO {

    /**
     * 业务场景
     */
    private String ydBusinessscene;

    /**
     * 销售组织ID
     */
    private Long orgId;

    /**
     * 苍穹客户ID
     */
    private Long cqCustomerId;

    /**
     * 是否按品牌分单
     */
    private Boolean isBrandSplitBill;

    /**
     * 渠道组织不存在
     */
    private Boolean notExistChannelOrg;

    /**
     * 客户不存在
     */
    private Boolean notExistCustomer;

    /**
     * 剔除仓库
     */
    private Boolean excludeStock;

    /**
     * 结算状态
     */
    private String settleStatus;
} 
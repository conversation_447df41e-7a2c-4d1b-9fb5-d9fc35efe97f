package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 管理后台 - 苍穹发货明细结算信息VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CqDeliveryDetailSettleInfoVO {

    /**
     * 发货明细ID
     */
    private Long id = null;

    /**
     * 是否不存在客户 0-存在 1-不存在
     */
    private Integer isNotExitCustomer = null;

    /**
     * 是否按品牌分单 0-不分单 1-分单
     */
    private Integer isBrandSplitBill = null;

    /**
     * 是否排除仓库 0-不排除 1-排除
     */
    private Integer isExcludedWarehouse = null;

    /**
     * 下游单据类型
     */
    private String downstreamBillType = null;

    /**
     * 是否不传EAS 0-传EAS 1-不传EAS
     */
    private Integer isNotSendEas = null;

    /**
     * 苍穹客户ID
     */
    private Long cqCustomerId = null;

    /**
     * 是否非销售 0-正常销售 1-非销售
     */
    private Integer isNotSale = null;

    /**
     * 预算承担公司
     */
    private String ydBudgetcompany = null;

    /**
     * 非销店铺
     */
    private String ydNotsaleshop = null;

    /**
     * 申请部门
     */
    private String ydApplydepart = null;

    /**
     * 领料用途
     */
    private String ydRequisitionuse = null;

    /**
     * 预算承担部门
     */
    private String ydBudgetdepart = null;

    /**
     * 预算科目
     */
    private String ydBudgetaccount = null;

    /**
     * 店铺组织ID
     */
    private Long shopOrgId = null;

    // /**
    //  * 苍穹仓库ID
    //  */
    // private Long cqWarehouseId = null;

    /**
     * 是否存在仓库 0-存在 1-不存在
     */
    private Integer isNotExitWarehouse = null;
    
    /**
     * 退单关联订单是否不存在 0-存在 1-不存在
     */
    private Integer relatedOrderNotExists = null;

    /**
     * 销售组织仓库是否不存在 0-存在 1-不存在
     */
    private Integer isNotExitSaleOrgStock = null;

    /**
     * 物料是否不存在 0-存在 1-不存在
     */
    private Integer isNotExitMaterial = null;

    /**
     * 库存组织是否不存在 0-存在 1-不存在
     */
    private Integer isNotExitInvOrg = null;

    /**
     * 库存组织仓库是否不存在 0-存在 1-不存在
     */
    private Integer isNotExitInvOrgStock = null;

    /**
     * 销售组织是否不存在 0-存在 1-不存在
     */
    private Integer isNotExitSaleOrg = null;

    /**
     * 商家备注
     */
    private String merchantRemark = null;

    /**
     * 订单备注
     */
    private String orderRemark = null;
    
    /**
     * 成本中心
     */
    private String costCenter = null;
    
    /**
     * 基金科目
     */
    private String fundAccount = null;
    
    /**
     * 客户(非销)
     */
    private String notSaleCust = null;
    
    /**
     * 客户类型(非销)
     */
    private String notSaleCustType = null;
    
    /**
     * 预留单号
     */
    private String reservedOrderNum = null;
} 
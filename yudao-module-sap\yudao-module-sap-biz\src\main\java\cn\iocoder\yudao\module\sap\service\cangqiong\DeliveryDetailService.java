package cn.iocoder.yudao.module.sap.service.cangqiong;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.E3OrderSyncParamsDTO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliveryDetailSaveRespVO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliveryDetailVO;

import java.io.IOException;
import java.util.List;

/**
 * 发货明细表 Service 接口
 */
public interface DeliveryDetailService {

    /**
     * 保存发货明细表
     *
     * @param deliveryDetails 发货明细表列表
     * @return 保存结果
     * @throws IOException 请求异常
     */
    DeliveryDetailSaveRespVO saveDeliveryDetails(List<DeliveryDetailVO> deliveryDetails) throws IOException;
    
    /**
     * 从E3获取订单并保存为发货明细
     *
     * @param company 公司名称
     * @param orderSn 订单编号，可选
     * @param sdCode 商店编码，可选
     * @return 保存结果
     * @throws IOException 请求异常
     */
    DeliveryDetailSaveRespVO syncE3OrdersToDeliveryDetails(String company, String orderSn, String sdCode) throws IOException;

    /**
     * 解析E3订单同步任务参数
     *
     * @param param JSON格式的参数字符串
     * @return 解析后的参数对象
     */
    E3OrderSyncParamsDTO parseE3OrderSyncParams(String param);

} 
package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发货明细表 DO
 */
@TableName("tk_yd_fhmx")
@KeySequence("tk_yd_fhmx_seq")
@Data
@ToString(callSuper = true)
public class CqDeliveryDetailDO {

    // ==================== 基础信息 ====================
    /**
     * 主键ID
     */
    @TableId(value = "FId")
    private Long id;

    /**
     * 单据编号
     */
    @TableField("fbillno")
    private String billNo;

    /**
     * 单据状态
     * 枚举:A:暂存 B:已提交 C:已审核
     */
    @TableField("fbillstatus")
    private String billStatus;

    // ==================== 审计信息 ====================
    /**
     * 创建人
     * 基础资料：人员 bos_user
     */
    @TableField("fcreatorid")
    private Long creatorId;

    /**
     * 修改人
     * 基础资料：人员 bos_user
     */
    @TableField("fmodifierid")
    private Long modifierId;

    /**
     * 审核人ID
     * 基础资料：人员 bos_user
     */
    @TableField("fauditorid")
    private Long auditorId;

    /**
     * 审核日期
     */
    @TableField("fauditdate")
    private LocalDateTime auditDate;

    /**
     * 修改时间
     */
    @TableField("fmodifytime")
    private LocalDateTime modifyTime;

    /**
     * 创建时间
     */
    @TableField("fcreatetime")
    private LocalDateTime createTime;

    // ==================== 订单基本信息 ====================
    /**
     * 平台
     * 枚举:1:E3 2:旺店通 3:吉客云 4:万里牛 5:新E3
     */
    @TableField("fk_yd_combofield_pt")
    private String platform;

    /**
     * 订单编号
     */
    @TableField("fk_yd_textfield_ddbh")
    private String orderNo;

    /**
     * 店铺编号
     */
    @TableField("fk_yd_textfield_dpbh")
    private String shopNo;

    /**
     * 下游单号
     */
    @TableField("fk_yd_textfield_xsckdh")
    private String downstreamNo;

    /**
     * 平台发货时间
     */
    @TableField("fk_yd_datetimefield_xdsj")
    private LocalDateTime platformDeliveryTime;

    /**
     * 下游单据类型
     * 枚举:1:销售出库单 2:其他出库单
     */
    @TableField("fk_yd_combofield_xyd")
    private String downstreamType;

    /**
     * 单据类型
     * 枚举:0:订单 1:退单
     */
    @TableField("fk_yd_ordertype")
    private String orderType;

    /**
     * 来源类型
     */
    @TableField("fk_yd_lylx")
    private String sourceType;

    /**
     * 商店id
     */
    @TableField("fk_yd_sdid")
    private String storeId;

    /**
     * 店铺名称
     */
    @TableField("fk_yd_sdname")
    private String shopName;

    /**
     * 店铺
     */
    @TableField("fk_yd_notsaleshop")
    private String notSaleShop;

    /**
     * 店铺组织
     */
    @TableField("fk_yd_shoporgid")
    private Long shopOrg;

    /**
     * 订单状态
     */
    @TableField("fk_yd_orderstatus")
    private Long orderStatus;

    /**
     * 财务状态
     */
    @TableField("fk_yd_paystatus")
    private String payStatus;

    /**
     * 结算状态
     * 枚举:1:待结算 2:结算异常 3:已结算，待下推；4:已部分下推；5：已完全下推
     */
    @TableField("fk_yd_settlestatus")
    private String settleStatus;

    /**
     * 客户
     * 基础资料：客户 bd_customer
     */
    @TableField("fk_yd_customerid")
    private Long customerId;

    /**
     * 交易号
     */
    @TableField("fk_yd_dealcode")
    private String dealCode;

    /**
     * 备注
     */
    @TableField("fk_yd_bz")
    private String remark;

    /**
     * 商家备注
     */
    @TableField("fk_yd_merchantremark")
    private String merchantRemark;

    // ==================== 状态标记 ====================
    /**
     * 是否开票
     */
    @TableField("fk_yd_checkboxfield_sfkp")
    private String isInvoiced;

    /**
     * 是否手工
     */
    @TableField("fk_yd_checkboxfield_sfsg")
    private String isManual;

    /**
     * 客户不存在
     */
    @TableField("fk_yd_checkboxfield_khbcz")
    private String customerNotExist;

    // /**
    //  * 仓库不存在
    //  */
    // @TableField("fk_yd_checkboxfield_ckbcz")
    // private String warehouseNotExist;

    /**
     * 物料不存在
     */
    @TableField("fk_yd_checkboxfield_wlbcz")
    private Integer materialNotExist;   

    /**
     * 不传EAS
     */
    @TableField("fk_yd_checkboxfield_bcl")
    private String notTransferToEAS;

    /**
     * 是否退货
     */
    @TableField("fk_yd_checkboxfield_th")
    private String isReturn;

    /**
     * 参与合单
     */
    @TableField("fk_yd_checkboxfield_cyhd")
    private String participateInCombine;

    /**
     * 整单剔除物料
     */
    @TableField("fk_yd_checkboxfield_khwdy")
    private String excludeMaterialInOrder;

    /**
     * 物料未审核或物料库存,销售信息未审核
     */
    @TableField("fk_yd_checkboxfield_wlwsh")
    private String materialNotAudited;

    /**
     * 排除仓库
     */
    @TableField("fk_yd_pcck")
    private Integer excludeWarehouse;

    /**
     * 物料对应关系重复
     */
    @TableField("fk_yd_dygxcf")
    private String materialRefDuplicated;

    /**
     * 同步到eas成功
     */
    @TableField("fk_yd_tbeas")
    private String syncToEASSuccess;

    /**
     * 单据异常
     */
    @TableField("fk_yd_iserror")
    private String isError;

    /**
     * 是否换货单
     */
    @TableField("fk_yd_ishh")
    private String isExchangeOrder;

    /**
     * 是否被拆分
     */
    @TableField("fk_yd_issplit")
    private String isSplit;

    /**
     * 是否拆分子单
     */
    @TableField("fk_yd_issplitnew")
    private String isSplitNew;

    /**
     * 是否被合并
     */
    @TableField("fk_yd_iscombine")
    private String isCombined;

    /**
     * 是否合并新单
     */
    @TableField("fk_yd_iscombinenew")
    private String isCombinedNew;

    /**
     * 是否复制单
     */
    @TableField("fk_yd_iscopy")
    private String isCopy;

    /**
     * 是否包含组装品
     */
    @TableField("fk_yd_ispackage")
    private Integer hasAssembly;

    /**
     * 是否组装拆分
     */
    @TableField("fk_yd_ispagesplit")
    private String isAssemblySplit;

    /**
     * 价税合计
     */
    @TableField("fk_yd_isamountandtax")
    private BigDecimal isAmountAndTax;

    /**
     * 是否非销
     */
    @TableField("fk_yd_isnotsale")
    private Integer isNotSale;

    /**
     * 是否按品牌分单
     */
    @TableField("fk_yd_isbrandsplitbill")
    private Integer isBrandSplitBill;

    // ==================== 金额信息 ====================
    /**
     * 运费
     */
    @TableField("fk_yd_decimalfield_yf")
    private BigDecimal freight;

    /**
     * 已付金额
     */
    @TableField("fk_yd_payment")
    private BigDecimal payment;

    /**
     * 订单总金额
     */
    @TableField("fk_yd_totalamount")
    private BigDecimal totalAmount;

    /**
     * 平台活动价(淘宝订单)
     */
    @TableField("fk_yd_totalfee")
    private BigDecimal platformActivityPrice;

    /**
     * 订单其他折让(整单折让)
     */
    @TableField("fk_yd_otherdiscountfee")
    private BigDecimal otherDiscountFee;

    /**
     * 买家应付金额
     */
    @TableField("fk_yd_orderamount")
    private BigDecimal orderAmount;

    /**
     * 总重量(单位KG)
     */
    @TableField("fk_yd_weigh")
    private BigDecimal weight;

    /**
     * 卖家优惠(子)
     */
    @TableField("fk_yd_good_offer")
    private BigDecimal goodOffer;

    /**
     * 卖家券
     */
    @TableField("fk_yd_seller_offer")
    private BigDecimal sellerOffer;

    /**
     * 汇率
     */
    @TableField("fk_yd_exrate")
    private BigDecimal exRate;

    /**
     * 剔除总金额
     */
    @TableField("fk_yd_excludeamount")
    private BigDecimal excludeAmount;

    /**
     * 结算币别
     * 基础资料：币别 bd_currency
     */
    @TableField("fk_yd_settlecurrency")
    private Long settleCurrencyId;

    /**
     * 税
     */
    @TableField("fk_yd_tax")
    private BigDecimal tax;

    /**
     * 订单总金额(亚马逊)
     */
    @TableField("fk_yd_totalamount_amz")
    private BigDecimal totalAmountAmz;

    /**
     * 货币类别
     */
    @TableField("fk_yd_currency")
    private String currency;

    // ==================== 物流信息 ====================
    /**
     * 发货仓库
     */
    @TableField("fk_yd_textfield_ck")
    private String warehouse;

    /**
     * 仓库名称
     */
    @TableField("fk_yd_fhckmc")
    private String warehouseName;

    /**
     * 快递编码
     */
    @TableField("fk_yd_shippingcode")
    private String expressCode;

    /**
     * 快递名称
     */
    @TableField("fk_yd_shippingname")
    private String expressName;

    /**
     * 快递单号
     */
    @TableField("fk_yd_shippingsn")
    private String expressNo;

    /**
     * 退单物流状态
     */
    @TableField("fk_yd_shippingstatus")
    private String shippingStatus;

    /**
     * 渠道代码
     */
    @TableField("fk_yd_qdcode")
    private String channelCode;

    /**
     * 渠道名称
     */
    @TableField("fk_yd_qdname")
    private String channelName;

    // /**
    //  * 苍穹仓库ID
    //  */
    // @TableField("fk_yd_cqwarehouseid")
    // private Long warehouseId;

    // ==================== 地址信息 ====================
    /**
     * 省
     */
    @TableField("fk_yd_provincename")
    private String province;

    /**
     * 市
     */
    @TableField("fk_yd_cityname")
    private String city;

    /**
     * 区
     */
    @TableField("fk_yd_districtname")
    private String district;

    /**
     * 收货地址
     */
    @TableField("fk_yd_address")
    private String address;

    // ==================== 时间信息 ====================
    /**
     * 出入库时间
     */
    @TableField("fk_yd_datefield_fhrq")
    private LocalDateTime deliveryDate;

    /**
     * E3出库时间
     */
    @TableField("fk_yd_shippingtimeck")
    private LocalDateTime shippingTimeCk;

    /**
     * E3发货时间
     */
    @TableField("fk_yd_shippingtimefh")
    private LocalDateTime shippingTimeFh;

    /**
     * 下单时间
     */
    @TableField("fk_yd_addtime")
    private LocalDateTime addTime;

    /**
     * 支付时间
     */
    @TableField("fk_yd_paytime")
    private LocalDateTime payTime;

    // ==================== 关联信息 ====================
    /**
     * 换货原始交易号
     */
    @TableField("fk_yd_oridealcode")
    private String oriDealCode;

    /**
     * 换货原始订单号
     */
    @TableField("fk_yd_oriordersn")
    private String oriOrderSn;

    /**
     * 退单关联订单号
     */
    @TableField("fk_yd_relatingordersn")
    private String relatingOrderSn;

    // ==================== 其他信息 ====================
    /**
     * 异常情况
     * 枚举:0:无异常 1:金额为负，请核对E3单据各分录行金额、数量 2:数量缺漏，请核对E3单据各分录行金额、数量 3:数量缺漏、金额为负，请核对E3单据各分录行金额、数量
     */
    @TableField("fk_yd_errorstate")
    private String errorState;

    /**
     * 失败原因
     */
    @TableField("fk_yd_textfield_sbyy")
    private String failReason;

    /**
     * 预算承担公司
     */
    @TableField("fk_yd_budgetcompany")
    private String budgetCompany;

    /**
     * 预算承担部门
     */
    @TableField("fk_yd_budgetdepart")
    private String budgetDepartment;

    /**
     * 预算科目
     */
    @TableField("fk_yd_budgetaccount")
    private String budgetAccount;

    /**
     * 申请部门
     */
    @TableField("fk_yd_applydepart")
    private String applyDepartment;

    /**
     * 领用用途
     */
    @TableField("fk_yd_requisitionuse")
    private String requisitionUse;

    /**
     * 退单关联订单是否不存在
     * 0-存在 1-不存在
     */
    @TableField("fk_yd_relatedordernotexis")
    private String relatedOrderNotExists;

    // 销售组织仓库不存在
    @TableField("fk_yd_salorgstocknotexist")
    private Integer saleOrgStockNotExist;

    /**
     * 库存组织仓库是否不存在 0-存在 1-不存在
     */
    @TableField("fk_yd_invorgstocknotexist")
    private Integer invOrgStockNotExist;

    /**
     * 销售组织是否不存在 0-存在 1-不存在
     */
    @TableField("fk_yd_saleorgnotexist")
    private Integer saleOrgNotExist;

    /**
     * 库存组织是否不存在 0-存在 1-不存在
     */
    @TableField("fk_yd_invorgnotexist")
    private Integer invOrgNotExist;

    // // 库存组织
    // @TableField("fk_yd_invorgid")
    // private Long invOrgId;

    /**
     * 成本中心
     */
    @TableField("fk_yd_costcenter")
    private String costCenter;
} 
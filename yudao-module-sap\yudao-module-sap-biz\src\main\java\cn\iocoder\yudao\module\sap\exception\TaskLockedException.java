package cn.iocoder.yudao.module.sap.exception;

/**
 * 任务锁获取失败异常
 * 当分布式锁获取失败时抛出此异常，表明任务已被其他实例执行
 */
public class TaskLockedException extends RuntimeException {
    
    /**
     * 任务名称
     */
    private final String taskName;
    
    /**
     * 锁键名
     */
    private final String lockKey;
    
    /**
     * 构造方法
     * 
     * @param taskName 任务名称
     * @param lockKey 锁键名
     * @param message 异常信息
     */
    public TaskLockedException(String taskName, String lockKey, String message) {
        super(message);
        this.taskName = taskName;
        this.lockKey = lockKey;
    }
    
    /**
     * 构造方法
     * 
     * @param taskName 任务名称
     * @param lockKey 锁键名
     * @param message 异常信息
     * @param cause 原因异常
     */
    public TaskLockedException(String taskName, String lockKey, String message, Throwable cause) {
        super(message, cause);
        this.taskName = taskName;
        this.lockKey = lockKey;
    }
    
    /**
     * 获取任务名称
     * 
     * @return 任务名称
     */
    public String getTaskName() {
        return taskName;
    }
    
    /**
     * 获取锁键名
     * 
     * @return 锁键名
     */
    public String getLockKey() {
        return lockKey;
    }
} 
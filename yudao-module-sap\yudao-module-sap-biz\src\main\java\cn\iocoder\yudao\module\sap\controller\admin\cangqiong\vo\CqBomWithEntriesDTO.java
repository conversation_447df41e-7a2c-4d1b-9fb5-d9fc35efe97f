package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomEntryDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 苍穹销售BOM主表与子表组合DTO
 * 
 * 用于组合BOM主表信息和对应的子表明细信息，便于统一返回和处理
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CqBomWithEntriesDTO {

    /**
     * BOM主表信息
     */
    private CqBomDO bomInfo;
    
    /**
     * BOM子表信息列表
     */
    private List<CqBomEntryDO> entries;
    
    /**
     * 静态构建方法
     * 
     * @param bomInfo BOM主表信息
     * @param entries BOM子表信息列表
     * @return DTO对象
     */
    public static CqBomWithEntriesDTO build(CqBomDO bomInfo, List<CqBomEntryDO> entries) {
        return new CqBomWithEntriesDTO(bomInfo, entries);
    }
} 
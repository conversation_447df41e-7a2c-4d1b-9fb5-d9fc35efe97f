package cn.iocoder.yudao.module.sap.model.cangqiong;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 其他出库单保存请求 DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OtherOutboundSaveReqDTO {
    /**
     * ID
     */
    @SerializedName("id")
    private String id;
    
    /**
     * 单据编号
     */
    @SerializedName("billno")
    private String billno;
    
    /**
     * 业务日期
     */
    @SerializedName("biztime")
    private String biztime;
    
    /**
     * 备注
     */
    @SerializedName("comment")
    private String comment;
    
    /**
     * 是否虚单
     */
    @SerializedName("isvirtualbill")
    private Boolean isvirtualbill;
    
    /**
     * 单据生成类型 0:手工生成, 1:导入生成, 2:后台生成, 3:webApi生成
     */
    @SerializedName("billcretype")
    private String billcretype;
    
    /**
     * 记账日期
     */
    @SerializedName("bookdate")
    private String bookdate;
    
    /**
     * 异步状态 A:处理中, B:已完成
     */
    @SerializedName("asyncstatus")
    private String asyncstatus;
    
    /**
     * 已被冲销
     */
    @SerializedName("ischargeoffed")
    private Boolean ischargeoffed;
    
    /**
     * 冲销
     */
    @SerializedName("ischargeoff")
    private Boolean ischargeoff;
    
    /**
     * 计量单位来源 MAINBILLUNIT:核心单据计量单位, BIZUNIT:默认业务单位
     */
    @SerializedName("unitsrctype")
    private String unitsrctype;
    
    /**
     * 运费
     */
    @SerializedName("yd_amountfield_yf")
    private String yd_amountfield_yf;
    
    /**
     * EAS保存失败原因
     */
    @SerializedName("yd_textareafield01")
    private String yd_textareafield01;
    
    /**
     * EAS提交失败原因
     */
    @SerializedName("yd_sftj")
    private String yd_sftj;
    
    /**
     * EAS审核失败原因
     */
    @SerializedName("yd_textareafield03")
    private String yd_textareafield03;
    
    /**
     * EAS是否保存成功
     */
    @SerializedName("yd_checkboxfield01")
    private Boolean yd_checkboxfield01;
    
    /**
     * EAS是否提交成功
     */
    @SerializedName("yd_checkboxfield02")
    private Boolean yd_checkboxfield02;
    
    /**
     * EAS是否审核成功
     */
    @SerializedName("yd_checkboxfield03")
    private Boolean yd_checkboxfield03;
    
    /**
     * 苍穹是否审核成功
     */
    @SerializedName("yd_checkboxfield04")
    private Boolean yd_checkboxfield04;
    
    /**
     * 苍穹审核失败原因
     */
    @SerializedName("yd_textareafield")
    private String yd_textareafield;
    
    /**
     * 同步来源系统 1:苍穹财务系统-E3, 2:苍穹财务系统-旺店通, 3:苍穹财务系统-旺店通
     */
    @SerializedName("yd_tbly")
    private String yd_tbly;
    
    /**
     * 流程判断是否重复
     */
    @SerializedName("yd_yc")
    private Boolean yd_yc;
    
    /**
     * 结算类型 1:正向, 0:逆向
     */
    @SerializedName("yd_settletype")
    private String yd_settletype;
    
    /**
     * 是否已发起结算
     */
    @SerializedName("yd_isbeginsettle")
    private Boolean yd_isbeginsettle;
    
    /**
     * 是否已结算完成
     */
    @SerializedName("yd_issettled")
    private Boolean yd_issettled;
    
    /**
     * 是否首笔结算单
     */
    @SerializedName("yd_firstinternalbill")
    private Boolean yd_firstinternalbill;
    
    /**
     * 失败信息
     */
    @SerializedName("yd_settleerror")
    private String yd_settleerror;
    
    /**
     * 是否已重置批次
     */
    @SerializedName("yd_beresetlot")
    private Boolean yd_beresetlot;
    
    /**
     * 是否链路已推送EAS
     */
    @SerializedName("yd_istoeas")
    private Boolean yd_istoeas;
    
    /**
     * 销售业务类型 1:非销
     */
    @SerializedName("yd_biztype")
    private String yd_biztype;
    
    /**
     * 是否委外结算
     */
    @SerializedName("yd_isoutsettle")
    private Boolean yd_isoutsettle;
    
    /**
     * 来源单据类型 1:发货明细
     */
    @SerializedName("yd_sourcebilltype")
    private String yd_sourcebilltype;
    
    /**
     * SAP非销单号
     */
    @SerializedName("yd_sapnotsalebillno")
    private String yd_sapnotsalebillno;
    
    /**
     * 库存操作类型
     */
    @SerializedName("yd_invoptype")
    private String yd_invoptype;
    
    /**
     * 来源系统
     */
    @SerializedName("yd_sourcesys")
    private String yd_sourcesys;
    
    /**
     * 是否品牌汇总
     */
    @SerializedName("yd_isbrandsum")
    private Boolean yd_isbrandsum;
    
    /**
     * 发放类型
     */
    @SerializedName("yd_zissuetype")
    private String yd_zissuetype;

    /**
     * 预留单号
     */
    @SerializedName("yd_reservedorderno")
    private String yd_reservedorderno;
    
    /**
     * 物料明细列表
     */
    @SerializedName("billentry")
    private List<BillEntry> billentry;
    
    /**
     * 发货明细单号列表
     */
    @SerializedName("yd_entryentity")
    private List<EntryEntity> yd_entryentity;
    
    /**
     * 库存组织.编码
     */
    @SerializedName("org_number")
    private String org_number;
    
    /**
     * 单据类型.编码
     */
    @SerializedName("billtype_number")
    private String billtype_number;
    
    /**
     * 业务组织.编码
     */
    @SerializedName("bizorg_number")
    private String bizorg_number;
    
    /**
     * 业务部门.编码
     */
    @SerializedName("bizdept_number")
    private String bizdept_number;
    
    /**
     * 库管部门.编码
     */
    @SerializedName("dept_number")
    private String dept_number;
    
    /**
     * 库管组.编码
     */
    @SerializedName("operatorgroup_number")
    private String operatorgroup_number;
    
    /**
     * 库管员.业务员名称
     */
    @SerializedName("operator_operatorname")
    private String operator_operatorname;
    
    /**
     * 业务组.编码
     */
    @SerializedName("bizoperatorgroup_number")
    private String bizoperatorgroup_number;
    
    /**
     * 业务员.业务员名称
     */
    @SerializedName("bizoperator_operatorname")
    private String bizoperator_operatorname;
    
    /**
     * 业务类型.编码
     */
    @SerializedName("biztype_number")
    private String biztype_number;
    
    /**
     * 库存事务.编码
     */
    @SerializedName("invscheme_number")
    private String invscheme_number;
    
    /**
     * 客户.编码
     */
    @SerializedName("customer_number")
    private String customer_number;
    
    /**
     * 币别.货币代码
     */
    @SerializedName("settlecurrency_number")
    private String settlecurrency_number;
    
    /**
     * 其他出库区域.编码
     */
    @SerializedName("yd_quyu_number")
    private String yd_quyu_number;
    
    /**
     * 原库存组织.编码
     */
    @SerializedName("yd_oriorg_number")
    private String yd_oriorg_number;
    
    /**
     * 销售组织.编码
     */
    @SerializedName("yd_bizorg_number")
    private String yd_bizorg_number;
    
    /**
     * 是否退货
     */
    @SerializedName("yd_isreturn")
    private Boolean yd_isreturn;
    
    /**
     * 物料明细实体类
     */
    @Data
    @NoArgsConstructor
    public static class BillEntry {
        /**
         * 物料明细.id
         */
        @SerializedName("id")
        private String id;
        
        /**
         * 行类型.编码
         */
        @SerializedName("linetype_number")
        private String linetype_number;
        
        /**
         * 物料.编码
         */
        @SerializedName("material_number")
        private String material_number;
        
        /**
         * 计量单位.编码
         */
        @SerializedName("unit_number")
        private String unit_number;
        
        /**
         * 出库状态.编码
         */
        @SerializedName("outinvstatus_number")
        private String outinvstatus_number;
        
        /**
         * 出库库存类型.编码
         */
        @SerializedName("outinvtype_number")
        private String outinvtype_number;
        
        /**
         * 出库货主.编码
         */
        @SerializedName("outowner_number")
        private String outowner_number;
        
        /**
         * 出库保管者.编码
         */
        @SerializedName("outkeeper_number")
        private String outkeeper_number;
        
        /**
         * 基本计量单位.编码
         */
        @SerializedName("baseunit_number")
        private String baseunit_number;
        
        /**
         * 仓库.编码
         */
        @SerializedName("warehouse_number")
        private String warehouse_number;
        
        /**
         * 物料明细.分录行号
         */
        @SerializedName("seq")
        private Integer seq;
        
        /**
         * 物料明细.数量
         */
        @SerializedName("qty")
        private BigDecimal qty;
        
        /**
         * 物料明细.辅助数量
         */
        @SerializedName("qtyunit2nd")
        private String qtyunit2nd;
        
        /**
         * 物料明细.基本数量
         */
        @SerializedName("baseqty")
        private BigDecimal baseqty;
        
        /**
         * 物料明细.生产日期
         */
        @SerializedName("producedate")
        private String producedate;
        
        /**
         * 物料明细.到期日期
         */
        @SerializedName("expirydate")
        private String expirydate;
        
        /**
         * 物料明细.备注
         */
        @SerializedName("entrycomment")
        private String entrycomment;
        
        /**
         * 物料明细.入库货主类型 bos_org:核算组织, bd_supplier:供应商, bd_customer:客户
         */
        @SerializedName("ownertype")
        private String ownertype;
        
        /**
         * 物料明细.入库保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
         */
        @SerializedName("keepertype")
        private String keepertype;
        
        /**
         * 物料明细.批号
         */
        @SerializedName("lotnumber")
        private String lotnumber;
        
        /**
         * 物料明细.出库货主类型 bos_org:核算组织, bd_supplier:供应商, bd_customer:客户
         */
        @SerializedName("outownertype")
        private String outownertype;
        
        /**
         * 物料明细.出库保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
         */
        @SerializedName("outkeepertype")
        private String outkeepertype;
        
        /**
         * 物料明细.物流单据
         */
        @SerializedName("logisticsbill")
        private Boolean logisticsbill;
        
        /**
         * 物料明细.不更新库存字段
         */
        @SerializedName("noupdateinvfields")
        private String noupdateinvfields;
        
        /**
         * 物料明细.物料名称
         */
        @SerializedName("materialname")
        private String materialname;
        
        /**
         * 物料明细.单价
         */
        @SerializedName("price")
        private String price;
        
        /**
         * 物料明细.金额
         */
        @SerializedName("amount")
        private String amount;
        
        /**
         * 物料明细.赠品
         */
        @SerializedName("yd_checkboxfield")
        private Boolean yd_checkboxfield;
        
        /**
         * 物料明细.店铺
         */
        @SerializedName("yd_store")
        private String yd_store;
        
        /**
         * 物料明细.批次号
         */
        @SerializedName("yd_ph")
        private String yd_ph;
        
        /**
         * 物料明细.生产日期
         */
        @SerializedName("yd_scrq")
        private String yd_scrq;
        
        /**
         * 物料明细.到期日
         */
        @SerializedName("yd_dqr")
        private String yd_dqr;
        
        /**
         * 物料明细.序列号
         */
        @SerializedName("serialnumber")
        private String serialnumber;
        
        /**
         * 物料明细.是否BOM
         */
        @SerializedName("yd_isbom")
        private Boolean yd_isbom;
        
        /**
         * 物料明细.BOM编号
         */
        @SerializedName("yd_bomnum")
        private String yd_bomnum;
        
        /**
         * 物料明细.库存组织仓库.编码
         */
        @SerializedName("yd_invorgstock_number")
        private String yd_invorgstock_number;

        /**
         * 物料明细.库存组织.编码
         */
        @SerializedName("yd_invorg_number")
        private String ydInvorgNumber;

        /**
         * 物料类型编码
         * 非必填，物料组的编码
         */
        @SerializedName("yd_matgroup_number")
        private String ydMatgroupNumber;

        /**
         * 产品类型编码
         * 非必填，产品类型的编码
         */
        @SerializedName("yd_producttype")
        private String ydProducttype;

        /**
         * 成本中心编码
         */
        @SerializedName("yd_costcenter_number")
        private String ydCostcenterNumber;

        /**
         * 品牌编码
         */
        @SerializedName("yd_basedatafield_pp_number")
        private String ydBasedatafieldPpNumber;
    }
    
    /**
     * 发货明细单号实体类
     */
    @Data
    @NoArgsConstructor
    public static class EntryEntity {
        /**
         * 发货明细单号.id
         */
        @SerializedName("id")
        private String id;
        
        /**
         * 发货明细单号.发货明细单号
         */
        @SerializedName("yd_textfield_fhmxdh")
        private String yd_textfield_fhmxdh;

        /**
         * 发货明细单号.行号
         */
        @SerializedName("yd_rowno")
        private String yd_rowno;

        /**
         * 发货明细单号.行ID
         */
        @SerializedName("yd_rowid")
        private String yd_rowid;

    }
} 
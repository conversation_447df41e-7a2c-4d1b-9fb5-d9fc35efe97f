package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.sap.config.CangQiongConfig;
import cn.iocoder.yudao.module.sap.model.delivery.DeliveryDetailEntryVO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliveryDetailSaveRespVO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliveryDetailVO;
import cn.iocoder.yudao.module.sap.model.e3.E3OrderListGetResponse;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDeliveryDetailService;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailService;
import cn.iocoder.yudao.module.sap.service.e3.E3OrderService;
import cn.iocoder.yudao.module.sap.utils.BillNoUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 发货明细表 Service 实现类
 */
@Service
@Slf4j
public class DeliveryDetailServiceImpl implements DeliveryDetailService {

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final Gson GSON = new Gson();
    
    // 重试相关常量
    private static final int MAX_RETRY_TIMES = 3; // 最大重试次数
    private static final long RETRY_DELAY_MS = 2000; // 重试延迟时间，单位毫秒
    
    @Autowired
    private CangQiongConfig cangQiongConfig;
    
    @Autowired
    private CangQiongApiServiceImpl cangQiongApiService;
    
    @Autowired
    private E3OrderService e3OrderService;
    
    @Autowired
    private CqDeliveryDetailService cqDeliveryDetailService;
    
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    @Override
    public DeliveryDetailSaveRespVO saveDeliveryDetails(List<DeliveryDetailVO> deliveryDetails) throws IOException {
        log.info("[saveDeliveryDetails][开始保存发货明细表，数量: {}, 配置信息：baseUrl={}]", 
                deliveryDetails.size(), cangQiongConfig.getBaseUrl());
        
        // 检查URL配置
        String baseUrl = cangQiongConfig.getUrl();
        if (StrUtil.isBlank(baseUrl)) {
            log.error("[saveDeliveryDetails][仓穹API的URL配置为空]");
            throw new ServiceException("仓穹API的URL配置为空");
        }
        
        // 获取缓存的令牌
        String accessToken = cangQiongApiService.getTokenWithCache();
        
        // 构建请求数据
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("data", deliveryDetails);
        
        // 使用 FastJSON 进行序列化
        String requestBody = JSONObject.toJSONString(requestData);
        
        // 添加重试机制
        IOException lastException = null;
        for (int retry = 0; retry < MAX_RETRY_TIMES; retry++) {
            try {
                // 如果非首次尝试，记录重试信息
                if (retry > 0) {
                    log.info("[saveDeliveryDetails][第 {} 次重试保存发货明细表]", retry);
                    // 增加重试延迟，避免立即重试
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("[saveDeliveryDetails][重试等待被中断]", e);
                    }
                }
                
                // 创建POST请求
                Request request = new Request.Builder()
                        .url(baseUrl + "/kapi/v2/yd/im/yd_fhmxb/ec_yd_fhmxb_save")
                        .post(RequestBody.create(requestBody, JSON))
                        .addHeader("accesstoken", accessToken)
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Idempotency-Key", String.valueOf(IdUtil.getSnowflakeNextId())) // 幂等性参数
                        .build();
                
                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new ServiceException("保存发货明细表失败，状态码：" + response.code());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("[saveDeliveryDetails][保存发货明细表响应：{}]", responseBody);
                    
                    try {
                        JSONObject jsonResp = JSONObject.parseObject(responseBody);
                        
                        // 检查响应状态
                        if (!jsonResp.getBooleanValue("status")) {
                            log.error("[saveDeliveryDetails][保存发货明细表失败：{}]", responseBody);
                            throw new ServiceException("保存发货明细表失败：" + jsonResp.getString("message"));
                        }
                        
                        // 返回data对象
                        return JSONObject.parseObject(jsonResp.getJSONObject("data").toJSONString(), 
                                DeliveryDetailSaveRespVO.class);
                    } catch (Exception e) {
                        log.error("[saveDeliveryDetails][解析响应失败：{}]", responseBody, e);
                        throw new ServiceException("解析响应失败：" + e.getMessage());
                    }
                }
            } catch (IOException e) {
                // 捕获IO异常（连接超时等），准备重试
                lastException = e;
                log.error("[saveDeliveryDetails][请求异常，准备第 {} 次重试]", retry + 1, e);
            }
        }
        
        // 所有重试都失败了，抛出最后一次的异常
        log.error("[saveDeliveryDetails][重试 {} 次后仍然失败]", MAX_RETRY_TIMES);
        if (lastException != null) {
            throw lastException;
        } else {
            throw new IOException("保存发货明细表失败，未知原因");
        }
    }
    
    /**
     * 从E3获取订单并保存为发货明细
     * 修改为接收公司名称、订单编号和商店编码参数，自动设置时间范围为上个月到今天，并按天降序遍历
     *
     * @param company 公司名称
     * @param orderSn 订单编号，可选
     * @param sdCode 商店编码，可选
     * @return 保存结果
     * @throws IOException 请求异常
     */
    @Override
    public DeliveryDetailSaveRespVO syncE3OrdersToDeliveryDetails(String company, String orderSn, String sdCode) throws IOException {
        // 记录同步开始的日志，包含公司名称、订单编号和商店编码信息
        log.info("[syncE3OrdersToDeliveryDetails][开始同步E3订单到发货明细表，公司: {}, 订单编号: {}, 商店编码: {}]", company, orderSn, sdCode);

        // 如果提供了订单编号或商店编码，则执行特定订单的同步
        if (StrUtil.isNotBlank(orderSn) || StrUtil.isNotBlank(sdCode)) {
            return syncSpecificE3Orders(company, orderSn, sdCode);
        }

        // 初始化同步的起始日期（上个月1号）
        Date startDate = initSyncStartDate();
        // 按日期范围同步订单（从当前日期到起始日期，按天倒序遍历）
        DeliveryDetailSaveRespVO result = syncOrdersByDateRange(company, orderSn, sdCode, startDate);
        // 记录同步完成的日志，包含成功和失败的数量
        log.info("[syncE3OrdersToDeliveryDetails][同步完成，成功: {}, 失败: {}]", result.getSuccessCount(), result.getFailCount());
        // 返回同步结果
        return result;
    }
    
    /**
     * 初始化同步的起始日期
     * 设置为上个月的1号0点0分0秒
     *
     * @return 起始日期
     */
    private Date initSyncStartDate() {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.add(Calendar.MONTH, -1); // 上个月
        startCalendar.set(Calendar.DAY_OF_MONTH, 1); // 1号
        startCalendar.set(Calendar.HOUR_OF_DAY, 0); // 0点
        startCalendar.set(Calendar.MINUTE, 0); // 0分
        startCalendar.set(Calendar.SECOND, 0); // 0秒
        startCalendar.set(Calendar.MILLISECOND, 0); // 0毫秒
        return startCalendar.getTime();
    }

    /**
     * 按日期范围同步订单
     * 从当前日期开始，按天倒序遍历到起始日期，每天分别同步订单
     *
     * @param company 公司名称
     * @param orderSn 订单编号，可选
     * @param sdCode 商店编码，可选
     * @param startDate 起始日期
     * @return 同步结果统计
     */
    private DeliveryDetailSaveRespVO syncOrdersByDateRange(String company, String orderSn, String sdCode, Date startDate) {
        // 初始化最终结果对象
        DeliveryDetailSaveRespVO finalResult = new DeliveryDetailSaveRespVO();
        finalResult.setSuccessCount(0);
        finalResult.setFailCount(0);

        // 初始化日期格式化工具
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar currentDay = Calendar.getInstance();

        // 从当前日期开始，按天倒序遍历到起始日期
        while (currentDay.getTime().after(startDate) || dayFormat.format(currentDay.getTime()).equals(dayFormat.format(startDate))) {
            // 同步单天的订单
            DeliveryDetailSaveRespVO dayResult = syncOrdersForSingleDay(company, orderSn, sdCode, currentDay, sdf, dayFormat);
            // 累加成功和失败数量
            finalResult.setSuccessCount(finalResult.getSuccessCount() + dayResult.getSuccessCount());
            finalResult.setFailCount(finalResult.getFailCount() + dayResult.getFailCount());
            // 累加跳过数量（如果有）
            if (dayResult.getSkipCount() != null) {
                finalResult.setSkipCount((finalResult.getSkipCount() == null ? 0 : finalResult.getSkipCount()) + dayResult.getSkipCount());
            }
            // 日期减一天
            currentDay.add(Calendar.DAY_OF_MONTH, -1);
        }
        return finalResult;
    }

    /**
     * 同步单天的订单数据
     * 设置当天的开始和结束时间，并分页查询处理
     *
     * @param company 公司名称
     * @param orderSn 订单编号，可选
     * @param sdCode 商店编码，可选
     * @param day 当前处理的日期
     * @param sdf 日期时间格式化工具
     * @param dayFormat 日期格式化工具
     * @return 当天同步结果统计
     */
    private DeliveryDetailSaveRespVO syncOrdersForSingleDay(String company, String orderSn, String sdCode, Calendar day, SimpleDateFormat sdf, SimpleDateFormat dayFormat) {
        // 初始化当天结果对象
        DeliveryDetailSaveRespVO dayResult = new DeliveryDetailSaveRespVO();
        dayResult.setSuccessCount(0);
        dayResult.setFailCount(0);

        // 设置当天开始时间：0点0分0秒0毫秒
        Calendar dayStart = (Calendar) day.clone();
        dayStart.set(Calendar.HOUR_OF_DAY, 0);
        dayStart.set(Calendar.MINUTE, 0);
        dayStart.set(Calendar.SECOND, 0);
        dayStart.set(Calendar.MILLISECOND, 0);

        // 设置当天结束时间：23点59分59秒999毫秒
        Calendar dayEnd = (Calendar) day.clone();
        dayEnd.set(Calendar.HOUR_OF_DAY, 23);
        dayEnd.set(Calendar.MINUTE, 59);
        dayEnd.set(Calendar.SECOND, 59);
        dayEnd.set(Calendar.MILLISECOND, 999);

        // 格式化时间字符串
        String dayStartStr = sdf.format(dayStart.getTime());
        String dayEndStr = sdf.format(dayEnd.getTime());

        log.info("[syncE3OrdersToDeliveryDetails][处理日期: {}, 时间范围: {} 至 {}]", dayFormat.format(day.getTime()), dayStartStr, dayEndStr);

        // 分页参数初始化
        int pageNo = 1;
        int pageSize = 50;
        boolean hasMoreData = true;

        // 循环处理所有分页数据
        while (hasMoreData) {
            // 同步当前页的订单
            DeliveryDetailSaveRespVO pageResult = syncOrdersByPage(company, orderSn, sdCode, dayStartStr, dayEndStr, dayFormat, pageNo, pageSize);
            // 累加成功和失败数量
            dayResult.setSuccessCount(dayResult.getSuccessCount() + pageResult.getSuccessCount());
            dayResult.setFailCount(dayResult.getFailCount() + pageResult.getFailCount());
            // 累加跳过数量（如果有）
            if (pageResult.getSkipCount() != null) {
                dayResult.setSkipCount((dayResult.getSkipCount() == null ? 0 : dayResult.getSkipCount()) + pageResult.getSkipCount());
            }
            // 判断是否还有更多数据：当前页处理的数据量等于或超过页大小，说明可能还有下一页
            hasMoreData = pageResult.getSuccessCount() + pageResult.getFailCount() > 0 && pageResult.getSuccessCount() + pageResult.getFailCount() >= pageSize;
            pageNo++;
        }
        return dayResult;
    }

    /**
     * 同步单页订单数据
     * 查询指定时间范围内的订单，并转换为发货明细保存
     *
     * @param company 公司名称
     * @param orderSn 订单编号，可选
     * @param sdCode 商店编码，可选
     * @param dayStartStr 开始时间字符串
     * @param dayEndStr 结束时间字符串
     * @param dayFormat 日期格式化工具
     * @param pageNo 当前页码
     * @param pageSize 每页大小
     * @return 当前页同步结果统计
     */
    private DeliveryDetailSaveRespVO syncOrdersByPage(String company, String orderSn, String sdCode, String dayStartStr, String dayEndStr, SimpleDateFormat dayFormat, int pageNo, int pageSize) {
        // 初始化页结果对象
        DeliveryDetailSaveRespVO pageResult = new DeliveryDetailSaveRespVO();
        pageResult.setSuccessCount(0);
        pageResult.setFailCount(0);

        try {
            log.info("[syncE3OrdersToDeliveryDetails][日期范围: {} - {}, 第 {} 页数据，每页 {} 条]", dayStartStr, dayEndStr, pageNo, pageSize);

            // 调用E3订单服务获取订单列表
            E3OrderListGetResponse orderListResponse = e3OrderService.getOrderList(
                    company, dayStartStr, dayEndStr, pageNo, pageSize, orderSn, null, sdCode);

            // 检查是否获取到订单数据
            if (orderListResponse == null || orderListResponse.getOrderListGets() == null || orderListResponse.getOrderListGets().isEmpty()) {
                log.info("[syncE3OrdersToDeliveryDetails][日期范围: {} - {}, 第 {} 页无订单数据]", dayStartStr, dayEndStr, pageNo);
                return pageResult;
            }

            // 将E3订单转换为发货明细
            List<DeliveryDetailVO> deliveryDetails = convertE3OrdersToDeliveryDetails(company, orderListResponse.getOrderListGets());
            if (deliveryDetails.isEmpty()) {
                log.info("[syncE3OrdersToDeliveryDetails][日期范围: {} - {}, 第 {} 页无可转换订单]", dayStartStr, dayEndStr, pageNo);
                return pageResult;
            }

            // 按批次处理发货明细，每批次1条
            int batchSize = 1;
            for (int i = 0; i < deliveryDetails.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, deliveryDetails.size());
                List<DeliveryDetailVO> batchDetails = deliveryDetails.subList(i, endIndex);
                processBatch(batchDetails, pageResult, dayStartStr, pageNo, i / batchSize + 1, deliveryDetails.size(), batchSize);
            }

        } catch (Exception e) {
            log.error("[syncE3OrdersToDeliveryDetails][日期范围: {} - {}, 第 {} 页异常]", dayStartStr, dayEndStr, pageNo, e);
        }

        return pageResult;
    }

    /**
     * 处理一批发货明细
     * 检查是否已存在，过滤后保存不重复的数据
     *
     * @param batchDetails 待处理的发货明细批次
     * @param result 结果统计对象
     * @param dayStr 日期字符串
     * @param pageNo 当前页码
     * @param batchIndex 当前批次索引
     * @param totalSize 总数据量
     * @param batchSize 批次大小
     */
    private void processBatch(List<DeliveryDetailVO> batchDetails, DeliveryDetailSaveRespVO result, String dayStr, int pageNo, int batchIndex, int totalSize, int batchSize) {
        try {
            log.info("[syncE3OrdersToDeliveryDetails][日期: {}, 第 {} 页，批次 {}/{}，批次大小 {}]", dayStr, pageNo, batchIndex, (int) Math.ceil((double) totalSize / batchSize), batchDetails.size());

            // 过滤已存在的发货明细
            List<DeliveryDetailVO> filteredBatchDetails = new ArrayList<>();
            for (DeliveryDetailVO detail : batchDetails) {
                boolean exists = isDeliveryDetailExists(detail);
                if (exists) {
                    log.info("[syncE3OrdersToDeliveryDetails][重复数据，跳过，订单编号: {}, 平台: {}, 是否退货: {}, 店铺编号: {}]",
                            detail.getYdTextfieldDdbh(),
                            detail.getYdCombofieldPt(),
                            detail.getYdCheckboxfieldTh() ? "是" : "否",
                            detail.getYdTextfieldDpbh());
                    result.setSkipCount(result.getSkipCount() != null ? result.getSkipCount() + 1 : 1);
                } else {
                    filteredBatchDetails.add(detail);
                }
            }

            // 如果所有数据都已存在，直接返回
            if (filteredBatchDetails.isEmpty()) {
                log.info("[syncE3OrdersToDeliveryDetails][批次所有数据已存在，跳过保存]");
                return;
            }

            // 保存过滤后的发货明细
            DeliveryDetailSaveRespVO batchResult = saveDeliveryDetails(filteredBatchDetails);

            // 累加成功和失败数量
            if (batchResult.getSuccessCount() != null) {
                result.setSuccessCount(result.getSuccessCount() + batchResult.getSuccessCount());
            }
            if (batchResult.getFailCount() != null) {
                result.setFailCount(result.getFailCount() + batchResult.getFailCount());
            }

        } catch (Exception e) {
            log.error("[syncE3OrdersToDeliveryDetails][日期: {}, 第 {} 页，批次 {} 处理失败]", dayStr, pageNo, batchIndex, e);
            result.setFailCount(result.getFailCount() + batchDetails.size());
        }
    }
    
    /**
     * 同步特定的E3订单
     *
     * @param company 公司名称
     * @param orderSn 订单编号
     * @param sdCode 商店编码
     * @return 保存结果
     * @throws IOException 请求异常
     */
    private DeliveryDetailSaveRespVO syncSpecificE3Orders(String company, String orderSn, String sdCode) throws IOException {
        log.info("[syncSpecificE3Orders][开始同步特定E3订单，公司: {}, 订单编号: {}, 商店编码: {}]", 
                company, orderSn, sdCode);
        
        // 初始化结果对象
        DeliveryDetailSaveRespVO result = new DeliveryDetailSaveRespVO();
        result.setSuccessCount(0);
        result.setFailCount(0);
        
        // 设置时间范围为较大范围，确保能查到订单
        Calendar calendar = Calendar.getInstance();
        Date endDate = calendar.getTime(); // 今天
        
        // 设置为2025年1月1日
        calendar = Calendar.getInstance();
        calendar.set(2025, Calendar.JANUARY, 1, 0, 0, 0);
        Date startDate = calendar.getTime();
        
        // 格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startModified = sdf.format(startDate);
        String endModified = sdf.format(endDate);
        
        // 调用E3订单服务获取特定订单
        E3OrderListGetResponse orderListResponse = e3OrderService.getOrderList(
                company, startModified, endModified, 1, 10,
                orderSn, null, sdCode);
        
        // 检查是否获取到订单
        if (orderListResponse == null || orderListResponse.getOrderListGets() == null || orderListResponse.getOrderListGets().isEmpty()) {
            log.info("[syncSpecificE3Orders][未获取到符合条件的E3订单数据]");
            return result;
        }
        
        log.info("[syncSpecificE3Orders][获取到E3订单数量: {}]", orderListResponse.getOrderListGets().size());
        
        // 将E3订单转换为发货明细
        List<DeliveryDetailVO> deliveryDetails = convertE3OrdersToDeliveryDetails(company, orderListResponse.getOrderListGets());
        
        // 如果没有可转换的订单，直接返回空结果
        if (deliveryDetails.isEmpty()) {
            log.info("[syncSpecificE3Orders][没有可转换的E3订单]");
            return result;
        }
        
        // 过滤掉已存在的发货明细
        List<DeliveryDetailVO> filteredDetails = new ArrayList<>();
        for (DeliveryDetailVO detail : deliveryDetails) {
            if (!isDeliveryDetailExists(detail)) {
                filteredDetails.add(detail);
            } else {
                log.info("[syncSpecificE3Orders][发现重复数据，跳过保存，订单编号: {}, 平台: {}, 是否退货: {}, 店铺编号: {}]", 
                        detail.getYdTextfieldDdbh(), 
                        detail.getYdCombofieldPt(), 
                        detail.getYdCheckboxfieldTh() ? "是" : "否", 
                        detail.getYdTextfieldDpbh());
            }
        }
        if (filteredDetails.isEmpty()) {
            log.info("[syncSpecificE3Orders][所有订单均已存在，无需保存]");
            return result;
        }
        
        // 保存未重复的发货明细
        return saveDeliveryDetails(filteredDetails);
    }

    /**
     * 判断发货明细是否已存在
     */
    private boolean isDeliveryDetailExists(DeliveryDetailVO detail) {
        return cqDeliveryDetailService.existsDeliveryDetailByOrderAndDealAndShop(
                detail.getYdTextfieldDdbh(),  // 订单编号
                detail.getYdDealcode(),       // 交易号
                detail.getYdTextfieldDpbh()   // 店铺编号
        );
    }
    
    /**
     * 将E3订单转换为发货明细
     *
     * @param orders E3订单列表
     * @return 发货明细列表
     */
    private List<DeliveryDetailVO> convertE3OrdersToDeliveryDetails(String company, List<E3OrderListGetResponse.OrderListGet> orders) {
        if (orders == null || orders.isEmpty()) {
            return Collections.emptyList();
        }
        
        log.info("[convertE3OrdersToDeliveryDetails][开始转换E3订单到发货明细表，订单数量: {}]", orders.size());
        List<DeliveryDetailVO> deliveryDetails = new ArrayList<>();
        
        for (E3OrderListGetResponse.OrderListGet order : orders) {
            try {
                // 构建单据编号
                String orderSn = order.getOrderSn(); // 订单编号
                String sdCode = order.getSdCode(); // 商店code
                String billno = BillNoUtils.generateE3OrderBillNo(sdCode, orderSn);
                
                // 处理订单明细（子表数据）
                List<DeliveryDetailEntryVO> entries = createDeliveryDetailEntries(order.getOrderDetailGets());
                
                // 判断明细的ydDetailtotalamount是否存在负数
                boolean hasNegativeAmount = entries.stream()
                        .anyMatch(entry -> entry.getYdDetailtotalamount() != null && 
                                entry.getYdDetailtotalamount().compareTo(BigDecimal.ZERO) < 0);
                if (hasNegativeAmount) {
                    log.warn("[convertE3OrdersToDeliveryDetails][订单{}存在负数金额明细]", order.getOrderSn());
                }
                
                // 创建并填充主表数据
                DeliveryDetailVO detail = createDeliveryDetailMainData(company, order, billno, orderSn, sdCode, hasNegativeAmount);
                
                // 计算明细表运费总和，并更新主表运费
                BigDecimal totalShippingFee = calculateTotalShippingFee(entries);
                detail.setYdDecimalfieldYf(totalShippingFee);
                
                // 计算明细表总金额，并更新主表总金额
                BigDecimal totalAmount = calculateTotalAmount(entries);
                detail.setYdTotalamount(totalAmount);
                
                // 设置明细条目
                detail.setYdEntryentity(entries);
                
                deliveryDetails.add(detail);
            } catch (Exception e) {
                log.error("[convertE3OrdersToDeliveryDetails][转换订单失败：{}]", e.getMessage(), e);
            }
        }
        
        log.info("[convertE3OrdersToDeliveryDetails][转换E3订单到发货明细表完成，转换后数量: {}]", deliveryDetails.size());
        return deliveryDetails;
    }
    
    /**
     * 计算明细表运费总和
     *
     * @param entries 发货明细条目列表
     * @return 运费总和
     */
    private BigDecimal calculateTotalShippingFee(List<DeliveryDetailEntryVO> entries) {
        if (entries == null || entries.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return entries.stream()
                .map(entry -> entry.getYdAvlogisticscost() != null ? entry.getYdAvlogisticscost() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算明细表总金额
     *
     * @param entries 发货明细条目列表
     * @return 总金额
     */
    private BigDecimal calculateTotalAmount(List<DeliveryDetailEntryVO> entries) {
        if (entries == null || entries.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return entries.stream()
                .map(entry -> entry.getYdDetailtotalamount() != null ? entry.getYdDetailtotalamount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 创建发货明细主表数据
     *
     * @param order E3订单
     * @param billno 单据编号
     * @param orderSn 订单编号
     * @param sdCode 商店编码
     * @param hasNegativeAmount 是否存在负数金额明细
     * @return 发货明细主表数据
     */
    private DeliveryDetailVO createDeliveryDetailMainData(String company, E3OrderListGetResponse.OrderListGet order, 
                                                         String billno, String orderSn, String sdCode, boolean hasNegativeAmount) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfrq = new SimpleDateFormat("yyyy-MM-dd");
        
        // 创建发货明细对象
        DeliveryDetailVO detail = new DeliveryDetailVO();
        
        // 设置基本信息
        detail.setBillno(billno);
        
        // 处理日期时间
        processDateFields(detail, order, sdf, sdfrq);
        
        // 设置订单基本信息
        detail.setYdOrdertype("0"); // 订单类型
        detail.setYdCombofieldPt("1"); // 平台(E3:1;旺店通:2;吉客云:3;万里牛:4;麦优E3:5;)
        if(company.equalsIgnoreCase("maiyou") || company.equalsIgnoreCase("baiyue")){
            detail.setYdCombofieldPt("5");
        }
        detail.setYdTextfieldDdbh(orderSn); // 订单编号
        detail.setYdTextfieldDpbh(sdCode); // 店铺编号
        detail.setYdTextfieldCk(order.getFhck()); // 发货仓库

        detail.setYdSettlestatus("1"); // 结算状态
        
        // 处理备注字段，超过50个字符时进行截取
        String orderMsg = order.getOrderMsg();
        // if (orderMsg != null && orderMsg.length() > 50) {
        //     orderMsg = orderMsg.substring(0, 50);
        //     log.info("[createDeliveryDetailMainData][订单备注超长，已截取前50个字符，订单编号: {}]", orderSn);
        // }
        detail.setYdBz(orderMsg); // 备注
        
        detail.setYdDealcode(order.getDealCode()); // 交易号
        detail.setYdLylx(String.valueOf(order.getLylx())); // 来源类型
        detail.setYdSdid(order.getSdId()); // 商店id
        detail.setYdOrderstatus(Integer.valueOf(order.getOrderStatus())); // 订单状态
        detail.setYdShippingcode(order.getShippingCode()); // 快递编码
        detail.setYdShippingname(order.getShippingName()); // 快递名称
        detail.setYdShippingsn(order.getShippingSn()); // 快递单号
        
        // 设置金额相关信息
        processAmountFields(detail, order);
        
        // 设置标志位
        processFlags(detail, order);

        // 设置其他信息
        detail.setYdWeigh(new BigDecimal(order.getWeigh())); // 总重量
        detail.setYdSdname(order.getSdName()); // 店铺名称
        detail.setYdFhckmc(order.getFhckmc()); // 仓库名称
        detail.setYdQdcode(order.getQdCode()); // 渠道代码
        detail.setYdQdname(order.getQdName()); // 渠道名称
        detail.setYdShopOrg(order.getQdName()); // E3店铺组织

        // 设置是否异常
        detail.setYdIserror(hasNegativeAmount);
        // 设置异常原因
        detail.setYdErrorstate(hasNegativeAmount ? "1" : "0"); // 0:无异常, 1:金额为负，请核对E3单据各分录行金额、数量, 2:数量缺漏，请核对E3单据各分录行金额、数量, 3:数量缺漏、金额为负，请核对E3单据各分录行金额、数量
        
        // 设置收货地址信息
        processAddressFields(detail, order);
        
        // 设置商家备注
        String sellerMsg = order.getSellerMsg();
        detail.setYdMerchantremark(sellerMsg); // 商家备注

        // // 处理临时店铺的特殊逻辑
        // if(sdCode.contains("temp")){
        //     detail.setYdIsnotsale(true);// 是否非销售            
        //     // 解析sellerMsg中的信息
        //     processSellerMsgOfMaiyou(detail, sellerMsg);
        // } else if(order.getSdName().contains("非销")){
        //     detail.setYdIsnotsale(true);// 是否非销售
        //     // 解析sellerMsg中的信息
        //     processSellerMsgOfBaiyue(detail, sellerMsg);
        // }

        // 是否退单
        detail.setYdCheckboxfieldTh(false);

        // 预留单号（昵称）
        String userName = order.getUserName();
        if (userName != null && userName.length() > 200) {
            userName = userName.substring(0, 200);
        }
        detail.setYdReservedordernum(userName);
        
        return detail;
    }
    
    /**
     * 处理日期字段
     */
    private void processDateFields(DeliveryDetailVO detail, E3OrderListGetResponse.OrderListGet order, 
                                  SimpleDateFormat sdf, SimpleDateFormat sdfrq) {
        String addTime = order.getAddTime(); // 下单时间
        String payTime = order.getPayTime(); // 支付时间
        String shippingTimeFh = order.getShippingTimeFh(); // 发货时间
        String shippingTimeCk = order.getShippingTimeCk(); // 出库时间
        
        try {
            if (StrUtil.isNotBlank(addTime)) {
                detail.setYdAddtime(sdf.parse(addTime)); // 下单时间
                detail.setYdDatetimefieldXdsj(shippingTimeCk); // 平台发货时间
            }
            
            if (StrUtil.isNotBlank(payTime)) {
                detail.setYdPaytime(sdf.parse(payTime)); // 支付时间
            }
            
            if (StrUtil.isNotBlank(shippingTimeFh)) {
                detail.setYdShippingtimefh(sdf.parse(shippingTimeFh)); // 发货时间
            }
            
            if (StrUtil.isNotBlank(shippingTimeCk)) {
                detail.setYdShippingtimeck(sdf.parse(shippingTimeCk)); // 出库时间
                // 设置发货日期（只取日期部分）
                try {
                    // 先解析完整日期时间
                    Date fullDate = sdf.parse(shippingTimeCk);
                    // 再用日期格式化器格式化为yyyy-MM-dd格式
                    detail.setYdDatefieldFhrq(sdfrq.parse(sdfrq.format(fullDate))); // 发货日期
                } catch (ParseException e) {
                    log.error("[processDateFields][解析发货日期失败：{}]", e.getMessage());
                }
            }
        } catch (ParseException e) {
            log.error("[processDateFields][解析日期时间失败：{}]", e.getMessage());
        }
    }
    
    /**
     * 处理金额相关字段
     */
    private void processAmountFields(DeliveryDetailVO detail, E3OrderListGetResponse.OrderListGet order) {
        // 注意：运费(ydDecimalfieldYf)将在convertE3OrdersToDeliveryDetails方法中根据明细表计算后设置
        // 注意：总金额(ydTotalamount)将在convertE3OrdersToDeliveryDetails方法中根据明细表计算后设置
        
        // 设置已付金额
        if (StrUtil.isNotBlank(order.getPayment())) {
            detail.setYdPayment(new BigDecimal(order.getPayment())); // 已付金额
        }

        // 设置币种
        detail.setYdCurrencyfieldNumber("BB01"); // 币种
    }
    
    /**
     * 处理标志位字段
     */
    private void processFlags(DeliveryDetailVO detail, E3OrderListGetResponse.OrderListGet order) {
        detail.setYdCheckboxfieldSfkp(StrUtil.isNotBlank(order.getInvoiceTitle())); // 是否开票
        detail.setYdCheckboxfieldSfsg(!"0".equals(order.getIsShougong())); // 是否手工
        detail.setYdIssplit("1".equals(order.getIsSplit())); // 是否被拆分
        detail.setYdIssplitnew("1".equals(order.getIsSplitNew())); // 是否拆分子单
        detail.setYdIscombine("1".equals(order.getIsCombine())); // 是否被合并
        detail.setYdIscombinenew("1".equals(order.getIsCombineNew())); // 是否合并新单
        detail.setYdIshh("1".equals(order.getIsHh())); // 是否换货单
        detail.setYdIscopy("1".equals(order.getIsCopy())); // 是否复制单
    }
    
    /**
     * 处理地址相关字段
     */
    private void processAddressFields(DeliveryDetailVO detail, E3OrderListGetResponse.OrderListGet order) {
        detail.setYdProvincename(order.getReceiverProvinceName()); // 省
        detail.setYdCityname(order.getReceiverCityName()); // 市
        detail.setYdDistrictname(order.getReceiverDistrictName()); // 区
        detail.setYdAddress(order.getReceiverAddress()); // 收货地址
    }
    
    /**
     * 创建发货明细子表数据
     *
     * @param orderDetails E3订单明细列表
     * @return 发货明细子表数据列表
     */
    private List<DeliveryDetailEntryVO> createDeliveryDetailEntries(List<E3OrderListGetResponse.OrderDetailGet> orderDetails) {
        List<DeliveryDetailEntryVO> entries = new ArrayList<>();
        
        if (orderDetails == null || orderDetails.isEmpty()) {
            log.info("[createDeliveryDetailEntries][订单明细为空，返回空列表]");
            return entries;
        }
        
        log.info("[createDeliveryDetailEntries][开始处理订单明细，数量: {}]", orderDetails.size());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        for (int i = 0; i < orderDetails.size(); i++) {
            E3OrderListGetResponse.OrderDetailGet orderDetail = orderDetails.get(i);
            try {
                DeliveryDetailEntryVO entry = new DeliveryDetailEntryVO();
                
                // 设置明细ID
                // entry.setId(orderDetail.getId());
                
                // 设置货品编号
                entry.setYdTextfieldHpbh(orderDetail.getSku());
                
                // 设置数量
                entry.setYdDecimalfieldSl(StrUtil.isBlank(orderDetail.getGoodsNumber()) ? 
                        BigDecimal.ZERO : new BigDecimal(orderDetail.getGoodsNumber()));
                entry.setYdE3qty(StrUtil.isBlank(orderDetail.getGoodsNumber()) ? 
                        BigDecimal.ZERO : new BigDecimal(orderDetail.getGoodsNumber()));      
                
                // 设置单价
                entry.setYdDecimalfieldDj(StrUtil.isBlank(orderDetail.getSharePrice()) ? 
                        BigDecimal.ZERO : new BigDecimal(orderDetail.getSharePrice()));
                
                // 设置分摊金额和总金额
                BigDecimal payment = StrUtil.isBlank(orderDetail.getSharePayment()) ? 
                        BigDecimal.ZERO : new BigDecimal(orderDetail.getSharePayment());
                BigDecimal shippingFee = StrUtil.isBlank(orderDetail.getShareShippingFee()) ? 
                        BigDecimal.ZERO : new BigDecimal(orderDetail.getShareShippingFee());
                                
                // 设置分摊金额合计
                entry.setYdShareamount(payment);
                
                // 设置平均物流成本
                entry.setYdAvlogisticscost(shippingFee);
                
                // 计算总金额(分摊金额+物流成本)
                BigDecimal totalAmount = payment.add(shippingFee);
                entry.setYdDecimalfieldZje(totalAmount);
                entry.setYdDetailtotalamount(totalAmount);
                
                // // 设置是否赠品 (总金额为0则为赠品)
                // entry.setYdIsgift(payment.compareTo(BigDecimal.ZERO) == 0);

                // // 设置是否组装品
                // entry.setYdIspropackage("1".equals(orderDetail.getIsCombo()));

                // // 处理批次信息
                // processBatchInfo(entry, orderDetail.getBatchs(), sdf);

                // 设置商品上架价
                entry.setYdShelvesPrice(StrUtil.isBlank(orderDetail.getShopPrice()) ? 
                        BigDecimal.ZERO : new BigDecimal(orderDetail.getShopPrice()));

                // 设置商品总折扣(商品上架单价*数量-均摊价合计)
                entry.setYdTotaldiscountamt(entry.getYdShelvesPrice().multiply(entry.getYdDecimalfieldSl())
                    .subtract(entry.getYdShareamount()));

                entries.add(entry);
                
                log.debug("[createDeliveryDetailEntries][处理订单明细成功，索引: {}, SKU: {}, 数量: {}, 金额: {}]", 
                        i, orderDetail.getSku(), entry.getYdDecimalfieldSl(), entry.getYdDecimalfieldZje());
            } catch (Exception e) {
                log.error("[createDeliveryDetailEntries][处理订单明细失败，索引: {}, SKU: {}]", 
                        i, orderDetail.getSku(), e);
            }
        }
        
        log.info("[createDeliveryDetailEntries][处理订单明细完成，成功处理数量: {}]", entries.size());
        return entries;
    }
    
    // /**
    //  * 处理批次信息
    //  *
    //  * @param entry 发货明细条目
    //  * @param batchs 批次信息列表
    //  * @param sdf 日期格式化器
    //  */
    // private void processBatchInfo(DeliveryDetailEntryVO entry, List<E3OrderListGetResponse.Batch> batchs, SimpleDateFormat sdf) {
    //     if (batchs == null || batchs.isEmpty()) {
    //         return;
    //     }
        
    //     // 这里只取第一个批次信息，如果需要处理多个批次，需要进一步调整
    //     E3OrderListGetResponse.Batch batch = batchs.get(0);
    //     entry.setYdPh(batch.getBatchNo()); // 批号
        
    //     try {
    //         if (StrUtil.isNotBlank(batch.getCreateDate())) {
    //             entry.setYdScrq(sdf.parse(batch.getCreateDate())); // 生产日期
    //         }
            
    //         if (StrUtil.isNotBlank(batch.getExpirationDate())) {
    //             entry.setYdDqr(sdf.parse(batch.getExpirationDate())); // 到期日
    //         }
    //     } catch (ParseException e) {
    //         log.error("[processBatchInfo][解析批次日期时间失败：{}]", e.getMessage());
    //     }
    // }
    
    /**
     * 处理临时店铺的特殊逻辑，解析sellerMsg中的信息
     * 
     * @param detail 发货明细对象
     * @param sellerMsg 商家备注信息
     */
    private void processSellerMsgOfMaiyou(DeliveryDetailVO detail, String sellerMsg) {
        if (StrUtil.isBlank(sellerMsg)) {
            return;
        }
        
        log.info("[processSellerMsg][开始解析商家备注信息: {}]", sellerMsg);
        
        try {
            // 使用$分割各个部分
            String[] parts = sellerMsg.split("\\$");
            
            if (parts.length >= 6) {
                // 设置预算承担公司
                detail.setYdBudgetcompany(parts[1].trim());
                
                // 设置非销店铺
                detail.setYdNotsaleshop(parts[2].trim());
                
                // 设置申请部门
                detail.setYdApplydepart(parts[3].trim());
                
                // 设置领料用途
                detail.setYdRequisitionuse(parts[4].trim());
                
                // 设置预算承担部门
                detail.setYdBudgetdepart(parts[5].trim());
                
                // 设置预算科目
                if (parts.length >= 7) {
                    detail.setYdBudgetaccount(parts[6].trim());
                }
            }
            
            log.info("[processSellerMsg][解析商家备注信息成功]");
        } catch (Exception e) {
            log.error("[processSellerMsg][解析商家备注信息失败: {}]", e.getMessage(), e);
        }
    }

    /**
     * 处理佰悦的特殊逻辑，解析sellerMsg中的信息
     * 
     * @param detail 发货明细对象
     * @param sellerMsg 商家备注信息
     */
    private void processSellerMsgOfBaiyue(DeliveryDetailVO detail, String sellerMsg) {
        // 示例格式： 预算承担公司=广东佰腾药业有限公司$预算承担部门=佰腾电商部$预算项目=销售费用.市场推广费.市场推广费.物料赠品费
        if (StrUtil.isBlank(sellerMsg)) {
            return;
        }
        
        log.info("[processSellerMsgOfBaiyue][开始解析商家备注信息: {}]", sellerMsg);
        
        try {
            // 使用$分割各个部分
            String[] parts = sellerMsg.split("\\$");
            
            for (String part : parts) {
                // 使用=分割键值对
                String[] keyValue = part.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    
                    // 根据键设置对应的值
                    if ("预算承担公司".equals(key)) {
                        detail.setYdBudgetcompany(value);
                    } else if ("预算承担部门".equals(key)) {
                        detail.setYdBudgetdepart(value);
                    } else if ("预算项目".equals(key)) {
                        detail.setYdBudgetaccount(value);
                    }
                }
            }
            
            log.info("[processSellerMsgOfBaiyue][解析商家备注信息成功]");
        } catch (Exception e) {
            log.error("[processSellerMsgOfBaiyue][解析商家备注信息失败: {}]", e.getMessage(), e);
        }
    }
}

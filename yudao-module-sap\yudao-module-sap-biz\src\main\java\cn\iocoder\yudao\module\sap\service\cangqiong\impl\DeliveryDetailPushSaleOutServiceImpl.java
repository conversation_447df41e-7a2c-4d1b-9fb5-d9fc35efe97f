package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMergeOrderFieldsDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMergeOrderRulesConditionEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMergeOrderRulesDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMergeOrderRulesDetailEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMergeOrderRulesWithEntriesDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.org.OrgDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.warehouse.CqBdWarehouseDO;
import cn.iocoder.yudao.module.sap.enums.BillTypeEnum;
import cn.iocoder.yudao.module.sap.model.cangqiong.SalesOutboundSaveSubmitAuditReqDTO;
import cn.iocoder.yudao.module.sap.model.cangqiong.SalesOutboundSaveSubmitAuditReqDTO.BillEntry;
import cn.iocoder.yudao.module.sap.model.cangqiong.SalesOutboundSaveSubmitAuditReqDTO.YdEntryEntity;
import cn.iocoder.yudao.module.sap.model.cangqiong.CangQiongOperationResp;
import cn.iocoder.yudao.module.sap.model.cangqiong.OtherOutboundSaveReqDTO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CangQiongApiService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqCustomerService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqMergeOrderFieldsService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqMergeOrderRulesService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqSqlQueryService;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailPushSaleOutService;
import cn.iocoder.yudao.module.sap.utils.UpstreamBillInfoUtils;
import cn.iocoder.yudao.module.sap.model.cangqiong.UpstreamBillInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.collection.CollUtil;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.stream.Collectors;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.CollectionUtils;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqOrgService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdMaterialService;
import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.measureunit.CqBdMeasureUnitDO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdMeasureUnitService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDeliveryDetailService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdWarehouseService;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqSourceDeliveryDetailEntryDO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailWithSourceEntriesDTO;
import java.util.Objects;
import java.util.Date;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailPushSaleOutParamsDTO;
import cn.iocoder.yudao.module.sap.utils.MergeSqlBuilder;
import cn.iocoder.yudao.module.sap.utils.DingDingUtils;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class DeliveryDetailPushSaleOutServiceImpl implements DeliveryDetailPushSaleOutService {

    /**
     * 表前缀常量
     */
    private static final String PREFIX_FHMX = "tk_yd_fhmx_";
    private static final String PREFIX_FHMXENTRY = "tk_yd_src_fhmxentry_";

    /**
     * 销售组织库存ID字段
     */
    private static final String FIELD_SALE_ORG_STOCK_ID = PREFIX_FHMXENTRY + "fk_yd_salorgstockid";

    /**
     * 库存组织库存ID字段
     */
    private static final String FIELD_INV_ORG_STOCK_ID = PREFIX_FHMXENTRY + "fk_yd_invorgstockid";

    /**
     * 苍穹客户ID字段
     */
    private static final String FIELD_CQ_CUSTOMER_ID = PREFIX_FHMXENTRY + "fk_yd_cqcustomerid";

    /**
     * 下游单据类型字段
     */
    private static final String FIELD_DOWNSTREAM_BILL_TYPE = PREFIX_FHMX + "fk_yd_combofield_xyd";

    /**
     * 单据编号字段
     */
    private static final String FIELD_BILL_NO = PREFIX_FHMX + "fbillno";

    /**
     * 单据ID字段
     */
    private static final String FIELD_FID = PREFIX_FHMX + "fid";

    /**
     * 明细ID字段
     */
    private static final String FIELD_ENTRY_ID = PREFIX_FHMXENTRY + "fentryid";

    /**
     * 发货日期字段
     */
    private static final String FIELD_SHIP_DATE = PREFIX_FHMX + "fk_yd_datefield_fhrq";

    /**
     * 是否退货字段
     */
    private static final String FIELD_IS_RETURN = PREFIX_FHMX + "fk_yd_checkboxfield_th";

    /**
     * 销售组织ID字段
     */
    private static final String FIELD_SALE_ORG_ID = PREFIX_FHMXENTRY + "fk_yd_saleorgid";

    /**
     * 库存组织ID字段
     */
    private static final String FIELD_INV_ORG_ID = PREFIX_FHMXENTRY + "fk_yd_invorgid";

    /**
     * 序号字段
     */
    private static final String FIELD_SEQ = PREFIX_FHMXENTRY + "fseq";

    /**
     * 物料ID字段
     */
    private static final String FIELD_MATERIAL_ID = PREFIX_FHMXENTRY + "fk_yd_materialid";

    /**
     * 数量字段
     */
    private static final String FIELD_QUANTITY = PREFIX_FHMXENTRY + "fk_yd_qty";

    /**
     * 价税合计字段
     */
    private static final String FIELD_TOTAL_TAX_AMOUNT = PREFIX_FHMXENTRY + "fk_yd_totaltaxamount";

    /**
     * 是否赠品字段
     */
    private static final String FIELD_IS_GIFT = PREFIX_FHMXENTRY + "fk_yd_isgift";

    /**
     * 是否组套
     */
    private static final String FIELD_IS_PRO_PACKAGE = PREFIX_FHMXENTRY + "fk_yd_src_ispropackage";

    /**
     * 货品编号(OMS明细)
     */
    private static final String FIELD_OMS_GOODS_NO = PREFIX_FHMXENTRY + "fk_yd_omsgoodsn";

    /**
     * 商品上架单价
     */
    private static final String FIELD_SHELVES_PRICE = PREFIX_FHMXENTRY + "fk_yd_sshelves_price";

    /**
     * 商品总折扣
     */
    private static final String FIELD_TOTAL_DISCOUNT_AMOUNT = PREFIX_FHMXENTRY + "fk_yd_stotaldiscountamt";

    /**
     * 平均物流成本
     */
    private static final String FIELD_AVG_LOGISTICS_COST = PREFIX_FHMXENTRY + "fk_yd_savlogisticscost";

    /**
     * 平台
     */
    private static final String FIELD_PLATFORM = PREFIX_FHMX + "fk_yd_combofield_pt";

    /**
     * 成本中心
     */
    private static final String FIELD_COST_CENTER = PREFIX_FHMX + "fk_yd_costcenter";

    // /**
    //  * 预算科目-预算项目
    //  */
    // private static final String FIELD_BUDGET_ACCOUNT = PREFIX_FHMX + "fk_yd_budgetaccount";

    /**
     * 领用用途-领用类型
     */
    private static final String FIELD_REQUISITION_USE = PREFIX_FHMX + "fk_yd_requisitionuse";

    /**
     * 是否品牌分单
     */
    private static final String FIELD_IS_BRAND_SPLIT_BILL = PREFIX_FHMX + "fk_yd_isbrandsplitbill";

    /**
     * 是否非销
     */
    private static final String FIELD_IS_NOT_SALE = PREFIX_FHMX + "fk_yd_isnotsale";

    /**
     * 订单备注
     */
    private static final String FIELD_ORDER_REMARK = PREFIX_FHMX + "fk_yd_bz";

    /**
     * 均摊金额
     */
    private static final String FIELD_SHARE_AMOUNT = PREFIX_FHMXENTRY + "fk_yd_splitshareamount";

    /**
     * 物料类型
     */
    private static final String FIELD_MATERIAL_TYPE = PREFIX_FHMXENTRY + "fk_yd_matgroupid";

    /**
     * 产品类型
     */
    private static final String FIELD_PRODUCT_TYPE = PREFIX_FHMXENTRY + "fk_yd_producttype";

    /**
     * 预留单号
     */
    private static final String FIELD_RESERVED_ORDER_NO = PREFIX_FHMX + "fk_yd_reservedordernum";

    /**
     * 异常通知相关常量
     */
    private static final int MAX_STACK_TRACE_LINES = 5;
    private static final String EXCEPTION_NOTIFICATION_ROBOT = "exception-notify";

    @Resource
    private CqMergeOrderRulesService mergeOrderRulesService;

    @Resource
    private CqSqlQueryService cqSqlQueryService;

    @Resource
    private CqMergeOrderFieldsService mergeOrderFieldsService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CangQiongApiService cangQiongApiService;

    @Resource
    private CqOrgService cqOrgService;

    @Resource
    private CqCustomerService cqCustomerService;

    @Resource
    private CqBdMaterialService cqBdMaterialService;

    @Resource
    private CqBdWarehouseService cqBdWarehouseService;

    @Resource
    private CqBdMeasureUnitService cqBdMeasureUnitService;

    @Resource
    private CqDeliveryDetailService cqDeliveryDetailService;

    @Resource
    private DingDingUtils dingDingUtils;

    /**
     * 预加载所有基础数据
     * 
     * 该方法用于预加载系统中的各种基础数据映射表，包括组织、客户、物料、仓库和计量单位信息，
     * 以便在后续的业务处理中快速查找相关数据，避免重复数据库查询，提升处理性能。
     * 
     * @param ruleCode 规则编号，用于日志记录中标识当前处理的规则
     * @return BaseDataMaps 包含所有基础数据映射表的封装对象
     */
    private BaseDataMaps preloadBaseData(String ruleCode) {
        // 预加载所有基础数据
        long preloadStartTime = System.currentTimeMillis();
        // 预加载组织信息映射表，用于后续快速查找组织数据
        Map<Long, OrgDO> orgMap = cqOrgService.getAllOrgsMap();
        // 预加载客户信息映射表，用于后续快速查找客户数据
        Map<Long, CqCustomerDO> customerMap = cqCustomerService.getAllCustomersMap();
        // 预加载物料信息映射表，用于后续快速查找物料数据
        Map<Long, CqBdMaterialDO> materialMap = cqBdMaterialService.getAllMaterialsMap();
        // 预加载仓库信息映射表，用于后续快速查找仓库数据
        Map<Long, CqBdWarehouseDO> warehouseMap = cqBdWarehouseService.getAllWarehousesMap();
        // 预加载计量单位信息映射表，用于后续快速查找计量单位数据
        Map<Long, CqBdMeasureUnitDO> unitMap = cqBdMeasureUnitService.getAllMeasureUnitsMap();
        BaseDataMaps baseDataMaps = new BaseDataMaps(orgMap, customerMap, materialMap, warehouseMap, unitMap);
        long preloadEndTime = System.currentTimeMillis();
        log.info("[preloadBaseData][预加载基础数据完成][规则:{}, 耗时={}ms, 组织数={}, 客户数={}, 物料数={}, 仓库数={}, 计量单位数={}]", 
                ruleCode,
                (preloadEndTime - preloadStartTime), 
                orgMap.size(), customerMap.size(), materialMap.size(), warehouseMap.size(), unitMap.size());
        
        return baseDataMaps;
    }

    /**
     * 构建字段标识列表
     * 根据字段ID列表获取合单字段信息，并构建完整的字段路径列表（表名.字段名格式）
     *
     * @param fieldFlagIds 字段ID列表
     * @return 字段标识列表，格式为表名.字段名
     */
    private List<String> buildFieldFlagList(List<Long> fieldFlagIds) {
        // 根据字段ID获取合单字段信息
        List<CqMergeOrderFieldsDO> mergeOrderFieldsList = mergeOrderFieldsService
                .getMergeOrderFieldsByIds(fieldFlagIds);
        if (mergeOrderFieldsList == null || mergeOrderFieldsList.isEmpty()) {
            return Collections.emptyList();
        }

        // 创建一个列表，用于存储合单字段的完整路径（表名_字段名格式）
        List<String> fieldFlagList = new ArrayList<>();
        // 遍历合单字段列表，构建完整的字段路径
        mergeOrderFieldsList.forEach(detailMergeOrderFields -> {
            String tableFlag = detailMergeOrderFields.getTableFlag();  // 表标识
            String fieldFlag = detailMergeOrderFields.getName();  // 获取字段名
            String tableFlag_fieldFlag = (tableFlag + "_" + fieldFlag).toLowerCase();  // 组合成表名_字段名格式并转小写
            fieldFlagList.add(tableFlag_fieldFlag);  // 添加到列表中
        });

        // 对字段标识列表进行去重
        return fieldFlagList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 推送销售出库单（V2版本）
     * 根据合单规则将多个销售出库单合并为一个单据，支持更多自定义配置
     */
    @Override
    public void pushSaleOutV2() throws IOException {
        pushSaleOutV2(null);
    }

    /**
     * 推送销售出库单（V2版本）
     * 根据合单规则将多个销售出库单合并为一个单据，支持更多自定义配置
     *
     * @param billNos 指定需要处理的单据编号列表，为空则处理所有符合条件的单据
     */
    @Override
    public void pushSaleOutV2(List<String> billNos) throws IOException {
        pushSaleOutV2(billNos, null);
    }

    /**
     * 推送销售出库单（V2版本）
     * 根据合单规则将多个销售出库单合并为一个单据，支持更多自定义配置
     *
     * @param billNos 指定需要处理的单据编号列表，为空则处理所有符合条件的单据
     * @param mergeRuleCode 指定需要使用的合单规则编码，为空则使用所有符合条件的合单规则
     */
    @Override
    public void pushSaleOutV2(List<String> billNos, String mergeRuleCode) throws IOException {
        // 记录总开始时间
        long totalStartTime = System.currentTimeMillis();
        
        // 获取所有已审核的合单规则及其条件和明细
        List<CqMergeOrderRulesWithEntriesDTO> mergeOrderRulesWithEntriesDTOList = mergeOrderRulesService
                .getAuditedMergeOrderRulesWithEntries();

        // 如果指定了合单规则编码，过滤符合条件的合单规则
        if (StringUtils.isNotBlank(mergeRuleCode)) {
            mergeOrderRulesWithEntriesDTOList = mergeOrderRulesWithEntriesDTOList.stream()
                    .filter(dto -> {
                        CqMergeOrderRulesDO rule = dto.getMergeOrderRules();
                        return rule != null && mergeRuleCode.equals(rule.getBillNo());
                    })
                    .collect(Collectors.toList());
            
            if (CollUtil.isEmpty(mergeOrderRulesWithEntriesDTOList)) {
                log.warn("[pushSaleOutV2][未找到指定的合单规则][mergeRuleCode={}]", mergeRuleCode);
                return;
            }
        }

        // 遍历每个合单规则
        for (CqMergeOrderRulesWithEntriesDTO mergeOrderRulesWithEntriesDTO : mergeOrderRulesWithEntriesDTOList) {
            CqMergeOrderRulesDO rule = mergeOrderRulesWithEntriesDTO.getMergeOrderRules();
            String ruleNo = rule != null ? rule.getBillNo() : "未知";
            long startTime = System.currentTimeMillis();
            try {
                log.info("[pushSaleOutV2][开始处理合单规则][ruleNo={}]", ruleNo);
                processMergeOrderRule(mergeOrderRulesWithEntriesDTO, billNos);
            } catch (Exception e) {
                log.error("[processMergeOrderRule][处理合并订单异常][ruleNo={}, mergeOrderRulesWithEntriesDTO={}]", ruleNo, mergeOrderRulesWithEntriesDTO, e);
                
                // 发送异常通知到钉钉群
                String additionalInfo = String.format("处理状态: 规则级别异常\n处理进度: 未开始或初始化阶段");
                sendMergeRuleExceptionNotification(ruleNo, e, "processMergeOrderRule", additionalInfo);
                
                // 异常不影响下次循环，继续处理下一组订单
            } finally {
                long endTime = System.currentTimeMillis();
                log.info("[pushSaleOutV2][完成处理合单规则][ruleNo={}, 耗时={}ms]", ruleNo, (endTime - startTime));
            }
        }
        
        // 计算并记录总耗时
        long totalEndTime = System.currentTimeMillis();
        log.info("[pushSaleOutV2][所有合单规则处理完成][总耗时={}ms, 规则数={}]", 
                (totalEndTime - totalStartTime), 
                mergeOrderRulesWithEntriesDTOList.size());
    }

    /**
     * 处理单个合单规则
     *
     * @param mergeOrderRulesWithEntriesDTO 合单规则及其条件和明细
     * @param billNos 指定需要处理的单据编号列表
     * @throws IOException IO异常
     */
    private void processMergeOrderRule(CqMergeOrderRulesWithEntriesDTO mergeOrderRulesWithEntriesDTO, List<String> billNos) throws IOException {
        CqMergeOrderRulesDO mergeOrderRules = mergeOrderRulesWithEntriesDTO.getMergeOrderRules();

        // 获取主合并分组规则条目
        List<CqMergeOrderRulesConditionEntryDO> mainMergeGroupRuleEntries = mergeOrderRulesWithEntriesDTO
                .getConditionEntries();
        if (mainMergeGroupRuleEntries.size() == 0) {
            return;
        }

        // 获取合单规则范围sql脚本，如果为空则跳过
        String mergeRuleScopeSql = mergeOrderRules.getBillScopeTag();
        if (mergeRuleScopeSql == null || mergeRuleScopeSql.equals("")) {
            log.warn("[processMergeOrderRule][合单规则范围sql脚本为空][mergeOrderRules={}]", mergeOrderRules);
            return;
        }

        // 【核心修改】获取所有不重复的发货日期（按天分组）
        List<String> distinctShipDates = getDistinctShipDates(mergeRuleScopeSql);
        if (distinctShipDates.isEmpty()) {
            log.warn("[processMergeOrderRule][未找到有效的发货日期][mergeOrderRules={}]", mergeOrderRules);
            return;
        }

        // 预加载所有基础数据
        BaseDataMaps baseDataMaps = preloadBaseData(mergeOrderRules.getBillNo());

        log.info("[processMergeOrderRule][开始按天分批处理][规则:{}, 天数:{}]", mergeOrderRules.getBillNo(), distinctShipDates.size());

        // 统计总处理信息
        int totalProcessedDays = 0;
        int totalProcessedRecords = 0;
        long totalProcessingTime = 0;

        // 【核心修改】按天遍历处理
        for (int i = 0; i < distinctShipDates.size(); i++) {
            String shipDate = distinctShipDates.get(i);
            long dateStartTime = System.currentTimeMillis();
            
            try {
                log.info("[processMergeOrderRule][处理日期:{}, 进度:{}/{}]", shipDate, (i + 1), distinctShipDates.size());
                
                // 获取该日期的合并后结果集
                List<Map<String, Object>> totalResultList = getMergedResultListByDate(mergeRuleScopeSql, shipDate);
                if (totalResultList.isEmpty()) {
                    log.warn("[processMergeOrderRule][日期:{}的结果集为空]", shipDate);
                    continue;
                }

                totalProcessedRecords += totalResultList.size();

                // 过滤billNos中的空值并创建新的不可变列表（保持原有逻辑）
                final List<String> filteredBillNos;
                if (billNos != null) {
                    filteredBillNos = billNos.stream()
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                } else {
                    filteredBillNos = null;
                }

                // 如果指定了单据编号列表，则过滤结果集（保持原有逻辑）
                if (CollUtil.isNotEmpty(filteredBillNos)) {
                    int beforeFilterSize = totalResultList.size();
                    totalResultList = totalResultList.stream()
                            .filter(map -> {
                                String fbillno = (String) map.get(FIELD_BILL_NO);
                                return fbillno != null && filteredBillNos.contains(fbillno);
                            })
                            .collect(Collectors.toList());

                    log.debug("[processMergeOrderRule][日期:{}过滤前记录数:{}, 过滤后记录数:{}]", shipDate, beforeFilterSize, totalResultList.size());

                    if (totalResultList.isEmpty()) {
                        log.warn("[processMergeOrderRule][日期:{}过滤后的结果集为空][billNos={}]", shipDate, filteredBillNos);
                        continue;
                    }
                }

                // 获取明细合并分组规则字段集合（保持原有逻辑）
                List<CqMergeOrderRulesDetailEntryDO> detailMergeGroupRules = mergeOrderRulesWithEntriesDTO.getDetailEntries();

                // 获取主合并分组规则字段ID列表（保持原有逻辑）
                List<Long> mainMergeGroupRuleFieldIds = mainMergeGroupRuleEntries.stream()
                        .map(CqMergeOrderRulesConditionEntryDO::getFieldFlagId)
                        .collect(Collectors.toList());

                // 构建主合并分组规则字段标识列表（保持原有逻辑）
                List<String> mainMergeGroupRuleFieldFlags = buildFieldFlagList(mainMergeGroupRuleFieldIds);
                if (mainMergeGroupRuleFieldFlags.isEmpty()) {
                    log.warn("[processMergeOrderRule][日期:{}主合并分组规则字段标识列表为空]", shipDate);
                    continue;
                }

                // 将合单结果按照字段标识列表进行分组（保持原有逻辑）
                List<List<Map<String, Object>>> mergedOrders = groupMergedOrders(totalResultList, mainMergeGroupRuleFieldFlags);
                log.info("[processMergeOrderRule][日期:{}分组数量:{}]", shipDate, mergedOrders.size());

                // 处理每个分组的订单（保持原有逻辑）
                int processedGroups = 0;
                // 按照mergedOrders数量由小到大排序后遍历
                mergedOrders.sort(Comparator.comparingInt(List::size));
                
                for (List<Map<String, Object>> mergedOrder : mergedOrders) {
                    try {
                        processMergedOrder(mergedOrder, detailMergeGroupRules, baseDataMaps);
                        processedGroups++;
                    } catch (Exception e) {
                        log.error("[processMergeOrderRule][日期:{}处理合并订单异常][mergedOrder={}]", shipDate, mergedOrder, e);
                        // 异常不影响下次循环，继续处理下一组订单
                    }
                }
                
                totalProcessedDays++;
                log.info("[processMergeOrderRule][日期:{}处理完成, 成功处理分组数:{}/{}]", shipDate, processedGroups, mergedOrders.size());
                
            } catch (Exception e) {
                log.error("[processMergeOrderRule][处理日期:{}异常]", shipDate, e);
                
                // 发送异常通知到钉钉群
                String additionalInfo = String.format("处理状态: 日期级别异常\n异常日期: %s\n处理进度: %d/%d", 
                                                    shipDate, (i + 1), distinctShipDates.size());
                sendMergeRuleExceptionNotification(mergeOrderRules.getBillNo(), e, "processMergeOrderRule-DateLevel", additionalInfo);
                
                // 单个日期异常不影响其他日期处理
            } finally {
                long dateEndTime = System.currentTimeMillis();
                long dayProcessingTime = dateEndTime - dateStartTime;
                totalProcessingTime += dayProcessingTime;
                log.info("[processMergeOrderRule][完成日期:{}处理, 耗时:{}ms]", shipDate, dayProcessingTime);
            }
        }
        
        // 输出总体统计信息
        log.info("[processMergeOrderRule][处理完成统计][规则:{}, 总天数:{}, 成功处理天数:{}, 总记录数:{}, 总耗时:{}ms, 平均每天耗时:{}ms]", 
                mergeOrderRules.getBillNo(), 
                distinctShipDates.size(), 
                totalProcessedDays, 
                totalProcessedRecords, 
                totalProcessingTime,
                totalProcessedDays > 0 ? totalProcessingTime / totalProcessedDays : 0);

        // 记录性能统计到Redis
        recordPerformanceStats(mergeOrderRules.getBillNo(), totalProcessedDays, totalProcessedRecords, totalProcessingTime);
    }

    /**
     * 获取符合条件的单据编号列表
     *
     * @param mergeRuleScopeSql 合单规则范围SQL脚本
     * @param billNos 指定需要处理的单据编号列表
     * @return 单据编号列表
     */
    private List<String> getBillNoList(String mergeRuleScopeSql, List<String> billNos) {
        // 执行SQL查询获取符合范围的单据
        List<Map<String, Object>> resultList = cqSqlQueryService.executeQuery(mergeRuleScopeSql);
        if (resultList.isEmpty()) {
            return Collections.emptyList();
        }

        // 从查询结果中提取单据编号列表，过滤掉为null和空字符串的billNo
        List<String> result = resultList.stream()
                .map(map -> (String) map.get("fbillno"))
                .filter(fbillno -> fbillno != null && !fbillno.isEmpty())
                .collect(Collectors.toList());

        // 如果billNos不为空且不为空列表，则进行过滤
        if (billNos != null && !billNos.isEmpty()) {
            log.info("[getBillNoList][根据参数过滤单据][billNos={}]", billNos);
            result = result.stream()
                    .filter(fbillno -> billNos.contains(fbillno))
                    .collect(Collectors.toList());
        }

        return result;
    }

    // /**
    //  * 获取合并后的结果集
    //  *
    //  * @param mergeRuleScopeSql 合单规则范围sql脚本
    //  * @return 合并后的结果集
    //  */
    // private List<Map<String, Object>> getMergedResultList(String mergeRuleScopeSql) {
    //     // 构建合单SQL语句
    //     long startTime = System.currentTimeMillis();
    //     String mergestrSQL = cn.iocoder.yudao.module.sap.utils.MergeSqlBuilder.buildMergeSql(mergeRuleScopeSql);
    //     // 执行SQL查询，获取合并后的结果集
    //     List<Map<String, Object>> result = cqSqlQueryService.executeQuery(mergestrSQL);
    //     long endTime = System.currentTimeMillis();
    //     log.info("[getMergedResultList][执行合并SQL查询耗时:{}ms]", (endTime - startTime));
    //     return result;
    // }

    /**
     * 获取所有不重复的发货日期（按天分组）
     *
     * @param mergeRuleScopeSql 合单规则范围sql脚本
     * @return 日期列表，格式为 yyyy-MM-dd
     */
    private List<String> getDistinctShipDates(String mergeRuleScopeSql) {
        long startTime = System.currentTimeMillis();
        String dateQuerySQL = cn.iocoder.yudao.module.sap.utils.MergeSqlBuilder.buildDateDistinctQuery(mergeRuleScopeSql);
        
        log.info("[getDistinctShipDates][执行日期查询SQL]: {}", dateQuerySQL);
        List<Map<String, Object>> dateResults = cqSqlQueryService.executeQuery(dateQuerySQL);
        long endTime = System.currentTimeMillis();
        
        log.info("[getDistinctShipDates][获取发货日期列表耗时:{}ms, 日期数量:{}]", (endTime - startTime), dateResults.size());
        
        List<String> result = dateResults.stream()
                .map(map -> {
                    Object shipDate = map.get("ship_date");
                    if (shipDate != null) {
                        // 确保日期格式统一为 yyyy-MM-dd，去除时间部分
                        try {
                            if (shipDate instanceof Date) {
                                return DateUtil.format((Date) shipDate, "yyyy-MM-dd");
                            } else {
                                // 如果是字符串，先解析再格式化为日期格式
                                Date parsedDate = DateUtil.parseDate(String.valueOf(shipDate));
                                return DateUtil.format(parsedDate, "yyyy-MM-dd");
                            }
                        } catch (Exception e) {
                            log.warn("[getDistinctShipDates][日期格式转换失败]: {}", shipDate, e);
                            return null;
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .distinct() // 额外保险，确保去重
                .sorted() // 按日期排序
                .collect(Collectors.toList());
        
        log.info("[getDistinctShipDates][处理后的日期列表]: {}", result);
        return result;
    }

    /**
     * 按指定日期获取合并后的结果集
     *
     * @param mergeRuleScopeSql 合单规则范围sql脚本
     * @param shipDate 发货日期，格式为 yyyy-MM-dd
     * @return 该日期的合并结果集
     */
    private List<Map<String, Object>> getMergedResultListByDate(String mergeRuleScopeSql, String shipDate) {
        long startTime = System.currentTimeMillis();
        String mergestrSQL = MergeSqlBuilder.buildMergeSqlWithDateFilter(mergeRuleScopeSql, shipDate);
        
        log.debug("[getMergedResultListByDate][日期:{}执行SQL]: {}", shipDate, mergestrSQL);
        List<Map<String, Object>> result = cqSqlQueryService.executeQuery(mergestrSQL);
        long endTime = System.currentTimeMillis();
        
        log.info("[getMergedResultListByDate][日期:{}执行合并SQL查询耗时:{}ms, 记录数:{}]", shipDate, (endTime - startTime), result.size());
        return result;
    }

    /**
     * 将合单结果按照字段标识列表进行分组
     *
     * @param totalResultList 合并后的结果集
     * @param firstFieldFlagList 主合并分组规则字段标识列表
     * @return 分组后的订单列表
     */
    private List<List<Map<String, Object>>> groupMergedOrders(List<Map<String, Object>> totalResultList, List<String> firstFieldFlagList) {
        return new ArrayList<>(
                totalResultList.stream()
                .collect(Collectors.groupingBy(map -> {
                    return firstFieldFlagList.stream()
                            .map(fieldFlag -> String.valueOf(map.get(fieldFlag)))
                            .collect(Collectors.joining(","));
                }))
                .values());
    }

    /**
     * 处理合并后的订单
     *
     * @param mergedOrder 合并后的订单数据
     * @param detailMergeGroupRules 明细合并分组规则
     * @param baseDataMaps 基础数据Map集合
     * @throws IOException IO异常
     */
    private void processMergedOrder(List<Map<String, Object>> mergedOrder, List<CqMergeOrderRulesDetailEntryDO> detailMergeGroupRules, BaseDataMaps baseDataMaps) throws IOException {
        // 获取mergedOrder的第一个元素，作为合并后的主表记录
        Map<String, Object> mergedMainRecord = mergedOrder.get(0);

        // 提取合并订单中所有元素的上游单据信息列表
        List<UpstreamBillInfo> upstreamBillInfoList = extractUpstreamBillInfoList(mergedOrder);

        // 下游单据类型
        String downstreamBillType = (String) mergedMainRecord.get(FIELD_DOWNSTREAM_BILL_TYPE);
        if (StringUtils.isBlank(downstreamBillType)) {
            log.warn("下游单据类型为空，需要在合单规则中配置下游单据类型");
            return;
        }

        // 根据目标单据类型判断：1是销售出库单，2是其他出库单
        if ("1".equals(downstreamBillType)) {
            processSalesOutboundOrder(mergedOrder, detailMergeGroupRules, mergedMainRecord, upstreamBillInfoList, baseDataMaps);
        } else if ("2".equals(downstreamBillType)) {
            processOtherOutboundOrder(mergedOrder, detailMergeGroupRules, mergedMainRecord, upstreamBillInfoList, baseDataMaps);
        }
    }

    /**
     * 提取订单中的上游单据信息列表
     *
     * @param mergedOrder 合并后的订单数据
     * @return 上游单据信息列表
     */
    private List<UpstreamBillInfo> extractUpstreamBillInfoList(List<Map<String, Object>> mergedOrder) {
        return mergedOrder.stream()
                .map(UpstreamBillInfo::fromMap)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 提取订单中的单据编号列表，格式为"单据编号$序号$明细ID"
     * @deprecated 使用 extractUpstreamBillInfoList 替代
     *
     * @param mergedOrder 合并后的订单数据
     * @return 单据编号和序号组合的列表，格式为"fbillno$fseq$entryId"
     */
    @Deprecated
    private List<String> extractBillNoList(List<Map<String, Object>> mergedOrder) {
        return extractUpstreamBillInfoList(mergedOrder).stream()
                .map(info -> info.getBillNo() + "$" + info.getSeq() + "$" + info.getEntryId())
                .collect(Collectors.toList());
    }

    /**
     * 提取订单中的明细ID列表
     *
     * @param mergedOrder 合并后的订单数据
     * @return 明细ID列表
     */
    private List<Long> extractEntryIds(List<Map<String, Object>> mergedOrder) {
        return mergedOrder.stream()
                .map(map -> Long.valueOf(String.valueOf(map.get(FIELD_ENTRY_ID))))
                .collect(Collectors.toList());
    }

    /**
     * 处理销售出库单
     *
     * @param mergedOrder 合并后的订单数据
     * @param detailMergeGroupRules 明细合并分组规则
     * @param mergedMainRecord 合并后的主表记录
     * @param upstreamBillInfoList 上游单据信息列表
     * @param baseDataMaps 基础数据Map集合
     * @throws IOException IO异常
     */
    private void processSalesOutboundOrder(List<Map<String, Object>> mergedOrder, List<CqMergeOrderRulesDetailEntryDO> detailMergeGroupRules,
            Map<String, Object> mergedMainRecord, List<UpstreamBillInfo> upstreamBillInfoList, BaseDataMaps baseDataMaps) throws IOException {
        
        // 从上游单据信息中提取明细ID列表
        List<String> entryIdStrings = UpstreamBillInfoUtils.extractEntryIds(upstreamBillInfoList);
        List<Long> mergedEntryIds = entryIdStrings.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<SalesOutboundSaveSubmitAuditReqDTO> data = new ArrayList<>();
        SalesOutboundSaveSubmitAuditReqDTO reqDTO = new SalesOutboundSaveSubmitAuditReqDTO();

        // 设置主表数据
        setMainTableDataOfSalesOutbound(reqDTO, mergedMainRecord, baseDataMaps);

        // 处理明细分组和合计，返回合并后的明细数据
        List<Map<String, Object>> mergedDetailRecords = processDetailGroupingAndSummary(detailMergeGroupRules, mergedOrder);
        List<SalesOutboundSaveSubmitAuditReqDTO.BillEntry> billEntryList = createBillEntriesOfSalesOutbound(reqDTO, mergedDetailRecords, baseDataMaps);
        reqDTO.setBillentry(billEntryList);

        // 创建发货明细实体列表
        List<SalesOutboundSaveSubmitAuditReqDTO.YdEntryEntity> ydEntryentity = createYdEntryEntitiesOfSalesOutbound(upstreamBillInfoList);
        reqDTO.setYdEntryentity(ydEntryentity);
        data.add(reqDTO);

        // 统计销售出库单数量和金额，根据销售出库单的发货明细找发货明细表统计拆单明细的数量和金额，要求两者保持一致
        validateSalesOutboundTotals(reqDTO, mergedEntryIds);

        // 保存销售出库单
        saveSalesOutboundOrder(data, upstreamBillInfoList, mergedEntryIds);
    }

    /**
     * 处理其他出库单
     *
     * @param mergedOrder 合并后的订单数据
     * @param detailMergeGroupRules 明细合并分组规则
     * @param mergedMainRecord 合并后的主表记录
     * @param upstreamBillInfoList 上游单据信息列表
     * @param baseDataMaps 基础数据Map集合
     * @throws IOException IO异常
     */
    private void processOtherOutboundOrder(List<Map<String, Object>> mergedOrder, List<CqMergeOrderRulesDetailEntryDO> detailMergeGroupRules,
            Map<String, Object> mergedMainRecord, List<UpstreamBillInfo> upstreamBillInfoList, BaseDataMaps baseDataMaps) throws IOException {
        
        // 从上游单据信息中提取明细ID列表
        List<String> entryIdStrings = UpstreamBillInfoUtils.extractEntryIds(upstreamBillInfoList);
        List<Long> mergedEntryIds = entryIdStrings.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<OtherOutboundSaveReqDTO> data = new ArrayList<>();
        OtherOutboundSaveReqDTO reqDTO = new OtherOutboundSaveReqDTO();

        // 设置主表数据
        setMainTableDataOfOtherOutbound(reqDTO, mergedMainRecord, baseDataMaps);

        // 处理明细分组和合计，返回合并后的明细数据
        List<Map<String, Object>> mergedDetailRecords = processDetailGroupingAndSummary(detailMergeGroupRules, mergedOrder);
        List<OtherOutboundSaveReqDTO.BillEntry> billEntryList = createBillEntriesOfOtherOutbound(mergedDetailRecords, baseDataMaps);
        reqDTO.setBillentry(billEntryList);

        // 创建发货明细实体列表
        List<OtherOutboundSaveReqDTO.EntryEntity> ydEntryentity = createYdEntryEntitiesOfOtherOutbound(upstreamBillInfoList);
        reqDTO.setYd_entryentity(ydEntryentity);

        data.add(reqDTO);

        // 统计其他出库单数量和金额，根据其他出库单的发货明细找发货明细表统计拆单明细的数量和金额，要求两者保持一致
        validateOtherOutboundTotals(reqDTO, mergedEntryIds);

        // 保存其他出库单
        saveOtherOutboundOrder(data, upstreamBillInfoList, mergedEntryIds);
    }

    /**
     * 设置主表数据
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setMainTableDataOfSalesOutbound(SalesOutboundSaveSubmitAuditReqDTO reqDTO, Map<String, Object> mergedMainRecord, BaseDataMaps baseDataMaps) {
        // 单据编号
        // 生成汇总单号（合并后的单据编号）
        // String mergedBillNo = generateMergedBillNo(BillTypeEnum.SALES_OUTBOUND);
        // reqDTO.setBillno(mergedBillNo);

        // 获取发货日期，用于设置业务日期、记账日期和汇率日期
        Object shipDateObj = mergedMainRecord.get(FIELD_SHIP_DATE);
        String formattedShipDate = shipDateObj != null ?
                DateUtil.format(DateUtil.parseDate(String.valueOf(shipDateObj)), "yyyy-MM-dd") : null;

        // 是否退货
        Object isReturnObj = mergedMainRecord.get(FIELD_IS_RETURN);
        boolean isReturn = isReturnObj != null && "1".equals(String.valueOf(isReturnObj));
        reqDTO.setYdIsreturn(isReturn);

        // 业务日期
        reqDTO.setBiztime(formattedShipDate);
        // 记账日期
        reqDTO.setBookdate(formattedShipDate);
        // 汇率日期
        reqDTO.setExratedate(formattedShipDate);
        // 单据生成类型(单据生成类型 [0:手工生成, 1:导入生成, 2:后台生成, 3:webApi生成])
        reqDTO.setBillcretype("3");
        // 汇率
        reqDTO.setExchangerate(BigDecimal.ONE);
        // 含税
        reqDTO.setIstax(true);
        // 是否初始单据
        reqDTO.setIsinitbill(false);
        // 付款方式
        reqDTO.setPaymode("CREDIT");
        // 单据类型编码
        reqDTO.setBilltypeNumber("im_SalOutBill_STD_BT_S");
        
        // 设置业务类型、库存事务编码和SAP单据类型
        setBusinessTypeAndSapBillType(reqDTO, isReturn);

        // 来源单据类型(来源单据类型 [1:批发退货单, 2:批发通知单, 3:PCP销售出库单, 4:PCP库存调拨单, 5:其他出库单, 6:发货明细]）
        reqDTO.setYdSourcebilltype("6");    
        
        // 结算币别
        reqDTO.setSettlecurrencyNumber("BB01");
        // 结算汇率
        reqDTO.setExratetableNumber("1");
        // 货币
        reqDTO.setCurrencyNumber("BB01");
        // 汇率表
        reqDTO.setExratetableNumber("ERT-01");

        // 设置组织信息（销售组织编号）
        setOrganizationInfo(reqDTO, mergedMainRecord, baseDataMaps);

        // 设置客户信息
        setCustomerInfo(reqDTO, mergedMainRecord, baseDataMaps);
        // 设置来源系统
        setSourceSystem(reqDTO, mergedMainRecord);        
        
        // 设置品牌分拆单信息
        setBrandSplitBillInfo(reqDTO, mergedMainRecord);   

        // 订单备注
        reqDTO.setComment(String.valueOf(mergedMainRecord.get(FIELD_ORDER_REMARK)));
    }

    /**
     * 设置业务类型、库存事务编码和SAP单据类型
     *
     * @param reqDTO 请求DTO
     * @param isReturn 是否退货
     */
    private void setBusinessTypeAndSapBillType(SalesOutboundSaveSubmitAuditReqDTO reqDTO, boolean isReturn) {
        if(isReturn) {
            // 业务类型编码
            reqDTO.setBiztypeNumber("2101");
            // 库存事务编码
            reqDTO.setInvschemeNumber("2101");
            // SAP单据类型*（ZORC:2C订单, ZREC:2C退货订单）
            reqDTO.setYdSapbilltype("ZREC");
        }else{
            // 业务类型编码
            reqDTO.setBiztypeNumber("210");
            // 库存事务编码
            reqDTO.setInvschemeNumber("210");
            // SAP单据类型*（ZORC:2C订单, ZREC:2C退货订单）
            reqDTO.setYdSapbilltype("ZORC");
        }
    }

    /**
     * 设置其他出库单的主表数据
     *
     * 根据合并后的主表记录，设置其他出库单请求DTO的各项属性，包括单据编号、业务日期、记账日期等
     *
     * @param reqDTO 其他出库单请求DTO对象
     * @param mergedMainRecord 合并后的主表记录数据
     * @param baseDataMaps 基础数据Map集合
     */
    private void setMainTableDataOfOtherOutbound(OtherOutboundSaveReqDTO reqDTO, Map<String, Object> mergedMainRecord, BaseDataMaps baseDataMaps) {
        // 单据编号
        // 生成汇总单号（合并后的单据编号）
        // String mergedBillNo = generateMergedBillNo(BillTypeEnum.OTHER_OUTBOUND);
        // reqDTO.setBillno(mergedBillNo);

        // 获取发货日期，用于设置业务日期、记账日期和汇率日期
        Object shipDateObj = mergedMainRecord.get(FIELD_SHIP_DATE);
        String formattedShipDate = shipDateObj != null ?
                DateUtil.format(DateUtil.parseDate(String.valueOf(shipDateObj)), "yyyy-MM-dd") : null;

        // 是否退货
        Object isReturnObj = mergedMainRecord.get(FIELD_IS_RETURN);
        boolean isReturn = isReturnObj != null && "1".equals(String.valueOf(isReturnObj));
        reqDTO.setYd_isreturn(isReturn);

        // 库存操作类型
        // 是否非销=是，是否退货=否 201 部门领料
        // 是否非销=是，是否退货=是 202 部门领料退货    
        Object isNotSaleObj = mergedMainRecord.get(FIELD_IS_NOT_SALE);
        boolean isNotSale = isNotSaleObj != null && "1".equals(String.valueOf(isNotSaleObj));
        if(isNotSale && !isReturn) {
            reqDTO.setYd_invoptype("201");
        }else if(isNotSale && isReturn) {
            reqDTO.setYd_invoptype("202");
        }

        // 业务日期
        reqDTO.setBiztime(formattedShipDate);
        // 记账日期
        reqDTO.setBookdate(formattedShipDate);

        // 设置组织信息（销售组织编号）
        setOtherOutboundOrganizationInfo(reqDTO, mergedMainRecord, baseDataMaps);

        // 单据生成类型(单据生成类型 [0:手工生成, 1:导入生成, 2:后台生成, 3:webApi生成])
        reqDTO.setBillcretype("3");
        // 单据类型
        reqDTO.setBilltype_number("im_OtherOutBill_STD_BT_S");
        // 业务类型
        reqDTO.setBiztype_number("355");
        // 库存事务编码
        reqDTO.setInvscheme_number("355");
        // 结算币别
        reqDTO.setSettlecurrency_number("BB01");

        // 同步来源（同步来源系统 [1:苍穹财务系统-E3, 2:苍穹财务系统-旺店通, 3:苍穹财务系统-旺店通]）
        reqDTO.setYd_tbly("1");
        // 销售业务类型（销售业务类型 [1:非销]）
        reqDTO.setYd_biztype("1");
        // 来源单据类型（来源单据类型 [1:发货明细]）
        reqDTO.setYd_sourcebilltype("1");
        // 其他出库区域
        reqDTO.setYd_quyu_number("ARNone");

        // bizoperatorgroup_number operatorgroup_number
        // reqDTO.setBizoperatorgroup_number("01");
        reqDTO.setOperatorgroup_number("01");

        // 设置客户信息
        setCustomerInfoOfOtherOutbound(reqDTO, mergedMainRecord, baseDataMaps);

        // 设置预算和领用类型信息
        setBudgetAndRequisitionInfo(reqDTO, mergedMainRecord);

        // 设置来源系统
        setSourceSystemOfOtherOutbound(reqDTO, mergedMainRecord);
        
        // 设置品牌分拆单信息
        setBrandSplitBillInfo(reqDTO, mergedMainRecord);   

        // 订单备注
        reqDTO.setComment(String.valueOf(mergedMainRecord.get(FIELD_ORDER_REMARK))); 
    }

    /**
     * 设置其他出库单的组织信息
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setOtherOutboundOrganizationInfo(OtherOutboundSaveReqDTO reqDTO, Map<String, Object> mergedMainRecord, BaseDataMaps baseDataMaps) {
        String orgNumber = "";
        Object saleOrgIdObj = mergedMainRecord.get(FIELD_SALE_ORG_ID);
        if (saleOrgIdObj != null) {
            try {
                Long orgId = Long.valueOf(String.valueOf(saleOrgIdObj));
                OrgDO org = baseDataMaps.getOrgMap().get(orgId);
                if (org != null && StringUtils.isNotBlank(org.getFnumber())) {
                    // 设置销售组织编号、库存组织编号和库管部门
                    orgNumber = org.getFnumber();
                    reqDTO.setOrg_number(orgNumber);
                    reqDTO.setDept_number(orgNumber);
                    // // 业务组织编码
                    // reqDTO.setBizorg_number(orgNumber);
                    // // 业务部门编码
                    // reqDTO.setBizdept_number(orgNumber);
                    // 原库存组织
                    reqDTO.setYd_oriorg_number(orgNumber);
                    // 销售组织.编码
                    reqDTO.setYd_bizorg_number(orgNumber);
                    log.debug("设置组织信息成功，组织编号：{}", orgNumber);
                } else {
                    log.warn("未找到有效的组织信息，组织ID：{}", orgId);
                }
            } catch (NumberFormatException e) {
                log.error("销售组织ID转换异常：{}", saleOrgIdObj, e);
            }
        } else {
            log.warn("销售组织ID为空");
        }
    }

    /**
     * 设置组织信息
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setOrganizationInfo(SalesOutboundSaveSubmitAuditReqDTO reqDTO, Map<String, Object> mergedMainRecord, BaseDataMaps baseDataMaps) {
        String orgNumber = "";
        Object saleOrgIdObj = mergedMainRecord.get(FIELD_SALE_ORG_ID);
        if (saleOrgIdObj != null) {
            try {
                Long orgId = Long.valueOf(String.valueOf(saleOrgIdObj));
                OrgDO org = baseDataMaps.getOrgMap().get(orgId);
                if (org != null && StringUtils.isNotBlank(org.getFnumber())) {
                    // 设置销售组织编号、库存组织编号和销售部门
                    orgNumber = org.getFnumber();
                    reqDTO.setBizorgNumber(orgNumber);
                    reqDTO.setOrgNumber(orgNumber);
                    reqDTO.setBizdeptNumber(orgNumber);
                    log.debug("设置组织信息成功，组织编号：{}", orgNumber);
                } else {
                    log.warn("未找到有效的组织信息，组织ID：{}", orgId);
                }
            } catch (NumberFormatException e) {
                log.error("销售组织ID转换异常：{}", saleOrgIdObj, e);
            }
        } else {
            log.warn("销售组织ID为空");
        }
    }

    /**
     * 设置客户信息
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setCustomerInfo(SalesOutboundSaveSubmitAuditReqDTO reqDTO, Map<String, Object> mergedMainRecord, BaseDataMaps baseDataMaps) {
        Object customerIdObj = mergedMainRecord.get(FIELD_CQ_CUSTOMER_ID);
        if (customerIdObj != null) {
            try {
                Long customerId = Long.valueOf(String.valueOf(customerIdObj));
                CqCustomerDO customer = baseDataMaps.getCustomerMap().get(customerId);
                if (customer != null && StringUtils.isNotBlank(customer.getFnumber())) {
                    // 收货客户
                    reqDTO.setCustomerNumber(customer.getFnumber());
                    //对账客户
                    reqDTO.setYdDzdkhNumber(customer.getFnumber());
                    log.debug("设置客户信息成功，客户编号：{}", customer.getFnumber());
                } else {
                    log.warn("未找到有效的客户信息，客户ID：{}", customerId);
                }
            } catch (NumberFormatException e) {
                log.error("客户ID转换异常：{}", customerIdObj, e);
            }
        } else {
            log.warn("客户ID为空");
        }
    }

    /**
     * 设置客户信息（其他出库单）
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setCustomerInfoOfOtherOutbound(OtherOutboundSaveReqDTO reqDTO, Map<String, Object> mergedMainRecord, BaseDataMaps baseDataMaps) {
        Object customerIdObj = mergedMainRecord.get(FIELD_CQ_CUSTOMER_ID);
        if (customerIdObj != null) {
            try {
                Long customerId = Long.valueOf(String.valueOf(customerIdObj));
                CqCustomerDO customer = baseDataMaps.getCustomerMap().get(customerId);
                if (customer != null && StringUtils.isNotBlank(customer.getFnumber())) {
                    // 客户
                    reqDTO.setCustomer_number(customer.getFnumber());
                    log.debug("设置客户信息成功，客户编号：{}", customer.getFnumber());
                } else {
                    log.warn("未找到有效的客户信息，客户ID：{}", customerId);
                }
            } catch (NumberFormatException e) {
                log.error("客户ID转换异常：{}", customerIdObj, e);
            }
        } else {
            log.warn("客户ID为空");
        }
    }

    /**
     * 设置来源系统
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     */
    private void setSourceSystem(SalesOutboundSaveSubmitAuditReqDTO reqDTO, Map<String, Object> mergedMainRecord) {
        Object platformObj = mergedMainRecord.get(FIELD_PLATFORM);
        if(platformObj != null && StringUtils.isNotBlank(String.valueOf(platformObj))) {
            reqDTO.setYdSourcesys(String.valueOf(platformObj));
        } else {
            log.debug("平台信息为空，未设置来源系统");
        }
    }

    /**
     * 设置其他出库单的来源系统
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     */
    private void setSourceSystemOfOtherOutbound(OtherOutboundSaveReqDTO reqDTO, Map<String, Object> mergedMainRecord) {
        Object platformObj = mergedMainRecord.get(FIELD_PLATFORM);
        if(platformObj != null && StringUtils.isNotBlank(String.valueOf(platformObj))) {
            reqDTO.setYd_sourcesys(String.valueOf(platformObj));
        } else {
            log.debug("平台信息为空，未设置来源系统");
        }
    }

    /**
     * 创建销售出库单的明细列表
     *
     * @param reqDTO 请求DTO
     * @param mergedDetailRecords 合并后的明细记录列表
     * @param baseDataMaps 基础数据Map集合
     * @return 销售出库单的明细列表
     */
    private List<SalesOutboundSaveSubmitAuditReqDTO.BillEntry> createBillEntriesOfSalesOutbound(SalesOutboundSaveSubmitAuditReqDTO reqDTO, List<Map<String, Object>> mergedDetailRecords, BaseDataMaps baseDataMaps) {
        List<SalesOutboundSaveSubmitAuditReqDTO.BillEntry> billEntryList = new ArrayList<>();
        for (Map<String, Object> mergedDetailRecord : mergedDetailRecords) {
            SalesOutboundSaveSubmitAuditReqDTO.BillEntry billEntry = createBillEntryOfSalesOutbound(reqDTO, mergedDetailRecord, baseDataMaps);
            billEntryList.add(billEntry);
        }
        return billEntryList;
    }

    /**
     * 创建销售出库单的单个明细条目
     *
     * @param reqDTO 请求DTO
     * @param mergedDetailRecord 合并后的明细记录数据
     * @param baseDataMaps 基础数据Map集合
     * @return 构建完成的销售出库单明细对象
     */
    private BillEntry createBillEntryOfSalesOutbound(SalesOutboundSaveSubmitAuditReqDTO reqDTO, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        BillEntry billEntry = new BillEntry();
        try {
            // 行类型编码
            billEntry.setLinetypeNumber("010");

            // 设置物料信息
            setMaterialInfo(billEntry, mergedDetailRecord, baseDataMaps);

            // 设置数量信息
            setQuantityInfo(billEntry, mergedDetailRecord);

            // 设置仓库信息（销售组织仓库）
            setWarehouseInfo(billEntry, mergedDetailRecord, baseDataMaps);

            // 设置仓库信息（库存组织仓库）
            setWarehouseInfoOfInvOrg(billEntry, mergedDetailRecord, baseDataMaps);

            // 折扣类型
            billEntry.setDiscounttype("NULL");

            // 出库库存类型编码
            billEntry.setOutinvtypeNumber("110");

            // 设置价格信息
            setPriceInfo(billEntry, mergedDetailRecord);

            // 设置赠品信息
            setGiftInfo(billEntry, mergedDetailRecord);

            // 获取组织编号（库存组织编号）
            String invOrgNumber = getOrgNumber(mergedDetailRecord, FIELD_INV_ORG_ID, baseDataMaps);
            billEntry.setYdInvorgNumber(invOrgNumber);

            // 获取组织编号（销售组织编号）
            String saleOrgNumber = getOrgNumber(mergedDetailRecord, FIELD_SALE_ORG_ID, baseDataMaps);

            // 结算组织编号
            billEntry.setEntrysettleorgNumber(saleOrgNumber);
            // 出库状态编码
            billEntry.setOutinvstatusNumber("110");
            // 出库货主编号
            billEntry.setOutownerNumber(saleOrgNumber);
            // 出库货主类型
            billEntry.setOutownertype("bos_org");
            // 出库保管者
            billEntry.setOutkeeperNumber(saleOrgNumber);
            // 出库保管者类型
            billEntry.setOutkeepertype("bos_org");
            // 出库库存类型
            billEntry.setOutinvtypeNumber("110");

            // 设置行类型
            setYdRowtype(billEntry, reqDTO.getYdSapbilltype());

            // 是否组套
            setProPackageInfo(billEntry, mergedDetailRecord);

            // 设置商品价格、折扣和物流成本信息
            setProductPriceAndCostInfo(billEntry, mergedDetailRecord);

        } catch (Exception e) {
            log.error("处理销售出库单明细异常", e);
        }
        return billEntry;
    }

    /**
     * 设置行类型编码
     * 根据是否退货和是否赠品设置不同的行类型编码
     * 
     * @param billEntry 单据明细
     * @param sapBillType SAP单据类型（ZORC:2C订单, ZREC:2C退货订单）
     */
    private void setYdRowtype(BillEntry billEntry, String sapBillType) {
        // 是否退货=否且是否赠品=否，取 TAN：常规销售类别
        // 是否退货=否且是否赠品=是，取 TANN：免费行
        // 是否退货=是且是否赠品=否，取 REN：退货行
        // 是否退货=是且是否赠品=是，取 RENN：免费行退货
        boolean isPresent = billEntry.getIspresent();
        boolean isReturn = "ZREC".equals(sapBillType);
        
        String rowType;
        if (isReturn) {
            rowType = isPresent ? "RENN" : "REN"; // 退货单
        } else {
            rowType = isPresent ? "TANN" : "TAN"; // 销售单
        }
        
        billEntry.setYdRowtype(rowType);
    }

    /**
     * 设置商品价格、折扣和物流成本信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setProductPriceAndCostInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        // 商品上架单价
        Object shelvesPrice = mergedDetailRecord.get(FIELD_SHELVES_PRICE);
        if (shelvesPrice != null && StringUtils.isNotBlank(String.valueOf(shelvesPrice))) {
            billEntry.setYdShelvesPrice(new BigDecimal(String.valueOf(shelvesPrice)).setScale(2, RoundingMode.HALF_UP));
        }

        // 商品总折扣
        Object totalDiscountAmount = mergedDetailRecord.get(FIELD_TOTAL_DISCOUNT_AMOUNT);
        if (totalDiscountAmount != null && StringUtils.isNotBlank(String.valueOf(totalDiscountAmount))) {
            billEntry.setYdDiscount(new BigDecimal(String.valueOf(totalDiscountAmount)).setScale(2, RoundingMode.HALF_UP));
        }

        // 平均物流成本（均摊运费）
        Object avgLogisticsCost = mergedDetailRecord.get(FIELD_AVG_LOGISTICS_COST);
        if (avgLogisticsCost != null && StringUtils.isNotBlank(String.valueOf(avgLogisticsCost))) {
            billEntry.setYdShareFreight(new BigDecimal(String.valueOf(avgLogisticsCost)).setScale(2, RoundingMode.HALF_UP));
        }

        // 均摊金额
        Object shareAmount = mergedDetailRecord.get(FIELD_SHARE_AMOUNT);
        if (shareAmount != null && StringUtils.isNotBlank(String.valueOf(shareAmount))) {
            billEntry.setYdShareAmount(new BigDecimal(String.valueOf(shareAmount)).setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 设置组套信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setProPackageInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        Object isProPackageObj = mergedDetailRecord.get(FIELD_IS_PRO_PACKAGE);
        boolean isProPackage = isProPackageObj != null && "1".equals(String.valueOf(isProPackageObj));
        billEntry.setYdIsbom(isProPackage);
        // 是否组装品=是，取对应【发货明细-商品明细分录】货品编码，否则为空 
        if(isProPackage) {
            // 货品编号（OMS明细）-组套编码
            billEntry.setYdBomnum(String.valueOf(mergedDetailRecord.get(FIELD_OMS_GOODS_NO)));
        }
    }

    /**
     * 创建其他出库单的明细列表
     *
     * @param mergedDetailRecords 合并后的明细记录列表
     * @param baseDataMaps 基础数据Map集合
     * @return 其他出库单的明细列表
     */
    private List<OtherOutboundSaveReqDTO.BillEntry> createBillEntriesOfOtherOutbound(List<Map<String, Object>> mergedDetailRecords, BaseDataMaps baseDataMaps) {
        List<OtherOutboundSaveReqDTO.BillEntry> billEntryList = new ArrayList<>();
        for (Map<String, Object> mergedDetailRecord : mergedDetailRecords) {
            OtherOutboundSaveReqDTO.BillEntry billEntry = createBillEntryOfOtherOutbound(mergedDetailRecord, baseDataMaps);
            billEntryList.add(billEntry);
        }
        return billEntryList;
    }

    /**
     * 创建其他出库单的单个明细条目
     *
     * @param mergedDetailRecord 合并后的明细记录数据
     * @param baseDataMaps 基础数据Map集合
     * @return 构建完成的其他出库单明细对象
     */
    private OtherOutboundSaveReqDTO.BillEntry createBillEntryOfOtherOutbound(Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        OtherOutboundSaveReqDTO.BillEntry billEntry = new OtherOutboundSaveReqDTO.BillEntry();
        try {
            // 行类型编码(物资)
            billEntry.setLinetype_number("010");

            // 设置物料信息
            setOtherOutboundMaterialInfo(billEntry, mergedDetailRecord, baseDataMaps);

            // 设置数量信息
            setOtherOutboundQuantityInfo(billEntry, mergedDetailRecord);

            // 设置仓库信息（销售组织仓库）
            setOtherOutboundWarehouseInfo(billEntry, mergedDetailRecord, baseDataMaps);

            // 设置仓库信息（库存组织仓库）
            setWarehouseInfoOfInvOrgForOtherOutbound(billEntry, mergedDetailRecord, baseDataMaps);

            // 是否组套
            setProPackageInfoForOtherOutbound(billEntry, mergedDetailRecord);

            // 设置库存组织编码
            String invOrgNumber = getOrgNumber(mergedDetailRecord, FIELD_INV_ORG_ID, baseDataMaps);
            billEntry.setYdInvorgNumber(invOrgNumber);

            // 设置出库相关信息
            // 获取销售组织编码
            String saleOrgNumber = getOrgNumber(mergedDetailRecord, FIELD_SALE_ORG_ID, baseDataMaps);
            setOtherOutboundInventoryInfo(billEntry, saleOrgNumber);

        } catch (Exception e) {
            log.error("处理销售出库单明细异常", e);
        }
        return billEntry;
    }

    /**
     * 设置其他出库单的物料信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setOtherOutboundMaterialInfo(OtherOutboundSaveReqDTO.BillEntry billEntry, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        Object materialIdObj = mergedDetailRecord.get(FIELD_MATERIAL_ID);
        if (materialIdObj != null) {
            Long materialId = Long.valueOf(String.valueOf(materialIdObj));
            CqBdMaterialDO material = baseDataMaps.getMaterialMap().get(materialId);
            if (material != null) {
                billEntry.setMaterial_number(material.getNumber());
                // 计量单位编码
                Long baseUnitId = material.getBaseUnitId();
                CqBdMeasureUnitDO baseUnit = baseDataMaps.getUnitMap().get(baseUnitId);
                if (baseUnit != null) {
                    billEntry.setUnit_number(baseUnit.getNumber());
                    billEntry.setBaseunit_number(baseUnit.getNumber());
                    // 产品类型
                    billEntry.setYdProducttype(material.getProType());
                    // 物料类型
                    billEntry.setYdMatgroupNumber(material.getMatGroupNum());
                } else {
                    log.warn("未找到计量单位信息，计量单位ID：{}", baseUnitId);
                }
            } else {
                log.warn("未找到物料信息，物料ID：{}", materialId);
            }
        }
    }

    /**
     * 设置其他出库单的数量信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setOtherOutboundQuantityInfo(OtherOutboundSaveReqDTO.BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        Object qtyObj = mergedDetailRecord.get(FIELD_QUANTITY);
        if (qtyObj != null) {
            billEntry.setQty(new BigDecimal(String.valueOf(qtyObj)));
            // 基本数量
            billEntry.setBaseqty(new BigDecimal(String.valueOf(qtyObj)));
        }
    }

    /**
     * 设置其他出库单的仓库信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setOtherOutboundWarehouseInfo(OtherOutboundSaveReqDTO.BillEntry billEntry, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        Object stockIdObj = mergedDetailRecord.get(FIELD_SALE_ORG_STOCK_ID);
        if (stockIdObj != null) {
            Long stockId = Long.valueOf(String.valueOf(stockIdObj));
            CqBdWarehouseDO warehouse = baseDataMaps.getWarehouseMap().get(stockId);
            if (warehouse != null && warehouse.getNumber() != null) {
                billEntry.setWarehouse_number(warehouse.getNumber());
            } else {
                log.warn("未找到仓库信息，仓库ID：{}", stockId);
            }
        }
    }

    /**
     * 设置其他出库单的出库相关信息
     *
     * @param billEntry 单据明细
     * @param orgNumber 组织编号
     */
    private void setOtherOutboundInventoryInfo(OtherOutboundSaveReqDTO.BillEntry billEntry, String orgNumber) {
        // 出库库存类型编码
        billEntry.setOutinvtype_number("110");
        // 出库状态编码
        billEntry.setOutinvstatus_number("110");
        // 出库货主类型
        billEntry.setOutownertype("bos_org");
        // 出库货主编号
        billEntry.setOutowner_number(orgNumber);
        // 出库保管者类型
        billEntry.setOutkeepertype("bos_org");
        // 出库保管者编码
        billEntry.setOutkeeper_number(orgNumber);
    }

    /**
     * 设置物料信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setMaterialInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        Object materialIdObj = mergedDetailRecord.get(FIELD_MATERIAL_ID);
        if (materialIdObj != null) {
            Long materialId = Long.valueOf(String.valueOf(materialIdObj));
            CqBdMaterialDO material = baseDataMaps.getMaterialMap().get(materialId);
            if (material != null) {
                billEntry.setMaterialNumber(material.getNumber());
                // 计量单位编码
                Long baseUnitId = material.getBaseUnitId();
                CqBdMeasureUnitDO baseUnit = baseDataMaps.getUnitMap().get(baseUnitId);
                if (baseUnit != null) {
                    billEntry.setUnitNumber(baseUnit.getNumber());
                    billEntry.setBaseunitNumber(baseUnit.getNumber());
                    // 产品类型
                    billEntry.setYdProducttype(material.getProType());
                    // 物料类型
                    billEntry.setYdMatgroupNumber(material.getMatGroupNum());
                } else {
                    log.warn("未找到计量单位信息，计量单位ID：{}", baseUnitId);
                }
            } else {
                log.warn("未找到物料信息，物料ID：{}", materialId);
            }
        }
    }

    /**
     * 设置数量信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setQuantityInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        Object qtyObj = mergedDetailRecord.get(FIELD_QUANTITY);
        if (qtyObj != null) {
            billEntry.setQty(new BigDecimal(String.valueOf(qtyObj)));
            // 基本数量
            billEntry.setBaseqty(new BigDecimal(String.valueOf(qtyObj)));
        }
    }

    /**
     * 设置仓库信息（销售组织仓库）
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setWarehouseInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        Object stockIdObj = mergedDetailRecord.get(FIELD_SALE_ORG_STOCK_ID);
        if (stockIdObj != null) {
            Long stockId = Long.valueOf(String.valueOf(stockIdObj));
            CqBdWarehouseDO warehouse = baseDataMaps.getWarehouseMap().get(stockId);
            if (warehouse != null && warehouse.getNumber() != null) {
                billEntry.setWarehouseNumber(warehouse.getNumber());
            } else {
                log.warn("未找到仓库信息，仓库ID：{}", stockId);
            }
        }
    }

    /**
     * 设置仓库信息（库存组织仓库）
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setWarehouseInfoOfInvOrg(BillEntry billEntry, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        Object stockIdObj = mergedDetailRecord.get(FIELD_INV_ORG_STOCK_ID);
        if (stockIdObj != null) {
            Long stockId = Long.valueOf(String.valueOf(stockIdObj));
            CqBdWarehouseDO warehouse = baseDataMaps.getWarehouseMap().get(stockId);
            if (warehouse != null && warehouse.getNumber() != null) {
                billEntry.setYdInvorgstockNumber(warehouse.getNumber());
            } else {
                log.warn("未找到库存组织仓库信息，仓库ID：{}", stockId);
            }
        }
    }

    /**
     * 设置价格信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setPriceInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        Object totalTaxAmountObj = mergedDetailRecord.get(FIELD_TOTAL_TAX_AMOUNT);
        Object qtyObj = mergedDetailRecord.get(FIELD_QUANTITY);
        if (totalTaxAmountObj != null && qtyObj != null) {
            BigDecimal totalTaxAmount = new BigDecimal(String.valueOf(totalTaxAmountObj));
            BigDecimal qty = new BigDecimal(String.valueOf(qtyObj));
            // billEntry.setCuramountandtax(totalTaxAmount);
            // billEntry.setAmountandtax(totalTaxAmount);
            if (qty.compareTo(BigDecimal.ZERO) > 0) {
                // 计算含税单价，保留两位小数，四舍五入
                BigDecimal priceAndTax = totalTaxAmount.divide(qty, 6, BigDecimal.ROUND_HALF_UP);
                billEntry.setPriceandtax(priceAndTax);
            } else {
                log.warn("数量为零，无法计算含税单价");
                billEntry.setPriceandtax(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 设置赠品信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setGiftInfo(BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        Object isgiftObj = mergedDetailRecord.get(FIELD_IS_GIFT);
        if (isgiftObj != null) {
            String isgiftStr = String.valueOf(isgiftObj);
            billEntry.setIspresent("1".equals(isgiftStr) || "true".equalsIgnoreCase(isgiftStr));
        }
    }

    /**
     * 获取组织编号
     *
     * @param mergedDetailRecord 合并后的明细记录
     * @param orgIdField 组织ID字段名
     * @param baseDataMaps 基础数据Map集合
     * @return 组织编号
     */
    private String getOrgNumber(Map<String, Object> mergedDetailRecord, String orgIdField, BaseDataMaps baseDataMaps) {
        String orgNumber = "";
        Object saleOrgIdObj = mergedDetailRecord.get(orgIdField);
        if (saleOrgIdObj != null) {
            try {
                Long orgId = Long.valueOf(String.valueOf(saleOrgIdObj));
                OrgDO org = baseDataMaps.getOrgMap().get(orgId);
                if (org != null && StringUtils.isNotBlank(org.getFnumber())) {
                    orgNumber = org.getFnumber();
                }
            } catch (NumberFormatException e) {
                log.error("{} 组织ID转换异常：{}", orgIdField, saleOrgIdObj, e);
            }
        }
        return orgNumber;
    }

    /**
     * 创建销售出库单的发货明细实体列表
     *
     * @param upstreamBillInfoList 上游单据信息列表
     * @return 发货明细实体列表
     */
    private List<YdEntryEntity> createYdEntryEntitiesOfSalesOutbound(List<UpstreamBillInfo> upstreamBillInfoList) {
        // 记录日志，一次性输出所有处理的上游单据信息
        log.info("处理的上游单据信息列表：{}", upstreamBillInfoList);

        // 创建发货明细实体列表
        List<YdEntryEntity> entryEntities = new ArrayList<>();

        // 对上游单据信息列表进行去重
        List<UpstreamBillInfo> distinctBillInfoList = upstreamBillInfoList.stream()
                .distinct()
                .collect(Collectors.toList());

        distinctBillInfoList.forEach(billInfo -> {
            // 创建新的发货明细实体对象
            YdEntryEntity entryEntity = new YdEntryEntity();
            // 设置发货明细单号
            entryEntity.setYdTextfieldFhmxdh(billInfo.getBillNo());
            // 设置发货明细单号.行号
            entryEntity.setYdRowno(billInfo.getSeq());
            // 设置发货明细单号.行ID
            entryEntity.setYdRowid(billInfo.getEntryId());
            // 将发货明细实体添加到列表中
            entryEntities.add(entryEntity);
        });

        return entryEntities;
    }

    /**
     * 创建其他出库单的发货明细实体列表
     *
     * @param upstreamBillInfoList 上游单据信息列表
     * @return 发货明细实体列表
     */
    private List<OtherOutboundSaveReqDTO.EntryEntity> createYdEntryEntitiesOfOtherOutbound(List<UpstreamBillInfo> upstreamBillInfoList) {
        // 记录日志，一次性输出所有处理的上游单据信息
        log.info("处理的上游单据信息列表：{}", upstreamBillInfoList);

        // 创建发货明细实体列表
        List<OtherOutboundSaveReqDTO.EntryEntity> ydEntryentity = new ArrayList<>();

        // 对上游单据信息列表进行去重
        List<UpstreamBillInfo> distinctBillInfoList = upstreamBillInfoList.stream()
                .distinct()
                .collect(Collectors.toList());

        distinctBillInfoList.forEach(billInfo -> {
            // 创建新的发货明细实体对象
            OtherOutboundSaveReqDTO.EntryEntity ydEntryEntity = new OtherOutboundSaveReqDTO.EntryEntity();
            // 设置发货明细单号
            ydEntryEntity.setYd_textfield_fhmxdh(billInfo.getBillNo());
            // 设置发货明细单号.行号
            ydEntryEntity.setYd_rowno(billInfo.getSeq());
            // 设置发货明细单号.行ID
            ydEntryEntity.setYd_rowid(billInfo.getEntryId());
            // 将发货明细实体添加到列表中
            ydEntryentity.add(ydEntryEntity);
        });

        return ydEntryentity;
    }

    /**
     * 从响应对象中安全提取第一个生成的单号
     *
     * @param resp 苍穹操作响应对象
     * @return 提取的单号，如果提取失败则返回空字符串
     */
    private String extractFirstBillNumber(CangQiongOperationResp resp) {
        if (resp == null) {
            log.warn("[extractFirstBillNumber][响应对象为空，无法提取单号]");
            return "";
        }
        
        if (resp.getData() == null) {
            log.warn("[extractFirstBillNumber][响应数据为空，无法提取单号]");
            return "";
        }
        
        if (resp.getData().getResult() == null || resp.getData().getResult().isEmpty()) {
            log.warn("[extractFirstBillNumber][响应结果列表为空，无法提取单号]");
            return "";
        }
        
        CangQiongOperationResp.Result firstResult = resp.getData().getResult().get(0);
        if (firstResult == null || firstResult.getNumber() == null) {
            log.warn("[extractFirstBillNumber][第一个结果或其单号为空，无法提取单号]");
            return "";
        }
        
        String billNumber = firstResult.getNumber();
        log.debug("[extractFirstBillNumber][成功提取单号: {}]", billNumber);
        return billNumber;
    }

    /**
     * 保存销售出库单
     *
     * @param data 销售出库单数据
     * @param upstreamBillInfoList 上游单据信息列表
     * @param mergedEntryIds 合并后的发货明细ID列表
     * @throws IOException IO异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSalesOutboundOrder(List<SalesOutboundSaveSubmitAuditReqDTO> data, List<UpstreamBillInfo> upstreamBillInfoList, List<Long> mergedEntryIds) throws IOException {
        // 保存销售出库单
        CangQiongOperationResp resp = cangQiongApiService.saveSalesOutboundOrderOnlySave(data);
        if (resp != null && resp.isStatus()) {
            // 从响应中提取实际生成的单号
            String actualBillNo = extractFirstBillNumber(resp);
            String originalBillNo = data.get(0).getBillno();
            log.info("[saveSalesOutboundOrder][单号信息][原始单号: {}, 实际生成单号: {}]", originalBillNo, actualBillNo);
            
            // 根据分录ID列表更新下游单号
            cqDeliveryDetailService.updateDownstreamBillNoByEntryId(mergedEntryIds, actualBillNo, "");

            // 更新参与合单标记 和 结算状态
            updateParticipateInCombineMark(upstreamBillInfoList);

            // 获取结果列表中的单据编号
            List<String> billNumbers = new ArrayList<>();
            if (resp.getData() != null && resp.getData().getResult() != null) {
                for (CangQiongOperationResp.Result result : resp.getData().getResult()) {
                    if (result.getNumber() != null) {
                        billNumbers.add(result.getNumber());
                    }
                }
            }
            log.info("销售出库单保存成功，单据编号：{}", billNumbers);
        } else {
            log.error("销售出库单保存失败，错误信息：{}", resp.getMessage());
            String errorMsg = extractErrorMessages(resp);
            cqDeliveryDetailService.updateDownstreamBillNoByEntryId(mergedEntryIds, "", errorMsg);
        }
    }

    /**
     * 从响应中提取错误信息
     *
     * @param resp 苍穹操作响应对象
     * @return 格式化后的错误信息JSON字符串
     */
    private String extractErrorMessages(CangQiongOperationResp resp) {
        String errorMsg = "";
        if (resp.getData() != null && resp.getData().getResult() != null) {
            List<CangQiongOperationResp.Error> errors = new ArrayList<>();
            for (CangQiongOperationResp.Result result : resp.getData().getResult()) {
                if (result.getErrors() != null) {
                    errors.addAll(result.getErrors());
                }
            }
            errorMsg = JSONUtil.toJsonStr(errors);
        }
        if (StringUtils.isBlank(errorMsg)) {
            errorMsg = JSONUtil.toJsonStr(resp);
        }
        // 如果不为空，添加时间前缀
        if (StringUtils.isNotBlank(errorMsg)) {
            String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + " ";
            errorMsg = timePrefix + errorMsg;
        }
        return errorMsg;
    }

    /**
     * 保存其他出库单
     *
     * @param data 其他出库单数据
     * @param upstreamBillInfoList 上游单据信息列表
     * @param mergedEntryIds 合并后的发货明细ID列表
     * @throws IOException IO异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOtherOutboundOrder(List<OtherOutboundSaveReqDTO> data, List<UpstreamBillInfo> upstreamBillInfoList, List<Long> mergedEntryIds) throws IOException {
        // 保存其他出库单
        CangQiongOperationResp resp = cangQiongApiService.saveOtherOutboundOrder(data);
        if (resp != null && resp.isStatus()) {
            // 从响应中提取实际生成的单号
            String actualBillNo = extractFirstBillNumber(resp);
            String originalBillNo = data.get(0).getBillno();
            log.info("[saveOtherOutboundOrder][单号信息][原始单号: {}, 实际生成单号: {}]", originalBillNo, actualBillNo);
            
            // 根据分录ID列表更新下游单号
            cqDeliveryDetailService.updateDownstreamBillNoByEntryId(mergedEntryIds, actualBillNo, "");

            // 更新参与合单标记
            updateParticipateInCombineMark(upstreamBillInfoList);

            // 获取结果列表中的单据编号
            List<String> billNumbers = new ArrayList<>();
            if (resp.getData() != null && resp.getData().getResult() != null) {
                for (CangQiongOperationResp.Result result : resp.getData().getResult()) {
                    if (result.getNumber() != null) {
                        billNumbers.add(result.getNumber());
                    }
                }
            }
            log.info("其他出库单保存成功，单据编号：{}", billNumbers);
        } else {
            log.error("其他出库单保存失败，错误信息：{}", resp.getMessage());
            String errorMsg = extractErrorMessages(resp);
            cqDeliveryDetailService.updateDownstreamBillNoByEntryId(mergedEntryIds, "", errorMsg);
            // throw new RuntimeException("其他出库单保存失败：" + resp.getMessage());
        }
    }

    /**
     * 处理明细分组和合计
     *
     * @param detailMergeGroupRules 明细合并分组规则
     * @param mergedOrder 合并后的订单数据
     * @return 分组并合计后的明细数据
     */
    private List<Map<String, Object>> processDetailGroupingAndSummary(
            List<CqMergeOrderRulesDetailEntryDO> detailMergeGroupRules,
            List<Map<String, Object>> mergedOrder) {

        // 获取跨单分组字段ID列表
        List<Long> detailFieldFlagIds = detailMergeGroupRules.stream()
                .map(CqMergeOrderRulesDetailEntryDO::getDetailFieldFlag)
                .collect(Collectors.toList());

        // 根据字段ID获取合单字段信息
        List<CqMergeOrderFieldsDO> detailMergeOrderFieldsList = mergeOrderFieldsService
                .getMergeOrderFieldsByIds(detailFieldFlagIds);
        if (detailMergeOrderFieldsList == null || detailMergeOrderFieldsList.size() == 0) {
            return mergedOrder;
        }

        // 创建一个列表，用于存储合单字段的完整路径（表名_字段名格式）
        List<String> secondFieldFlagList = new ArrayList<>();
        // 遍历合单字段列表，构建完整的字段路径
        detailMergeOrderFieldsList.forEach(detailMergeOrderFields -> {
            String tableFlag = detailMergeOrderFields.getTableFlag();
            String fieldFlag = detailMergeOrderFields.getName();
            String tableFlag_fieldFlag = (tableFlag + "_" + fieldFlag).toLowerCase();  // 组合成表名_字段名格式并转小写
            secondFieldFlagList.add(tableFlag_fieldFlag);  // 添加到列表中
        });
        // 对字段标识列表进行去重
        final List<String> finalFieldFlagList = secondFieldFlagList.stream().distinct().collect(Collectors.toList());

        // 将mergedOrder按照secondFieldFlagList进行分组，并合计数量和价税合计字段
        Map<String, List<Map<String, Object>>> groupedMap = mergedOrder.stream()
                .collect(Collectors.groupingBy(map -> {
                    return finalFieldFlagList.stream()
                            .map(fieldFlag -> String.valueOf(map.get(fieldFlag)))
                            .collect(Collectors.joining(","));
                }));

        List<Map<String, Object>> result = new ArrayList<>();
        for (List<Map<String, Object>> group : groupedMap.values()) {
            if (group.isEmpty()) {
                continue;
            }

            // 创建新的合并后的记录，基于组内第一条记录
            Map<String, Object> mergedRecord = new HashMap<>(group.get(0));

            // 计算需要合计的字段
            BigDecimal totalQty = BigDecimal.ZERO;
            BigDecimal totalTaxAmount = BigDecimal.ZERO;
            BigDecimal totalAvgLogisticsCost = BigDecimal.ZERO;
            BigDecimal totalGoodsDiscount = BigDecimal.ZERO;
            BigDecimal totalShareAmount = BigDecimal.ZERO;

            for (Map<String, Object> record : group) {
                // 合计数量字段
                Object qtyObj = record.get(FIELD_QUANTITY);
                if (qtyObj != null) {
                    BigDecimal qty = new BigDecimal(String.valueOf(qtyObj));
                    totalQty = totalQty.add(qty);
                }

                // 合计价税合计字段
                Object taxAmountObj = record.get(FIELD_TOTAL_TAX_AMOUNT);
                if (taxAmountObj != null) {
                    BigDecimal taxAmount = new BigDecimal(String.valueOf(taxAmountObj));
                    totalTaxAmount = totalTaxAmount.add(taxAmount);
                }

                // 合计平均物流成本（均摊运费）
                Object avgLogisticsCostObj = record.get(FIELD_AVG_LOGISTICS_COST);
                if (avgLogisticsCostObj != null) {
                    BigDecimal avgLogisticsCost = new BigDecimal(String.valueOf(avgLogisticsCostObj));
                    totalAvgLogisticsCost = totalAvgLogisticsCost.add(avgLogisticsCost);
                }

                // 合计商品折扣
                Object goodsDiscountObj = record.get(FIELD_TOTAL_DISCOUNT_AMOUNT);
                if (goodsDiscountObj != null) {
                    BigDecimal goodsDiscount = new BigDecimal(String.valueOf(goodsDiscountObj));
                    totalGoodsDiscount = totalGoodsDiscount.add(goodsDiscount);
                }

                // 合计均摊金额
                Object shareAmountObj = record.get(FIELD_SHARE_AMOUNT);
                if (shareAmountObj != null) {
                    BigDecimal shareAmount = new BigDecimal(String.valueOf(shareAmountObj));
                    totalShareAmount = totalShareAmount.add(shareAmount);
                }
            }

            // 更新合并后的记录中的合计值
            mergedRecord.put(FIELD_QUANTITY, totalQty);
            mergedRecord.put(FIELD_TOTAL_TAX_AMOUNT, totalTaxAmount);
            mergedRecord.put(FIELD_AVG_LOGISTICS_COST, totalAvgLogisticsCost);
            mergedRecord.put(FIELD_TOTAL_DISCOUNT_AMOUNT, totalGoodsDiscount);
            mergedRecord.put(FIELD_SHARE_AMOUNT, totalShareAmount);

            result.add(mergedRecord);
        }

        return result;
    }

    /**
     * 生成汇总单号（合并后的单据编号）
     * 格式：MY-单据类型(XSCK/QTCK)-日期(yyMMdd)-流水号(6位)
     *
     * @param billType 单据类型枚举
     * @return 生成的合并单号
     */
    private String generateMergedBillNo(BillTypeEnum billType) {
        // 获取当前日期格式化为yyMMdd
        String currentDate = DateUtil.format(new java.util.Date(), "yyMMdd");

        // 从Redis获取并递增序列号
        String serialNumber = getNextSerialNumber(billType.getCode(), currentDate);

        // 组装最终单号
        return billType.getCode() + "-" + currentDate + "-" + serialNumber;
    }

    /**
     * 从Redis获取下一个序列号
     *
     * @param billTypeCode 单据类型代码
     * @param currentDate 当前日期（格式：yyMMdd）
     * @return 格式化后的6位序列号
     */
    private String getNextSerialNumber(String billTypeCode, String currentDate) {
        // 构建Redis键：MY:SerialNumber:{billType}:{yyMMdd}
        String redisKey = String.format("MY:SerialNumber:%s:%s", billTypeCode, currentDate);

        // 使用INCR命令获取并递增序列号
        Long serialNumber = stringRedisTemplate.opsForValue().increment(redisKey);

        // 首次创建键时设置24小时过期时间
        if (serialNumber != null && serialNumber == 1) {
            stringRedisTemplate.expire(redisKey, 24, TimeUnit.HOURS);
            log.info("创建新的序列号键: {}, 设置24小时过期", redisKey);
        }

        // 格式化为6位数字（前导零填充）
        return String.format("%06d", serialNumber != null ? serialNumber : 1);
    }

    /**
     * 从查询结果中提取delivery_bill_details并去重
     *
     * @param mergedResults SQL查询结果
     * @return 去重后的单据编号集合
     */
    private Set<String> extractUniqueBillDetails(List<Map<String, Object>> mergedResults) {
        if (mergedResults == null || mergedResults.isEmpty()) {
            return Collections.emptySet();
        }

        Set<String> uniqueBillNos = new HashSet<>();

        for (Map<String, Object> result : mergedResults) {
            String deliveryBillDetails = (String) result.get("delivery_bill_details");
            if (StringUtils.isNotBlank(deliveryBillDetails)) {
                // 分割合并的单据编号
                String[] billNos = deliveryBillDetails.split(",");
                uniqueBillNos.addAll(Arrays.asList(billNos));
            }
        }

        return uniqueBillNos;
    }

    /**
     * 构建带有字段替换的SQL
     *
     * @param sqlTemplate          SQL模板，使用#1、#2等作为占位符
     * @param mergeOrderFieldsList 合单字段列表
     * @param whereConditions      WHERE条件Map，可为null
     * @param havingCondition      HAVING条件，可为null
     * @return 替换后的SQL字符串
     */
    public String buildSqlWithFields(String sqlTemplate, List<CqMergeOrderFieldsDO> mergeOrderFieldsList,
            Map<String, Object> whereConditions, String havingCondition) {
        log.info("开始构建SQL，模板：{}，字段数量：{}", sqlTemplate, mergeOrderFieldsList.size());

        if (StringUtils.isBlank(sqlTemplate)) {
            log.error("SQL模板为空");
            throw new IllegalArgumentException("SQL模板不能为空");
        }

        if (mergeOrderFieldsList == null || mergeOrderFieldsList.isEmpty()) {
            log.error("合单字段列表为空");
            throw new IllegalArgumentException("合单字段列表不能为空");
        }

        StringBuilder resultSql = new StringBuilder(sqlTemplate);

        // 构建字段部分SQL片段
        String fieldsForSelect = buildFieldsForSelectClause(mergeOrderFieldsList);
        String fieldsForGroupBy = buildFieldsForGroupByClause(mergeOrderFieldsList);

        // 替换占位符
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("#1", fieldsForSelect);
        placeholders.put("#GROUP_BY", fieldsForGroupBy);

        // 执行替换
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            String placeholder = entry.getKey();
            String replacement = entry.getValue();

            int placeholderIndex = resultSql.indexOf(placeholder);
            if (placeholderIndex != -1) {
                resultSql.replace(placeholderIndex, placeholderIndex + placeholder.length(), replacement);
            }
        }

        // 处理WHERE条件
        if (whereConditions != null && !whereConditions.isEmpty()) {
            appendWhereClause(resultSql, whereConditions);
        }

        // 处理HAVING条件
        if (StringUtils.isNotBlank(havingCondition)) {
            appendHavingClause(resultSql, havingCondition);
        }

        String finalSql = resultSql.toString();
        log.info("SQL构建完成：{}", finalSql);
        return finalSql;
    }

    /**
     * 构建用于SELECT子句的字段部分
     *
     * @param mergeOrderFieldsList 合单字段列表
     * @return SELECT子句中的字段部分
     */
    private String buildFieldsForSelectClause(List<CqMergeOrderFieldsDO> mergeOrderFieldsList) {
        if (mergeOrderFieldsList.isEmpty()) {
            return "";
        }

        StringJoiner selectFields = new StringJoiner(", ");

        for (CqMergeOrderFieldsDO field : mergeOrderFieldsList) {
            String tableName = field.getTableName();
            String fieldFlag = field.getFieldFlag();

            if (StringUtils.isNotBlank(tableName) && StringUtils.isNotBlank(fieldFlag)) {
                selectFields.add(tableName + "." + fieldFlag);
            }
        }

        return selectFields.toString();
    }

    /**
     * 构建用于GROUP BY子句的字段部分
     *
     * @param mergeOrderFieldsList 合单字段列表
     * @return GROUP BY子句中的字段部分
     */
    private String buildFieldsForGroupByClause(List<CqMergeOrderFieldsDO> mergeOrderFieldsList) {
        return buildFieldsForSelectClause(mergeOrderFieldsList);
    }

    /**
     * 添加WHERE子句
     *
     * @param sql             SQL构建器
     * @param whereConditions WHERE条件Map
     */
    private void appendWhereClause(StringBuilder sql, Map<String, Object> whereConditions) {
        if (whereConditions.isEmpty()) {
            return;
        }

        // 检查SQL中是否已有WHERE子句
        boolean hasWhereClause = sql.toString().toUpperCase().contains("WHERE");

        if (hasWhereClause) {
            sql.append(" AND ");
        } else {
            sql.append(" WHERE ");
        }

        StringJoiner whereJoiner = new StringJoiner(" AND ");

        for (Map.Entry<String, Object> entry : whereConditions.entrySet()) {
            String field = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                whereJoiner.add(field + " = '" + value + "'");
            } else if (value instanceof Number) {
                whereJoiner.add(field + " = " + value);
            } else if (value == null) {
                whereJoiner.add(field + " IS NULL");
            } else {
                whereJoiner.add(field + " = '" + value.toString() + "'");
            }
        }

        sql.append(whereJoiner.toString());
    }

    /**
     * 添加HAVING子句
     *
     * @param sql             SQL构建器
     * @param havingCondition HAVING条件
     */
    private void appendHavingClause(StringBuilder sql, String havingCondition) {
        if (StringUtils.isBlank(havingCondition)) {
            return;
        }

        // 检查SQL中是否已有HAVING子句
        boolean hasHavingClause = sql.toString().toUpperCase().contains("HAVING");

        if (hasHavingClause) {
            sql.append(" AND ");
        } else {
            sql.append(" HAVING ");
        }

        sql.append(havingCondition);
    }

    /**
     * 设置其他出库单的预算和领用类型信息
     *
     * @param reqDTO 其他出库单请求DTO
     * @param mergedMainRecord 合并后的主表记录
     */
    private void setBudgetAndRequisitionInfo(OtherOutboundSaveReqDTO reqDTO, Map<String, Object> mergedMainRecord) {
        // // 成本中心
        // Object costCenter = mergedMainRecord.get(FIELD_COST_CENTER);
        // if (costCenter != null) {
        //     reqDTO.setYd_costcenternum(costCenter.toString());
        // }
        // // 预算项目
        // Object budgetAccount = mergedMainRecord.get(FIELD_BUDGET_ACCOUNT);
        // if (budgetAccount != null) {
        //     reqDTO.setYd_zbudgetitem(budgetAccount.toString());
        // }
        // 预留单号
        Object reservedOrderNo = mergedMainRecord.get(FIELD_RESERVED_ORDER_NO);
        if (reservedOrderNo != null) {
            reqDTO.setYd_reservedorderno(reservedOrderNo.toString());
        }
        // 领用类型
        Object requisitionUse = mergedMainRecord.get(FIELD_REQUISITION_USE);
        if (requisitionUse != null) {
            reqDTO.setYd_zissuetype(requisitionUse.toString());
        }
    }

    /**
     * 设置其他出库单的库存组织仓库信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     * @param baseDataMaps 基础数据Map集合
     */
    private void setWarehouseInfoOfInvOrgForOtherOutbound(OtherOutboundSaveReqDTO.BillEntry billEntry, Map<String, Object> mergedDetailRecord, BaseDataMaps baseDataMaps) {
        Object stockIdObj = mergedDetailRecord.get(FIELD_INV_ORG_STOCK_ID);
        if (stockIdObj != null) {
            Long stockId = Long.valueOf(String.valueOf(stockIdObj));
            CqBdWarehouseDO warehouse = baseDataMaps.getWarehouseMap().get(stockId);
            if (warehouse != null && warehouse.getNumber() != null) {
                billEntry.setYd_invorgstock_number(warehouse.getNumber());
            } else {
                log.warn("未找到库存组织仓库信息，仓库ID：{}", stockId);
            }
        }
    }

    /**
     * 设置其他出库单的组套信息
     *
     * @param billEntry 单据明细
     * @param mergedDetailRecord 合并后的明细记录
     */
    private void setProPackageInfoForOtherOutbound(OtherOutboundSaveReqDTO.BillEntry billEntry, Map<String, Object> mergedDetailRecord) {
        Object isProPackageObj = mergedDetailRecord.get(FIELD_IS_PRO_PACKAGE);
        boolean isProPackage = isProPackageObj != null && "1".equals(String.valueOf(isProPackageObj));
        billEntry.setYd_isbom(isProPackage);
        // 是否组装品=是，取对应【发货明细-商品明细分录】货品编码，否则为空 
        if(isProPackage) {
            // 货品编号（OMS明细）-组套编码
            billEntry.setYd_bomnum(String.valueOf(mergedDetailRecord.get(FIELD_OMS_GOODS_NO)));
        }
    }

    /**
     * 设置品牌分拆单信息（销售出库单）
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     */
    private void setBrandSplitBillInfo(SalesOutboundSaveSubmitAuditReqDTO reqDTO, Map<String, Object> mergedMainRecord) {
        Object isBrandSplitBill = mergedMainRecord.get(FIELD_IS_BRAND_SPLIT_BILL);
        if (isBrandSplitBill != null) {
            String brandSplitBillStr = String.valueOf(isBrandSplitBill);
            reqDTO.setYdIssplit("1".equals(brandSplitBillStr));
        } else {
            reqDTO.setYdIssplit(false);
        }
    }

    /**
     * 设置品牌分拆单信息（其他出库单）
     *
     * @param reqDTO 请求DTO
     * @param mergedMainRecord 合并后的主表记录
     */
    private void setBrandSplitBillInfo(OtherOutboundSaveReqDTO reqDTO, Map<String, Object> mergedMainRecord) {
        Object isBrandSplitBill = mergedMainRecord.get(FIELD_IS_BRAND_SPLIT_BILL);
        if (isBrandSplitBill != null) {
            String brandSplitBillStr = String.valueOf(isBrandSplitBill);
            reqDTO.setYd_isbrandsum("1".equals(brandSplitBillStr));
        } else {
            reqDTO.setYd_isbrandsum(false);
        }
    }

    /**
     * 更新参与合单标记
     * 从上游单据信息列表中提取单据编号并更新参与合单标记
     * 同时根据下游单号状态更新结算状态
     *
     * @param upstreamBillInfoList 上游单据信息列表
     */
    private void updateParticipateInCombineMark(List<UpstreamBillInfo> upstreamBillInfoList) {
        // 从上游单据信息列表中提取单据编号
        List<String> billNos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(upstreamBillInfoList)) {
            billNos = upstreamBillInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(UpstreamBillInfo::getBillNo)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }
        
        if (!CollectionUtils.isEmpty(billNos)) {
            log.info("更新参与合单标记，单据数量：{}", billNos.size());
            cqDeliveryDetailService.updateParticipateInCombineByBillNo(billNos);
            
            // 新增逻辑：根据下游单号状态更新结算状态
            updateSettleStatusByDownstreamBillStatus(billNos);
        }
    }

    /**
     * 根据下游单号状态更新结算状态
     * 
     * @param billNos 单据编号列表
     */
    private void updateSettleStatusByDownstreamBillStatus(List<String> billNos) {
        try {
            log.info("开始根据下游单号状态更新结算状态，单据数量：{}", billNos.size());
            
            for (String billNo : billNos) {
                // 查询该单据对应的发货明细和源明细分录数据
                List<CqDeliveryDetailWithSourceEntriesDTO> deliveryDetailsWithEntries = 
                    cqDeliveryDetailService.getDeliveryDetailsWithSourceEntriesByBillNos(Arrays.asList(billNo));
                
                if (CollectionUtils.isEmpty(deliveryDetailsWithEntries)) {
                    log.warn("单据 {} 未找到发货明细数据", billNo);
                    continue;
                }
                
                // 获取所有源明细分录
                List<CqSourceDeliveryDetailEntryDO> allSourceEntries = new ArrayList<>();
                for (CqDeliveryDetailWithSourceEntriesDTO dto : deliveryDetailsWithEntries) {
                    if (dto.getSourceEntries() != null) {
                        allSourceEntries.addAll(dto.getSourceEntries());
                    }
                }
                
                if (allSourceEntries.isEmpty()) {
                    log.warn("单据 {} 未找到源明细分录数据", billNo);
                    continue;
                }
                
                // 过滤出 isSplitExcludeMat 不等于 1 的数据
                List<CqSourceDeliveryDetailEntryDO> validEntries = allSourceEntries.stream()
                    .filter(entry -> entry.getIsSplitExcludeMat() == null || 
                                   !Integer.valueOf(1).equals(entry.getIsSplitExcludeMat()))
                    .collect(Collectors.toList());
                
                if (validEntries.isEmpty()) {
                    log.debug("单据 {} 没有有效的源明细分录数据（排除拆分排除物料）", billNo);
                    continue;
                }
                
                // 统计下游单号状态
                long totalCount = validEntries.size();
                long filledCount = validEntries.stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getDownstreamBillNo()))
                    .count();
                
                log.debug("单据 {} 统计结果：总数={}, 已填充下游单号数={}", billNo, totalCount, filledCount);
                
                // 根据统计结果更新结算状态
                String newSettleStatus = null;
                if (filledCount == totalCount && totalCount > 0) {
                    // 所有行的下游单号都不为空，更新为完全下推（6）
                    newSettleStatus = "6";
                    log.info("单据 {} 所有明细行下游单号已填充，更新结算状态为完全下推(6)", billNo);
                } else if (filledCount > 0) {
                    // 部分行的下游单号不为空，更新为部分下推（5）
                    newSettleStatus = "5";
                    log.info("单据 {} 部分明细行下游单号已填充，更新结算状态为部分下推(5)", billNo);
                }
                
                // 执行更新操作
                if (newSettleStatus != null) {
                    cqDeliveryDetailService.updateSettleStatusByBillNo(billNo, newSettleStatus, "");
                    log.info("成功更新单据 {} 的结算状态为 {}", billNo, newSettleStatus);
                }
            }
            
            log.info("完成根据下游单号状态更新结算状态");
            
        } catch (Exception e) {
            log.error("根据下游单号状态更新结算状态时发生异常", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 校验销售出库单数量和金额
     * 汇总统计reqDTO的Billentry的qty和amountandtax，并与发货明细表统计值进行比对
     *
     * @param reqDTO 销售出库单请求DTO
     * @param mergedEntryIds 合并后的明细ID列表
     */
    private void validateSalesOutboundTotals(SalesOutboundSaveSubmitAuditReqDTO reqDTO, List<Long> mergedEntryIds) {
        // 从请求DTO中获取明细条目列表
        List<SalesOutboundSaveSubmitAuditReqDTO.BillEntry> billEntries = reqDTO.getBillentry();
        if (CollUtil.isEmpty(billEntries)) {
            log.warn("销售出库单明细为空，无法进行数量和金额统计");
            return;
        }

        // 统计明细的数量和金额
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (SalesOutboundSaveSubmitAuditReqDTO.BillEntry entry : billEntries) {
            // 累加数量
            if (entry.getQty() != null) {
                totalQty = totalQty.add(entry.getQty());
            }

            // 计算并累加金额（含税单价 * 数量）
            if (entry.getPriceandtax() != null && entry.getQty() != null) {
                BigDecimal entryAmount = entry.getPriceandtax().multiply(entry.getQty()).setScale(2, RoundingMode.HALF_UP);
                totalAmount = totalAmount.add(entryAmount);
            }
        }

        // 从发货明细表中查询统计数据
        Map<String, BigDecimal> deliveryTotals = cqDeliveryDetailService.getTotalsByEntryIds(mergedEntryIds);
        BigDecimal deliveryTotalQty = deliveryTotals.getOrDefault("totalQty", BigDecimal.ZERO);
        BigDecimal deliveryTotalAmount = deliveryTotals.getOrDefault("totalAmount", BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);

        // 计算差异值
        BigDecimal qtyDiff = totalQty.subtract(deliveryTotalQty).abs();
        BigDecimal amountDiff = totalAmount.subtract(deliveryTotalAmount).abs();
        
        // 设置容差阈值为0，不允许任何误差
        BigDecimal qtyTolerance = BigDecimal.ZERO;
        BigDecimal amountTolerance = BigDecimal.ZERO;
        
        boolean qtyInTolerance = qtyDiff.compareTo(qtyTolerance) == 0;
        boolean amountInTolerance = amountDiff.compareTo(amountTolerance) == 0;
        
        // 验证数量和金额是否在容差范围内，不在则抛出异常
        if (qtyInTolerance && amountInTolerance) {
            log.info("销售出库单统计验证通过：订单号={}, 明细数量={}, 明细金额={}, 发货明细数量={}, 发货明细金额={}",
                    reqDTO.getBillno(), totalQty, totalAmount, deliveryTotalQty, deliveryTotalAmount);
        } else {
            String errorMsg = String.format("销售出库单统计验证失败：订单号=%s, 明细数量=%s, 明细金额=%s, 发货明细数量=%s, 发货明细金额=%s, 数量差异=%s, 金额差异=%s",
                    reqDTO.getBillno(), totalQty, totalAmount, deliveryTotalQty, deliveryTotalAmount, qtyDiff, amountDiff);
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    /**
     * 校验其他出库单数量和金额
     * 汇总统计reqDTO的billentry的qty和金额，并与发货明细表统计值进行比对
     *
     * @param reqDTO 其他出库单请求DTO
     * @param mergedEntryIds 合并后的明细ID列表
     */
    private void validateOtherOutboundTotals(OtherOutboundSaveReqDTO reqDTO, List<Long> mergedEntryIds) {
        // 从请求DTO中获取明细条目列表
        List<OtherOutboundSaveReqDTO.BillEntry> billEntries = reqDTO.getBillentry();
        if (CollUtil.isEmpty(billEntries)) {
            log.warn("其他出库单明细为空，无法进行数量和金额统计");
            return;
        }

        // 统计明细的数量和金额
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (OtherOutboundSaveReqDTO.BillEntry entry : billEntries) {
            // 累加数量
            if (entry.getQty() != null) {
                totalQty = totalQty.add(entry.getQty());
            }

            // 累加金额（对于其他出库单，主要验证数量，金额通常为零或不适用）
            // 如需计算金额，可根据实际业务需求调整
        }

        // 从发货明细表中查询统计数据
        Map<String, BigDecimal> deliveryTotals = cqDeliveryDetailService.getTotalsByEntryIds(mergedEntryIds);
        BigDecimal deliveryTotalQty = deliveryTotals.getOrDefault("totalQty", BigDecimal.ZERO);
        BigDecimal deliveryTotalAmount = deliveryTotals.getOrDefault("totalAmount", BigDecimal.ZERO);

        // 计算差异值，主要关注数量差异
        BigDecimal qtyDiff = totalQty.subtract(deliveryTotalQty).abs();
        
        // 设置容差阈值为0，不允许任何误差
        BigDecimal qtyTolerance = BigDecimal.ZERO;
        
        boolean qtyInTolerance = qtyDiff.compareTo(qtyTolerance) == 0;
        
        // 验证数量是否在容差范围内，不在则抛出异常
        if (qtyInTolerance) {
            log.info("其他出库单统计验证通过：订单号={}, 明细数量={}, 发货明细数量={}",
                    reqDTO.getBillno(), totalQty, deliveryTotalQty);
        } else {
            String errorMsg = String.format("其他出库单统计验证失败：订单号=%s, 明细数量=%s, 发货明细数量=%s, 数量差异=%s",
                    reqDTO.getBillno(), totalQty, deliveryTotalQty, qtyDiff);
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }
    /**
     * 记录分批处理的性能统计信息到Redis（可选）
     *
     * @param ruleCode 规则编码
     * @param dayCount 处理天数
     * @param totalRecords 总记录数
     * @param totalTime 总耗时
     */
    private void recordPerformanceStats(String ruleCode, int dayCount, int totalRecords, long totalTime) {
        try {
            String statsKey = String.format("performance:batch_process:%s:%s", ruleCode, DateUtil.format(new Date(), "yyyyMMdd"));
            Map<String, String> stats = new HashMap<>();
            stats.put("dayCount", String.valueOf(dayCount));
            stats.put("totalRecords", String.valueOf(totalRecords));
            stats.put("totalTime", String.valueOf(totalTime));
            stats.put("avgTimePerDay", dayCount > 0 ? String.valueOf(totalTime / dayCount) : "0");
            stats.put("avgRecordsPerDay", dayCount > 0 ? String.valueOf(totalRecords / dayCount) : "0");
            stats.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            stringRedisTemplate.opsForHash().putAll(statsKey, stats);
            stringRedisTemplate.expire(statsKey, 7, TimeUnit.DAYS); // 保留7天
            
            log.info("[recordPerformanceStats][性能统计已记录][key={}]", statsKey);
        } catch (Exception e) {
            log.warn("[recordPerformanceStats][记录性能统计异常]", e);
        }
    }

    /**
     * 解析下推销售出库单任务参数
     * 
     * @param param JSON格式的参数字符串，支持格式：{"billNos": ["单号1", "单号2", ...], "mergeRuleCode": "规则编码"}
     * @return 解析后的参数对象
     */
    @Override
    public DeliveryDetailPushSaleOutParamsDTO parsePushSaleOutParams(String param) {
        DeliveryDetailPushSaleOutParamsDTO paramsDTO = new DeliveryDetailPushSaleOutParamsDTO();
        
        if (StrUtil.isNotBlank(param)) {
            try {
                // 解析JSON参数
                JSONObject jsonParam = JSONUtil.parseObj(param);
                paramsDTO.setBillNos(jsonParam.getBeanList("billNos", String.class));
                paramsDTO.setMergeRuleCode(jsonParam.getStr("mergeRuleCode"));
                
                log.info("[parsePushSaleOutParams][参数解析成功: billNos={}, mergeRuleCode={}]", 
                        paramsDTO.getBillNos() != null ? paramsDTO.getBillNos().size() : 0, 
                        paramsDTO.getMergeRuleCode());
            } catch (Exception e) {
                log.warn("[parsePushSaleOutParams][解析参数失败: {}]", param, e);
            }
        } else {
            log.info("[parsePushSaleOutParams][参数为空: {}]", param);
        }
        
        return paramsDTO;
    }

    /**
     * 发送合单规则异常通知到钉钉群
     * 
     * 该方法用于在合单规则处理过程中发生异常时，向钉钉群发送异常通知。
     * 通知内容包括合单规则编号、异常类型、异常信息、发生时间等关键信息。
     * 
     * @param ruleCode 合单规则编号，用于标识出现异常的具体规则
     * @param exception 异常对象，包含异常类型和异常信息
     * @param methodName 发生异常的方法名称，用于定位异常发生位置
     * @param additionalInfo 额外的上下文信息，如处理日期、影响范围等
     */
    private void sendMergeRuleExceptionNotification(String ruleCode, Exception exception, String methodName, String additionalInfo) {
        try {
            // 格式化当前时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 获取异常信息
            String exceptionType = exception.getClass().getSimpleName();
            String exceptionMessage = exception.getMessage() != null ? exception.getMessage() : "无异常信息";
            
            // 获取简化的堆栈跟踪信息（只取前5行关键信息）
            String stackTrace = getSimplifiedStackTrace(exception);
            
            // 构建Markdown格式的异常通知消息
            String title = "❌ 合单规则处理异常通知";
            String content = String.format(
                "## ❌ 合单规则处理异常通知\n\n" +
                "**发生时间**: %s\n\n" +
                "**合单规则编号**: `%s`\n\n" +
                "**异常方法**: `%s`\n\n" +
                "**异常类型**: `%s`\n\n" +
                "**异常信息**: %s\n\n" +
                "%s" +
                "**堆栈跟踪**:\n\n```java\n%s\n```\n\n" +
                "---\n\n" +
                "**处理建议**: 请及时检查该合单规则的配置和执行状态，排查异常原因并进行相应处理。\n\n" +
                "*此消息由合单规则处理系统自动发送*",
                currentTime,
                ruleCode,
                methodName,
                exceptionType,
                exceptionMessage,
                additionalInfo != null ? "**附加信息**: " + additionalInfo + "\n\n" : "",
                stackTrace
            );
            
            // 发送钉钉消息到异常通知机器人
            boolean success = dingDingUtils.sendMarkdownMessage(title, content, EXCEPTION_NOTIFICATION_ROBOT);
            
            if (success) {
                log.info("[sendMergeRuleExceptionNotification][钉钉异常通知发送成功][规则编号: {}][方法: {}]", ruleCode, methodName);
            } else {
                log.warn("[sendMergeRuleExceptionNotification][钉钉异常通知发送失败][规则编号: {}][方法: {}]", ruleCode, methodName);
            }
            
        } catch (Exception e) {
            // 确保钉钉通知异常不影响主业务流程
            log.warn("[sendMergeRuleExceptionNotification][发送钉钉异常通知时发生异常][规则编号: {}][方法: {}][异常: {}]", 
                    ruleCode, methodName, e.getMessage(), e);
        }
    }

    /**
     * 获取简化的堆栈跟踪信息
     * 
     * 为了避免钉钉消息过长，只提取关键的堆栈跟踪信息（前5行）
     * 
     * @param exception 异常对象
     * @return 简化的堆栈跟踪字符串
     */
    private String getSimplifiedStackTrace(Exception exception) {
        StackTraceElement[] stackTrace = exception.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return "无堆栈跟踪信息";
        }
        
        StringBuilder sb = new StringBuilder();
        int maxLines = Math.min(MAX_STACK_TRACE_LINES, stackTrace.length);
        
        for (int i = 0; i < maxLines; i++) {
            StackTraceElement element = stackTrace[i];
            sb.append("at ").append(element.toString()).append("\n");
        }
        
        if (stackTrace.length > maxLines) {
            sb.append("... 还有 ").append(stackTrace.length - maxLines).append(" 行");
        }
        
        return sb.toString();
    }
}

// -- 给主表添加复合索引
// CREATE INDEX idx_d_filter ON tk_yd_fhmx(
//   fk_yd_combofield_xyd,
//   fbillstatus,
//   fk_yd_iserror,
//   fk_yd_settlestatus,
//   fk_yd_isnotsale,
//   fk_yd_datefield_fhrq,
//   fbillno
// );

// -- 给关联表添加复合索引
// CREATE INDEX idx_e_fk_downstream ON tk_yd_src_fhmxentry(fid, fk_yd_downstreambillno);

// -- 给子查询表添加复合索引
// CREATE INDEX idx_t1_fhmxdh_rowid ON tk_yd_fhmxdhqtck(fk_yd_textfield_fhmxdh, fk_yd_rowid);
// CREATE INDEX idx_t2_fhmxdh_rowid ON tk_yd_fhmxdh(fk_yd_textfield_fhmxdh, fk_yd_rowid);

// -- 给日期字段添加索引
// CREATE INDEX idx_d_datefield ON tk_yd_fhmx(fk_yd_datefield_fhrq);


// 下推关联客户对应关系表

// CREATE INDEX idx_fk_yd_combofield_hzcl_fk_yd_textfield 
// ON tk_yd_entryentitydykh (fk_yd_combofield_hzcl, fk_yd_textfield);

// CREATE INDEX idx_fk_yd_textfield_dpbh ON tk_yd_fhmx(fk_yd_textfield_dpbh);




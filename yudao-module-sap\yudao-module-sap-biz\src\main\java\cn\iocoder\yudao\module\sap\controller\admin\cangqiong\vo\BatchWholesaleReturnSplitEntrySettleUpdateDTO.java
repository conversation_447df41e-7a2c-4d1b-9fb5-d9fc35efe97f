package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import lombok.Data;
import java.util.List;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesaleReturnSplitEntryDO;

/**
 * 批量更新批发退货单拆分明细DTO
 */
@Data
public class BatchWholesaleReturnSplitEntrySettleUpdateDTO {

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 主表结算状态
     */
    private String mainSettleStatus;

    /**
     * 物料对应关系重复
     */
    private Boolean isMatRepeat = false;

    /**
     * 是否销售组织仓库不存在
     */
    private Boolean isNotExistSaleOrgStock = false;

    /**
     * 整单剔除物料
     */
    private Boolean isWholeOrderExcludeMaterial = false;

    /**
     * 是否库存组织仓库不存在
     */
    private Boolean isNotExistInvOrgStock = false;

    /**
     * 是否库存组织不存在
     */
    private Boolean isNotExistInvOrg = false;

    /**
     * 明细列表
     */
    private List<CqWholesaleReturnSplitEntryDO> entries;
} 
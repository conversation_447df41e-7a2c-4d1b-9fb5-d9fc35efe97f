package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 多库存品牌信息 DO
 * 
 * 表名：tk_yd_mulstockbrand
 */
@TableName("tk_yd_mulstockbrand")
@Data
public class CqMulstockbrandDO {

    /**
     * 主键ID
     */
    @TableId("fpkid")
    private Long pkid;

    /**
     * 分录ID
     */
    @TableField("fentryid")
    private Long entryId;

    /**
     * 品牌ID
     */
    @TableField("fbasedataid")
    private Long brandId;
} 
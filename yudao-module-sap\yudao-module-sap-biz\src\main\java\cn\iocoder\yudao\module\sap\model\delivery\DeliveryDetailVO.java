package cn.iocoder.yudao.module.sap.model.delivery;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 发货明细表VO
 */
@Data
public class DeliveryDetailVO {

    /**
     * 单据编号
     */
    @JSONField(name = "billno")
    private String billno;

    /**
     * 来源类型
     */
    @JSONField(name = "yd_lylx")
    private String ydLylx;

    /**
     * 商店id
     */
    @JSONField(name = "yd_sdid")
    private String ydSdid;

    /**
     * 订单状态
     */
    @JSONField(name = "yd_orderstatus")
    private Integer ydOrderstatus;

    /**
     * 快递编码
     */
    @JSONField(name = "yd_shippingcode")
    private String ydShippingcode;

    /**
     * 快递名称
     */
    @JSONField(name = "yd_shippingname")
    private String ydShippingname;

    /**
     * 快递单号
     */
    @JSONField(name = "yd_shippingsn")
    private String ydShippingsn;

    /**
     * 店铺名称
     */
    @JSONField(name = "yd_sdname")
    private String ydSdname;

    /**
     * 仓库名称
     */
    @JSONField(name = "yd_fhckmc")
    private String ydFhckmc;

    /**
     * 渠道代码
     */
    @JSONField(name = "yd_qdcode")
    private String ydQdcode;

    /**
     * 已付金额
     */
    @JSONField(name = "yd_payment")
    private BigDecimal ydPayment;

    /**
     * 平台活动价(淘宝订单)
     */
    @JSONField(name = "yd_totalfee")
    private BigDecimal ydTotalfee;

    /**
     * 是否换货单
     */
    @JSONField(name = "yd_ishh")
    private Boolean ydIshh;

    /**
     * 是否被拆分
     */
    @JSONField(name = "yd_issplit")
    private Boolean ydIssplit;

    /**
     * 是否拆分子单
     */
    @JSONField(name = "yd_issplitnew")
    private Boolean ydIssplitnew;

    /**
     * 是否被合并
     */
    @JSONField(name = "yd_iscombine")
    private Boolean ydIscombine;

    /**
     * 是否合并新单
     */
    @JSONField(name = "yd_iscombinenew")
    private Boolean ydIscombinenew;

    /**
     * 是否复制单
     */
    @JSONField(name = "yd_iscopy")
    private Boolean ydIscopy;

    /**
     * 财务状态
     */
    @JSONField(name = "yd_paystatus")
    private String ydPaystatus;

    /**
     * 退单物流状态
     */
    @JSONField(name = "yd_shippingstatus")
    private String ydShippingstatus;

    /**
     * 订单其他折让(整单折让)
     */
    @JSONField(name = "yd_otherdiscountfee")
    private BigDecimal ydOtherdiscountfee;

    /**
     * 买家应付金额
     */
    @JSONField(name = "yd_orderamount")
    private BigDecimal ydOrderamount;

    /**
     * 总重量(单位KG)
     */
    @JSONField(name = "yd_weigh")
    private BigDecimal ydWeigh;

    /**
     * 渠道名称
     */
    @JSONField(name = "yd_qdname")
    private String ydQdname;

    /**
     * 下单时间
     */
    @JSONField(name = "yd_addtime")
    private Date ydAddtime;

    /**
     * 支付时间
     */
    @JSONField(name = "yd_paytime")
    private Date ydPaytime;

    /**
     * 退单关联订单号
     */
    @JSONField(name = "yd_relatingordersn")
    private String ydRelatingordersn;

    /**
     * 单据类型 0:订单, 1:退单
     */
    @JSONField(name = "yd_ordertype")
    private String ydOrdertype;

    /**
     * 省
     */
    @JSONField(name = "yd_provincename")
    private String ydProvincename;

    /**
     * 市
     */
    @JSONField(name = "yd_cityname")
    private String ydCityname;

    /**
     * 区
     */
    @JSONField(name = "yd_districtname")
    private String ydDistrictname;

    /**
     * 收货地址
     */
    @JSONField(name = "yd_address")
    private String ydAddress;

    /**
     * E3出库时间
     */
    @JSONField(name = "yd_shippingtimeck")
    private Date ydShippingtimeck;

    /**
     * E3发货时间
     */
    @JSONField(name = "yd_shippingtimefh")
    private Date ydShippingtimefh;

    /**
     * 平台 1:E3, 2:旺店通, 3:吉客云, 4:万里牛
     */
    @JSONField(name = "yd_combofield_pt")
    private String ydCombofieldPt;

    /**
     * 订单编号
     */
    @JSONField(name = "yd_textfield_ddbh")
    private String ydTextfieldDdbh;

    /**
     * 店铺编号
     */
    @JSONField(name = "yd_textfield_dpbh")
    private String ydTextfieldDpbh;

    /**
     * 下游单号
     */
    @JSONField(name = "yd_textfield_xsckdh")
    private String ydTextfieldXsckdh;

    /**
     * 平台发货时间
     */
    @JSONField(name = "yd_datetimefield_xdsj")
    private String ydDatetimefieldXdsj;

    /**
     * 是否开票
     */
    @JSONField(name = "yd_checkboxfield_sfkp")
    private Boolean ydCheckboxfieldSfkp;

    /**
     * 是否手工
     */
    @JSONField(name = "yd_checkboxfield_sfsg")
    private Boolean ydCheckboxfieldSfsg;

    /**
     * 运费
     */
    @JSONField(name = "yd_decimalfield_yf")
    private BigDecimal ydDecimalfieldYf;

    /**
     * 发货仓库
     */
    @JSONField(name = "yd_textfield_ck")
    private String ydTextfieldCk;

    /**
     * 退货
     */
    @JSONField(name = "yd_checkboxfield_th")
    private Boolean ydCheckboxfieldTh;

    /**
     * 发货日期
     */
    @JSONField(name = "yd_datefield_fhrq")
    private Date ydDatefieldFhrq;

    /**
     * 是否非销售
     */
    @JSONField(name = "yd_isnotsale")
    private Boolean ydIsnotsale;

    /**
     * 领用用途(非销)
     */
    @JSONField(name = "yd_requisitionuse")
    private String ydRequisitionuse;

    /**
     * 申请部门(非销)
     */
    @JSONField(name = "yd_applydepart")
    private String ydApplydepart;

    /**
     * 预算科目(非销)
     */
    @JSONField(name = "yd_budgetaccount")
    private String ydBudgetaccount;

    /**
     * 预算承担部门(非销)
     */
    @JSONField(name = "yd_budgetdepart")
    private String ydBudgetdepart;

    /**
     * 预算承担公司(非销)
     */
    @JSONField(name = "yd_budgetcompany")
    private String ydBudgetcompany;

    /**
     * 店铺(非销)
     */
    @JSONField(name = "yd_notsaleshop")
    private String ydNotsaleshop;

    /**
     * 商家备注
     */
    @JSONField(name = "yd_merchantremark")
    private String ydMerchantremark;

    /**
     * 店铺组织
     */
    @JSONField(name = "yd_shop_org")
    private String ydShopOrg;

    /**
     * 汇率
     */
    @JSONField(name = "yd_exrate")
    private BigDecimal ydExrate;

    /**
     * 交易号
     */
    @JSONField(name = "yd_dealcode")
    private String ydDealcode;

    /**
     * 备注
     */
    @JSONField(name = "yd_bz")
    private String ydBz;

    /**
     * 总金额
     */
    @JSONField(name = "yd_totalamount")
    private BigDecimal ydTotalamount;

    /**
     * 结算币别.货币代码
     */
    @JSONField(name = "yd_currencyfield_number")
    private String ydCurrencyfieldNumber;

    /**
     * 明细条目
     */
    @JSONField(name = "yd_entryentity")
    private List<DeliveryDetailEntryVO> ydEntryentity;

    /**
     * 结算状态 [1:待结算, 2:结算异常, 3:已结算]
     */
    @JSONField(name = "yd_settlestatus")
    private String ydSettlestatus;

    /**
     * 是否异常
     */
    @JSONField(name = "yd_iserror")
    private Boolean ydIserror;

    /**
     * 异常情况 [0:无异常, 1:金额为负，请核对E3单据各分录行金额、数量, 2:数量缺漏，请核对E3单据各分录行金额、数量, 3:数量缺漏、金额为负，请核对E3单据各分录行金额、数量]
     */
    @JSONField(name = "yd_errorstate")
    private String ydErrorstate;

    /**
     * 预留单号（昵称）
     */
    @JSONField(name = "yd_reservedordernum")
    private String ydReservedordernum; 
    
} 
package cn.iocoder.yudao.module.sap.service.cangqiong.processor;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailRespVO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqDBCustomerRelationEntryDO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliverySettlementContext;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformShopKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 场景1处理器：非销售且客户对应关系为空
 * 
 * 处理逻辑：
 * 1. 调用 matchCustomerByMerchantRemark 匹配客户
 * 2. 如果匹配成功（返回值不为0），调用 data.setCqCustomerId(customerId) 设置客户ID
 * 3. 无论是否匹配成功，都调用 markCustomerNotExist 标记客户不存在
 * 4. 不需要设置店铺组织、下游单据类型和品牌分单相关属性
 */
@Slf4j
@Component
public class NonSaleNoRelationProcessor implements CustomerRelationProcessor {
    
    @Override
    public void process(DeliverySettlementContext data, 
                       CqDeliveryDetailRespVO deliveryDetail,
                       Map<PlatformShopKey, List<CqDBCustomerRelationEntryDO>> customerRelationEntriesMap,
                       Map<String, CqCustomerDO> customerMap,
                       Optional<CqDBCustomerRelationEntryDO> matchedRelation) {
        
        log.debug("[NonSaleNoRelationProcessor][处理非销售且客户对应关系为空场景，单据编号: {}]", deliveryDetail.getBillNo());
        
        // 1. 调用 matchCustomerByMerchantRemark 匹配客户
        Long customerId = matchCustomerByMerchantRemark(data, deliveryDetail, customerMap);
        
        // 2. 如果匹配成功（返回值不为0），设置客户ID
        if (customerId != 0L) {
            data.setCqCustomerId(customerId);
        }
        
        // 3. 无论是否匹配成功，都标记客户不存在
        markCustomerNotExist(data, deliveryDetail);
        
        // 4. 不需要设置店铺组织、下游单据类型和品牌分单相关属性
    }
    
    /**
     * 根据商家备注的客户（非销）匹配客户档案的客户编号获取ID
     */
    private Long matchCustomerByMerchantRemark(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail, Map<String, CqCustomerDO> customerMap) {
        String noSaleCust = data.getNotSaleInfo().getYdNotsalecust();
        CqCustomerDO customer = customerMap.get(noSaleCust);
        if (customer != null) {
            return customer.getFid();
        }
        return 0L;
    }
    
    /**
     * 标记客户不存在
     */
    private void markCustomerNotExist(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail) {
        log.error("[doSettle][单据编号: {} 未找到对应的客户关系配置，标记为客户不存在]", deliveryDetail.getBillNo());
        data.setIsNotExitCustomer(1);
    }
} 
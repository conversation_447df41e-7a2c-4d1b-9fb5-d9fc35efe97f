package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 批发通知单拆单明细表 DO
 *
 * <AUTHOR>
 */
@TableName("tk_yd_wholesalnot_spliten")
@KeySequence("tk_yd_wholesalnot_spliten_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode
@ToString
public class CqWholesaleNoticeSplitEntryDO {

    /**
     * 主表ID
     */
    @TableField("FId")
    private Long billId;

    /**
     * 明细表ID
     */
    @TableId(value = "FEntryId")
    private Long entryId;

    /**
     * 分录行号
     */
    @TableField("FSeq")
    private Integer seq;

    /**
     * 物料编码ID
     */
    @TableField("fk_yd_splitmaterialid")
    private Long splitMaterialId;

    /**
     * 品牌ID
     */
    @TableField("fk_yd_brandid")
    private Long brandId;

    /**
     * 数量
     */
    @TableField("fk_yd_splitqty")
    private BigDecimal splitQty;

    /**
     * 单价
     */
    @TableField("fk_yd_splitprice")
    private BigDecimal splitPrice;

    /**
     * 价税合计
     */
    @TableField("fk_yd_totaltaxamt")
    private BigDecimal totalTaxAmt;

    /**
     * 库存组织仓库ID
     */
    @TableField("fk_yd_splitinvorgstockid")
    private Long splitInvOrgStockId;

    /**
     * 销售组织仓库ID
     */
    @TableField("fk_yd_splitsalorgstockid")
    private Long splitSalOrgStockId;

    /**
     * 库存组织ID
     */
    @TableField("fk_yd_invorgid")
    private Long invOrgId;

    /**
     * 下游单号
     */
    @TableField("fk_yd_downstreambillno")
    private String downstreamBillNo;

    /**
     * 商品货号(OMS明细)
     */
    @TableField("fk_yd_omsgoodsn")
    private String omsGoodsNo;

    /**
     * 序号(OMS明细)
     */ 
    @TableField("fk_yd_omsrownum")
    private Integer omsRowNum;

    /**
     * 是否赠品
     */
    @TableField("fk_yd_isgift")
    private Boolean isGift = false;    

    /**
     * 是否剔除物料
     */
    @TableField("fk_yd_excludematsplit")
    private Boolean excludeMatSplit;

    /**
     * 物料类型
     */
    @TableField("fk_yd_matgroupid")
    private Long matGroupId;

    /**
     * 产品类型
     */
    @TableField("fk_yd_producttype")
    private String productType;
    
} 
package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.mysql.customer.CqCustomerMapper;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.sap.enums.ErrorCodeConstants.CUSTOMER_NOT_EXISTS;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 苍穹客户 Service 实现类
 */
@Service
@Validated
@Slf4j
@DS("cq_sys")
public class CqCustomerServiceImpl implements CqCustomerService {

    @Resource
    private CqCustomerMapper customerMapper;   

    @Override
    public CqCustomerDO getCustomer(Long id) {
        return customerMapper.selectById(id);
    }

    @Override
    public List<CqCustomerDO> getCustomerList(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return customerMapper.selectBatchIds(ids);
    }

    @Override
    public List<CqCustomerDO> getCustomerList(CqCustomerDO query) {
        // 创建查询条件
        LambdaQueryWrapperX<CqCustomerDO> wrapper = new LambdaQueryWrapperX<>();
        // 根据query中的非空字段添加条件
        if (query.getFnumber() != null) {
            wrapper.like(CqCustomerDO::getFnumber, query.getFnumber());
        }
        if (query.getFname() != null) {
            wrapper.like(CqCustomerDO::getFname, query.getFname());
        }
        if (query.getFstatus() != null) {
            wrapper.eq(CqCustomerDO::getFstatus, query.getFstatus());
        }
        if (query.getFenable() != null) {
            wrapper.eq(CqCustomerDO::getFenable, query.getFenable());
        }
        if (query.getFkYdEasid() != null) {
            wrapper.eq(CqCustomerDO::getFkYdEasid, query.getFkYdEasid());
        }
        
        // 按ID降序排序
        wrapper.orderByDesc(CqCustomerDO::getFid);
        
        // 执行查询
        return customerMapper.selectList(wrapper);
    }

    @Override
    public CqCustomerDO getCustomerByNumber(String number) {
        return customerMapper.selectByNumber(number);
    }

    @Override
    public List<CqCustomerDO> getCustomersByNameLike(String name) {
        return customerMapper.selectByNameLike(name);
    }

    @Override
    public List<CqCustomerDO> getCustomersByStatus(String status) {
        return customerMapper.selectByStatus(status);
    }
    
    @Override
    public CqCustomerDO getCustomerByEasId(String easId) {
        return customerMapper.selectByEasId(easId);
    }
    
    @Override
    public List<CqCustomerDO> getApprovedAndEnabledCustomers() {
        LambdaQueryWrapperX<CqCustomerDO> wrapper = new LambdaQueryWrapperX<>();
        // 已审核状态为 "C"
        wrapper.eq(CqCustomerDO::getFstatus, "C");
        // 未禁用状态为 "1"
        wrapper.eq(CqCustomerDO::getFenable, "1");
        // 按ID降序排序
        wrapper.orderByDesc(CqCustomerDO::getFid);
        
        return customerMapper.selectList(wrapper);
    }
    
    @Override
    public Map<Long, CqCustomerDO> getAllCustomersMap() {
        // 日志记录
        log.info("[getAllCustomersMap][开始获取所有客户Map]");
        
        try {
            // 查询所有客户数据
            List<CqCustomerDO> customerList = customerMapper.selectList(new LambdaQueryWrapper<>());
            
            // 将列表转换为Map
            Map<Long, CqCustomerDO> customerMap = customerList.stream()
                    .collect(Collectors.toMap(
                            CqCustomerDO::getFid,  // 使用客户ID作为Map的键
                            Function.identity(),  // 客户对象本身作为Map的值
                            (existing, replacement) -> existing));  // 如果有重复键，保留第一个
            
            log.info("[getAllCustomersMap][获取所有客户Map成功，总数:{}]", customerMap.size());
            return customerMap;
        } catch (Exception e) {
            log.error("[getAllCustomersMap][获取所有客户Map异常]", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取所有已审核客户的Map（以编号为键）
     * 
     * 主要功能：
     * 1. 查询所有已审核状态（fstatus = "C"）的客户数据
     * 2. 将客户列表转换为Map结构，便于快速查找
     * 3. 使用客户编号（fnumber）作为Map的键，客户对象作为值
     * 4. 处理重复键的情况，保留第一个遇到的客户
     * 
     * 使用场景：
     * - 在结算服务中根据客户编号快速查找客户信息
     * - 避免重复查询数据库，提高性能
     * 
     * @return 客户Map，key为客户编号（String），value为客户对象（CqCustomerDO）
     *         如果查询异常则返回空的HashMap
     */
    @Override
    public Map<String, CqCustomerDO> getAllCustomersByNumberMap() {
        // 日志记录 - 记录方法开始执行
        log.info("[getAllCustomersByNumberMap][开始获取所有已审核客户Map（以编号为键）]");
        
        try {
            // 构建查询条件 - 只查询已审核的客户数据
            LambdaQueryWrapperX<CqCustomerDO> wrapper = new LambdaQueryWrapperX<>();
            // 设置查询条件：已审核状态为 "C"（Confirmed/已确认）
            wrapper.eq(CqCustomerDO::getFstatus, "C");
            
            // 执行数据库查询 - 获取所有符合条件的客户列表
            List<CqCustomerDO> customerList = customerMapper.selectList(wrapper);
            
            // 数据转换 - 将客户列表转换为以编号为键的Map结构
            Map<String, CqCustomerDO> customerMap = customerList.stream()
                    .collect(Collectors.toMap(
                            CqCustomerDO::getFnumber,  // Map的键：使用客户编号（fnumber）
                            Function.identity(),  // Map的值：客户对象本身
                            (existing, replacement) -> existing));  // 冲突处理：如果有重复编号，保留第一个
            
            // 成功日志 - 记录转换结果
            log.info("[getAllCustomersByNumberMap][获取所有已审核客户Map（以编号为键）成功，总数:{}]", customerMap.size());
            return customerMap;
        } catch (Exception e) {
            // 异常处理 - 记录错误日志并返回空Map
            log.error("[getAllCustomersByNumberMap][获取所有已审核客户Map（以编号为键）异常]", e);
            return new HashMap<>();
        }
    }
} 
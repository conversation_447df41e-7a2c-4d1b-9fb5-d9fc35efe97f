package cn.iocoder.yudao.module.sap.dal.dataobject.bom;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售BOM-主表
 * 表名：tk_yd_bom
 */
@TableName("tk_yd_bom")
@Data
public class CqBomDO {
    
    /**
     * id
     */
    @TableId("FID")
    private Long id;
    
    /**
     * 编码
     */
    @TableField("fnumber")
    private String number;
    
    /**
     * 名称
     */
    @TableField("fname")
    private String name;
    
    /**
     * 数据状态,枚举:A:暂存 B:已提交 C:已审核
     */
    @TableField("fstatus")
    private String status;
    
    /**
     * 创建人
     */
    @TableField("fcreatorid")
    private Long creatorId;
    
    /**
     * 修改人
     */
    @TableField("fmodifierid")
    private Long modifierId;
    
    /**
     * 使用状态,枚举:0:禁用 1:可用
     */
    @TableField("fenable")
    private String enable;
    
    /**
     * 创建时间
     */
    @TableField("fcreatetime")
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    @TableField("fmodifytime")
    private LocalDateTime modifyTime;
    
    /**
     * 主数据内码
     */
    @TableField("fmasterid")
    private Long masterId;
    
    /**
     * 主商品编码
     */
    @TableField("fk_yd_bomno")
    private String bomNo;
    
    /**
     * 主商品名称
     */
    @TableField("fk_yd_bomname")
    private String bomName;
    
    /**
     * 组织
     */
    @TableField("fk_yd_orgid")
    private Long orgId;
    
    /**
     * 客户
     */
    @TableField("fk_yd_customerid")
    private Long customerId;
    
    /**
     * 主商品金额
     */
    @TableField("fk_yd_totalamount")
    private BigDecimal totalAmount;
} 
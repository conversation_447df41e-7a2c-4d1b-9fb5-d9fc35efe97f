package cn.iocoder.yudao.module.sap.model.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 内部交易关系缓存键
 * 用于在Map中索引内部交易关系数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InnerRelKey {
    
    /**
     * 原客户ID
     */
    private Long oriCustomerId;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 产品类型
     */
    private String mulProductType;
    
    /**
     * 物料组ID
     */
    private Long matgroupId;
    
    /**
     * 二参数构造函数（向后兼容）
     * 
     * @param oriCustomerId 原客户ID
     * @param brandId 品牌ID
     */
    public InnerRelKey(Long oriCustomerId, Long brandId) {
        this.oriCustomerId = oriCustomerId;
        this.brandId = brandId;
        this.mulProductType = "";
        this.matgroupId = 0L;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InnerRelKey that = (InnerRelKey) o;
        return Objects.equals(oriCustomerId, that.oriCustomerId) && 
               Objects.equals(brandId, that.brandId) &&
               Objects.equals(mulProductType, that.mulProductType) &&
               Objects.equals(matgroupId, that.matgroupId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(oriCustomerId, brandId, mulProductType, matgroupId);
    }
    
    @Override
    public String toString() {
        return "InnerRelKey{" +
                "oriCustomerId=" + oriCustomerId +
                ", brandId=" + brandId +
                ", mulProductType='" + mulProductType + '\'' +
                ", matgroupId=" + matgroupId +
                '}';
    }
} 
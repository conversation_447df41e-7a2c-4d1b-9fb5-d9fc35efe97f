package cn.iocoder.yudao.module.sap.model.delivery;

import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqCkdygxEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqDBCustomerRelationEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.warehouse.CqBdWarehouseDO;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 主单结算缓存数据
 * 
 * 用于封装主单结算过程中需要的所有预加载缓存数据，
 * 减少方法参数传递的复杂度，提高代码可维护性。
 * 
 * <AUTHOR>
 */
@Data
public class MainSettleCacheData {
    
    /**
     * 平台合并匹配参数
     * 用于判断是否启用平台合并匹配功能
     */
    private String platformMergeParam;
    
    /**
     * 排除仓库编码集合
     * 包含所有需要排除的仓库编码
     */
    private Set<String> excludedWarehouseCodes;
    
    /**
     * 仓库对应关系缓存映射
     * Key: PlatformWarehouseKey (平台+仓库编码+产品类型)
     * Value: 仓库对应关系条目列表
     * 注意：支持产品类型拆分，mulProductType字段中的逗号分隔值会被拆分为独立的条目
     */
    private Map<PlatformWarehouseKey, List<CqCkdygxEntryDO>> ckdygxEntriesMap;
    
    /**
     * 仓库信息缓存映射
     * Key: 仓库ID
     * Value: 仓库信息对象
     */
    private Map<Long, CqBdWarehouseDO> warehouseMap;
    
    /**
     * 客户关系缓存映射
     * Key: PlatformShopKey (平台+店铺+匹配类型)
     * Value: 客户关系条目列表
     */
    private Map<PlatformShopKey, List<CqDBCustomerRelationEntryDO>> customerRelationEntriesMap;

    /**
     * 客户信息缓存映射
     * Key: 客户编号
     * Value: 客户信息对象
     */
    private Map<String, CqCustomerDO> customerMap;
    
    /**
     * 缓存加载开始时间戳
     * 用于统计缓存加载耗时
     */
    private long cacheLoadStartTime;
    
    /**
     * 缓存加载完成时间戳
     * 用于统计缓存加载耗时
     */
    private long cacheLoadEndTime;
    
    /**
     * 获取缓存加载耗时
     * 
     * @return 加载耗时（毫秒）
     */
    public long getCacheLoadDuration() {
        return cacheLoadEndTime - cacheLoadStartTime;
    }
    
    /**
     * 检查缓存数据是否完整
     * 
     * @return true-缓存数据完整，false-缓存数据不完整
     */
    public boolean isComplete() {
        return platformMergeParam != null 
            && excludedWarehouseCodes != null 
            && ckdygxEntriesMap != null 
            && warehouseMap != null 
            && customerRelationEntriesMap != null
            && customerMap != null;
    }
} 
package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 内部交易关系物料组信息 DO
 * 
 * 表名：tk_yd_mulinnerrelmatgroup
 */
@TableName("tk_yd_mulinnerrelmatgroup")
@Data
public class CqMulinnerrelmatgroupDO {

    /**
     * 主键ID
     */
    @TableId("fpkid")
    private Long pkid;

    /**
     * 主表ID
     */
    @TableField("fid")
    private Long id;

    /**
     * 物料组ID
     */
    @TableField("fbasedataid")
    private Long matgroupId;
} 
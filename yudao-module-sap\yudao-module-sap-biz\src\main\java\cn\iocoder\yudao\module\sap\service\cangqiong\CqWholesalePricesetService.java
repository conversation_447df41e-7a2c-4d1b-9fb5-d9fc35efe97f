package cn.iocoder.yudao.module.sap.service.cangqiong;

import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesalePricesetLDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMule3channelDO;
import cn.iocoder.yudao.module.sap.model.pricing.WholesalePricesetKey;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 批发取价表 Service 接口
 */
public interface CqWholesalePricesetService {

    /**
     * 获取批发取价表
     *
     * @param id 批发取价表ID
     * @return 批发取价表
     */
    CqWholesalePricesetDO getWholesalePriceset(Long id);

    /**
     * 根据编码获取批发取价表
     *
     * @param number 编码
     * @return 批发取价表
     */
    CqWholesalePricesetDO getWholesalePricesetByNumber(String number);

    /**
     * 根据状态获取批发取价表列表
     *
     * @param status 数据状态
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByStatus(String status);

    /**
     * 根据启用状态获取批发取价表列表
     *
     * @param enable 启用状态
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByEnable(String enable);

    /**
     * 根据单据类型获取批发取价表列表
     *
     * @param billType 单据类型
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByBillType(String billType);

    /**
     * 根据价格类型获取批发取价表列表
     *
     * @param pricingType 价格类型
     * @return 批发取价表列表
     */
    List<CqWholesalePricesetDO> getWholesalePricesetListByPricingType(String pricingType);

    /**
     * 获取所有批发取价表Map
     *
     * @return ID为key的批发取价表Map
     */
    Map<Long, CqWholesalePricesetDO> getAllWholesalePricesetsMap();

    /**
     * 获取批发取价表的多语言信息
     *
     * @param id 主表ID
     * @return 多语言信息列表
     */
    List<CqWholesalePricesetLDO> getWholesalePricesetLangList(Long id);

    /**
     * 获取批发取价表的渠道多选信息
     *
     * @param id 主表ID
     * @return 渠道多选信息列表
     */
    List<CqMule3channelDO> getWholesalePricesetChannelList(Long id);

    /**
     * 批量查询所有已审核的批发取价表映射关系
     * 
     * @return Map<组合键, 价格类型> 以WholesalePricesetKey为键的价格类型映射Map
     */
    Map<WholesalePricesetKey, String> getAllWholesalePricesetMap();
} 
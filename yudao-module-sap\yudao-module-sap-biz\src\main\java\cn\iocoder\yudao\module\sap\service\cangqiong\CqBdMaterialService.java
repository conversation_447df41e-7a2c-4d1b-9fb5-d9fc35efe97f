package cn.iocoder.yudao.module.sap.service.cangqiong;

import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialWithEntriesDTO;

import java.util.List;
import java.util.Map;

/**
 * 物料-主表 Service 接口
 */
public interface CqBdMaterialService {

    /**
     * 获取物料信息
     *
     * @param id 物料ID
     * @return 物料信息
     */
    CqBdMaterialDO getMaterial(Long id);

    /**
     * 获取物料完整信息（包含辅助属性）
     *
     * @param id 物料ID
     * @return 物料完整信息
     */
    CqBdMaterialWithEntriesDTO getMaterialWithEntries(Long id);

    /**
     * 根据物料编号获取物料信息
     *
     * @param number 物料编号
     * @return 物料信息
     */
    CqBdMaterialDO getMaterialByNumber(String number);

    /**
     * 根据物料编号获取物料完整信息（包含辅助属性）
     *
     * @param number 物料编号
     * @return 物料完整信息
     */
    CqBdMaterialWithEntriesDTO getMaterialWithEntriesByNumber(String number);

    /**
     * 根据物料名称查询物料列表
     *
     * @param name 物料名称
     * @return 物料列表
     */
    List<CqBdMaterialDO> getMaterialListByName(String name);
    
    /**
     * 根据物料编号列表批量查询物料信息
     *
     * @param numbers 物料编号列表
     * @return 物料信息列表
     */
    List<CqBdMaterialDO> getMaterialListByNumbers(List<String> numbers);

    /**
     * 组合条件查询物料列表
     *
     * @param number 物料编号，模糊匹配
     * @param name 物料名称，模糊匹配
     * @param brandId 品牌ID
     * @param categoryId 品类ID
     * @param status 状态
     * @return 物料列表
     */
    List<CqBdMaterialDO> queryMaterialList(String number, String name, Long brandId, Long categoryId, String status);

    /**
     * 查询待审核的物料列表
     *
     * @return 待审核的物料列表
     */
    List<CqBdMaterialDO> getPendingAuditMaterialList();

    /**
     * 根据助记码查询物料列表
     *
     * @param helpCode 助记码
     * @return 物料列表
     */
    List<CqBdMaterialDO> getMaterialListByHelpCode(String helpCode);

//    /**
//     * 查询所有已审核的物料列表（用于批量查询优化）
//     *
//     * @return 所有已审核的物料列表
//     */
//    List<CqBdMaterialDO> getAllApprovedMaterials();
    
    /**
     * 获取所有物料的Map
     *
     * @return 所有物料的Map，key为物料ID，value为物料对象
     */
    Map<Long, CqBdMaterialDO> getAllMaterialsMap();
    
    /**
     * 获取所有物料列表（包含分组信息）
     * 
     * @return 所有物料列表，包含分组信息
     */
    List<CqBdMaterialDO> getAllMaterialsWithGroup();
} 
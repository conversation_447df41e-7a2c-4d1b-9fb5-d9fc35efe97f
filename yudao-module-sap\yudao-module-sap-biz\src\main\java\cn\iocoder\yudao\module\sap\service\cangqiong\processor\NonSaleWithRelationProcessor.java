package cn.iocoder.yudao.module.sap.service.cangqiong.processor;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailRespVO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqDBCustomerRelationEntryDO;
import cn.iocoder.yudao.module.sap.model.delivery.DeliverySettlementContext;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformShopKey;
import cn.iocoder.yudao.module.sap.enums.DownstreamBillTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 场景2处理器：非销售且客户对应关系存在
 * 
 * 处理逻辑：
 * 1. 调用 matchCustomerByMerchantRemark 匹配客户
 * 2. 如果匹配成功，设置客户ID；如果匹配失败，调用 markCustomerNotExist
 * 3. 无论客户匹配结果如何，都需要设置以下属性：
 *    - 店铺组织：data.setShopOrgId(matchedRelation.get().getCorrespondingOrgId())
 *    - 下游单据类型：调用 setupDownstreamBillInfo() 方法
 *    - 品牌分单：根据 matchedRelation.get().getIsBrandSeparated() 设置 data.setIsBrandSplitBill()
 */
@Slf4j
@Component
public class NonSaleWithRelationProcessor implements CustomerRelationProcessor {
    
    @Override
    public void process(DeliverySettlementContext data, 
                       CqDeliveryDetailRespVO deliveryDetail,
                       Map<PlatformShopKey, List<CqDBCustomerRelationEntryDO>> customerRelationEntriesMap,
                       Map<String, CqCustomerDO> customerMap,
                       Optional<CqDBCustomerRelationEntryDO> matchedRelation) {
        
        log.debug("[NonSaleWithRelationProcessor][处理非销售且客户对应关系存在场景，单据编号: {}]", deliveryDetail.getBillNo());
        
        CqDBCustomerRelationEntryDO customerRelation = matchedRelation.get();
        
        // 1. 调用 matchCustomerByMerchantRemark 匹配客户
        Long customerId = matchCustomerByMerchantRemark(data, deliveryDetail, customerMap);
        
        // 2. 根据匹配结果处理客户ID设置或标记客户不存在
        if (customerId != 0L) {
            data.setCqCustomerId(customerId);
        } else {
            markCustomerNotExist(data, deliveryDetail);
        }
        
        // 3. 无论客户匹配结果如何，都需要设置以下属性
        // 设置店铺组织ID
        data.setShopOrgId(customerRelation.getCorrespondingOrgId());
        
        // 设置下游单据类型
        setupDownstreamBillInfo(data, customerRelation);
        
        // 设置品牌分单标识
        Integer isBrandSplit = customerRelation.getIsBrandSeparated();
        if (isBrandSplit != null && isBrandSplit == 1) {
            data.setIsBrandSplitBill(1);
        }
    }
    
    /**
     * 根据商家备注的客户（非销）匹配客户档案的客户编号获取ID
     */
    private Long matchCustomerByMerchantRemark(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail, Map<String, CqCustomerDO> customerMap) {
        String noSaleCust = data.getNotSaleInfo().getYdNotsalecust();
        CqCustomerDO customer = customerMap.get(noSaleCust);
        if (customer != null) {
            return customer.getFid();
        }
        return 0L;
    }
    
    /**
     * 标记客户不存在
     */
    private void markCustomerNotExist(DeliverySettlementContext data, CqDeliveryDetailRespVO deliveryDetail) {
        log.error("[doSettle][单据编号: {} 未找到对应的客户关系配置，标记为客户不存在]", deliveryDetail.getBillNo());
        data.setIsNotExitCustomer(1);
    }
    
    /**
     * 设置下游单据类型和EAS传递标志
     */
    private void setupDownstreamBillInfo(DeliverySettlementContext data, CqDBCustomerRelationEntryDO customerRelation) {
        // 获取汇总类型
        String sumType = customerRelation.getSummaryType();
        
        // 使用新枚举类根据汇总类型和是否非销确定下游单据类型
        data.setDownstreamBillType(DownstreamBillTypeEnum.getDownstreamBillType(sumType, data.getIsNotSale()));
        
        // 如果下游单据类型为空，则不传EAS
        if (data.getDownstreamBillType() != null && data.getDownstreamBillType().isEmpty()) {
            data.setIsNotSendEas(1);
        }
    }
} 
package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

// 管理后台 - 苍穹发货明细 Response VO
@Data
public class CqDeliveryDetailRespVO {

    // ==================== 基础信息 ====================
    // 主键ID
    private Long id;

    // 单据编号
    private String fbillno;
    
    // 单据编号
    private String billNo;

    // 单据状态
    private String fbillstatus;
    
    // 单据状态
    private String billStatus;

    // ==================== 审计信息 ====================
    // 创建人ID
    private Long creatorId;

    // 修改人ID
    private Long modifierId;

    // 审核人ID
    private Long fauditorid;
    
    // 审核人ID
    private Long auditorId;

    // 审核日期
    private LocalDateTime fauditdate;
    
    // 审核日期
    private LocalDateTime auditDate;

    // 修改时间
    private LocalDateTime modifyTime;

    // 创建时间
    private LocalDateTime createTime;

    // 更新时间
    private LocalDateTime updateTime;

    // ==================== 订单基本信息 ====================
    // 平台
    private String platform;

    // 订单编号
    private String orderNo;

    // 店铺编号
    private String shopNo;

    // 下游单号
    private String downstreamNo;

    // 平台发货时间
    private LocalDateTime platformDeliveryTime;

    // 下游单据类型
    private String downstreamType;

    // 备注
    private String remark;

    // 交易号
    private String dealCode;

    // 店铺名称
    private String shopName;

    // 渠道代码
    private String channelCode;

    // 渠道名称
    private String channelName;
    
    // 单据类型
    private String orderType;
    
    // 来源类型
    private String sourceType;
    
    // 商店id
    private String storeId;
    
    // 店铺
    private String notSaleShop;
    
    // 店铺组织
    private Long shopOrg;
    
    // 订单状态
    private Long orderStatus;
    
    // 财务状态
    private String payStatus;
    
    // 结算状态
    private String settleStatus;
    
    // 客户
    private Long customerId;
    
    // 商家备注
    private String merchantRemark;

    // ==================== 状态标记 ====================
    // 是否退货
    private String isReturn;
    
    // 是否开票
    private String isInvoiced;
    
    // 是否手工
    private String isManual;
    
    // 客户不存在
    private String customerNotExist;
    
    // 仓库不存在
    private String warehouseNotExist;
    
    // 物料不存在
    private Integer materialNotExist;
    
    // 不传EAS
    private String notTransferToEAS;
    
    // 参与合单
    private String participateInCombine;
    
    // 整单剔除物料
    private String excludeMaterialInOrder;
    
    // 物料未审核或物料库存,销售信息未审核
    private String materialNotAudited;
    
    // 排除仓库
    private Integer excludeWarehouse;
    
    // 物料对应关系重复
    private String materialRefDuplicated;
    
    // 同步到eas成功
    private String syncToEASSuccess;
    
    // 单据异常
    private String isError;
    
    // 是否换货单
    private String isExchangeOrder;
    
    // 是否被拆分
    private String isSplit;
    
    // 是否拆分子单
    private String isSplitNew;
    
    // 是否被合并
    private String isCombined;
    
    // 是否合并新单
    private String isCombinedNew;
    
    // 是否复制单
    private String isCopy;
    
    // 是否包含组装品
    private Integer hasAssembly;
    
    // 是否组装拆分
    private String isAssemblySplit;
    
    // 价税合计
    private BigDecimal isAmountAndTax;
    
    // 是否非销
    private Integer isNotSale;

    // 是否按品牌分单
    private Integer isBrandSplitBill;
    
    // 销售组织仓库不存在标记
    private Integer saleOrgStockNotExist;
    
    // 库存组织仓库不存在标记
    private Integer invOrgStockNotExist;

    // ==================== 金额信息 ====================
    // 运费
    private BigDecimal freight;

    // 订单总金额
    private BigDecimal totalAmount;
    
    // 已付金额
    private BigDecimal payment;
    
    // 平台活动价
    private BigDecimal platformActivityPrice;
    
    // 订单其他折让
    private BigDecimal otherDiscountFee;
    
    // 买家应付金额
    private BigDecimal orderAmount;
    
    // 总重量
    private BigDecimal weight;
    
    // 卖家优惠(子)
    private BigDecimal goodOffer;
    
    // 卖家券
    private BigDecimal sellerOffer;
    
    // 汇率
    private BigDecimal exRate;
    
    // 剔除总金额
    private BigDecimal excludeAmount;
    
    // 结算币别
    private Long settleCurrencyId;
    
    // 税
    private BigDecimal tax;
    
    // 订单总金额(亚马逊)
    private BigDecimal totalAmountAmz;
    
    // 货币类别
    private String currency;

    // ==================== 物流信息 ====================
    // 发货仓库
    private String warehouse;
    
    // 仓库名称
    private String warehouseName;

    // 快递编码
    private String expressCode;

    // 快递名称
    private String expressName;

    // 快递单号
    private String expressNo;
    
    // 退单物流状态
    private String shippingStatus;

    // ==================== 地址信息 ====================
    // 省
    private String province;

    // 市
    private String city;

    // 区
    private String district;

    // 收货地址
    private String address;

    // ==================== 时间信息 ====================
    // 发货日期
    private LocalDateTime deliveryDate;
    
    // E3出库时间
    private LocalDateTime shippingTimeCk;
    
    // E3发货时间
    private LocalDateTime shippingTimeFh;
    
    // 下单时间
    private LocalDateTime addTime;
    
    // 支付时间
    private LocalDateTime payTime;

    // ==================== 关联信息 ====================
    // 换货原始交易号
    private String oriDealCode;
    
    // 换货原始订单号
    private String oriOrderSn;
    
    // 退单关联订单号
    private String relatingOrderSn;

    // 退单关联订单是否不存在
    private String relatedOrderNotExists;
    
    // 异常情况
    private String errorState;
    
    // 失败原因
    private String failReason;
    
    // 预算承担公司
    private String budgetCompany;
    
    // 预算承担部门
    private String budgetDepartment;
    
    // 预算科目
    private String budgetAccount;
    
    // 申请部门
    private String applyDepartment;
    
    // 领用用途
    private String requisitionUse;

    // 苍穹客户id
    private Long cqCustomerId;

    // 成本中心
    private String costCenter;

    // 基金科目
    private String fundAccount;

    // 客户(非销)
    private String notSaleCust;

    // 客户类型(非销)
    private String notSaleCustType;

    // 预留单号
    private String reservedOrderNum;

    // ==================== 分录信息 ====================
    // 明细列表
    private List<CqDeliveryDetailEntryRespVO> entries;
    
    // 组装明细列表
    private List<CqSourceDeliveryDetailEntryRespVO> sourceEntries;

    // 管理后台 - 苍穹发货明细分录 Response VO
    @Data
    public static class CqDeliveryDetailEntryRespVO {

        // ========== 基础标识字段 ==========
        // 主键ID
        private Long entryId;

        // 主表ID
        private Long mainId;

        // 序号
        private Long seq;

        // ========== 商品基本信息 ==========
        // 货品编号
        private String materialNo;
        
        // 货号
        private String goodsSn;
        
        // 货号id
        private String goodsId;
        
        // 商品名
        private String goodsName;
        
        // barcode
        private String barcode;
        
        // sku_id
        private String skuId;

        // ========== 数量和金额 ==========
        // 数量
        private BigDecimal quantity;

        // 单价
        private BigDecimal price;

        // 总金额
        private BigDecimal totalAmount;
        
        // 商品单价
        private BigDecimal goodsPrice;
        
        // 商品网店在售价格
        private BigDecimal shopPrice;
        
        // 价税合计
        private BigDecimal amountAndTax;
        
        // laz单价
        private BigDecimal paidPrice;
        
        // 数量
        private BigDecimal e3Qty;
        
        // 总金额
        private BigDecimal detailTotalAmount;

        // ========== 批次信息 ==========
        // 批号
        private String batchNo;

        // 生产日期
        private LocalDateTime productionDate;

        // 到期日
        private LocalDateTime expirationDate;

        // ========== 订单和交易信息 ==========
        // 商品原始订单号
        private String originalOrderSn;
        
        // 商品原始交易编号
        private String originalDealCode;
        
        // 子交易号
        private String subDealCode;
        
        // 平台商品ID
        private String numIid;
        
        // 商品行id
        private String rowId;

        // ========== 物流和成本 ==========
        // 平均物流成本
        private BigDecimal shareShippingFee;
        
        // 实际入库数量
        private Long numberReturnSj;
        
        // 均摊价合计
        private BigDecimal shareAmount;
        
        // 平均物流成本
        private BigDecimal avLogisticsCost;

        // ========== 平台特定信息 ==========
        // 平台补贴金额
        private BigDecimal tradeRebate;
        
        // laz运费补贴
        private BigDecimal lazFreight;
        
        // E3订单明细内套餐
        private String tcSku;

        // ========== 达人/主播信息 ==========
        // 达人ID
        private String anchorId;
        
        // 达人主播
        private String anchorName;

        // ========== 物料处理标记 ==========
        // 物料不存在
        private String mxMaterialNotExist;
        
        // 物料对应关系重复
        private String mxMaterialRefDuplicated;
        
        // 是否赠品
        private String isGift;

        // 是否组装品
        private Integer isAssembly;
        
        // 剔除物料
        private String excludeMaterial;
        
        // 组装品编码
        private String mainMatNum;

        // 单品物料ID
        private Long singleMaterialId;
        
        // 商品上架单价
        private BigDecimal shelvesPrice;
        
        // 商品总折扣
        private BigDecimal totalDiscountAmount;
    }
    
    // 管理后台 - 苍穹组装明细分录 Response VO
    @Data
    public static class CqSourceDeliveryDetailEntryRespVO {

        // ========== 基础标识字段 ==========
        // 分录ID
        private Long entryId;

        // 主表ID
        private Long mainId;

        // 序号
        private Long seq;

        // ========== 物料基本信息 ==========
        // 货品编号(旧)
        private String materialNoOld;

        // 物料ID
        private Long materialId;

        // 批号
        private String batchNo;

        // 生产日期
        private LocalDateTime productionDate;

        // 到期日
        private LocalDateTime expirationDate;

        // ========== 数量与价格信息 ==========
        // 数量(旧)
        private BigDecimal quantityOld;

        // 单价(旧)
        private BigDecimal priceOld;

        // 总金额(旧)
        private BigDecimal totalAmountOld;

        // 数量
        private BigDecimal quantity;

        // 价税合计
        private BigDecimal totalTaxAmount;

        // 分销供货价
        private BigDecimal distSupplyPrice;

        // 含税单价
        private BigDecimal taxPrice;

        // ========== 仓储、客户与组织信息 ==========
        // 销售组织仓库ID
        private Long saleOrgStockId;

        // 库存组织仓库ID
        private Long invOrgStockId;

        // 仓库类型
        private String stockTypeId;

        // 销售组织ID
        private Long saleOrgId;

        // 库存组织ID
        private Long invOrgId;

        // 客户ID
        private Long customerId;

        // ========== 状态标记信息 ==========
        // 是否组装品
        private Integer isAssembly;

        // 是否已拆分
        private Integer isPageSplit;

        // 是否赠品
        private Integer isGift;

        // 拆分失败原因
        private String srcError;
        
        // ========== OMS相关信息 ==========
        // OMS行号
        private Long omsRowNum;
        
        // 货品编号
        private String omsGoodsSn;

        // 分销供货价不存在标记
        private Integer distPriceNotExist;
        
        // 平均物流成本
        private BigDecimal avgLogisticsCost;
        
        // 商品上架单价
        private BigDecimal shelvesPrice;
        
        // 商品总折扣
        private BigDecimal totalDiscountAmount;
        
        // ========== 物料分组信息 ==========
        // 物料组ID
        private Long matgroupId;
        
        // 产品类型
        private String productType;
    }
} 
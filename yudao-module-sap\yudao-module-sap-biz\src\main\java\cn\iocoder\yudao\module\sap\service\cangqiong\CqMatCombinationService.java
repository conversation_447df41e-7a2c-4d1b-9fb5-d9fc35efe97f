//package cn.iocoder.yudao.module.sap.service.cangqiong;
//
//import java.util.List;
//import java.util.Map;
//
//// import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationEntryDetailVO;
//import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationRespVO;
//import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombinationDO;
//import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombinationEntryDO;
//import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombSubEntryDO;
//
//public interface CqMatCombinationService {
//    CqMatCombinationDO getCqMatCombination(Long id);
//
//    // 根据单据编号查询主表信息
//    CqMatCombinationDO getCqMatCombinationByBillNo(String billNo);
//
//    // 根据主表ID查询子表信息
//    List<CqMatCombinationEntryDO> getCqMatCombinationEntries(Long id);
//
//    // 根据子表ID查询子子表信息
//    List<CqMatCombSubEntryDO> getCqMatCombSubEntries(Long entryId);
//
//    // 获取完整的主商品拆分信息（包含主表、子表、子子表）
//    CqMatCombinationRespVO getCqMatCombinationDetail(Long id);
//
//    // 根据主商品编码查询拆分信息
//    List<CqMatCombinationEntryDO> getEntriesByMainMatNum(String mainMatNum);
//
//    // 根据物料类型查询拆分信息
//    List<CqMatCombinationEntryDO> getEntriesByMatType(String matType);
//
//    // 判断指定主商品编码是否存在于拆分表中
//    boolean isMainMatNumCombined(String mainMatNum);
//
//    /**
//     * 根据主商品编码获取子表记录及对应的子子表记录
//     *
//     * @param mainMatNum 主商品编码
//     * @return 子表和子子表的组合数据列表
//     */
//    List<CqMatCombinationEntryDetailVO> getEntryDetailsByMainMatNum(String mainMatNum);
//
//    /**
//     * 批量查询所有物料组合关系，优化性能
//     * @return Map<物料编号, 组合明细列表>
//     */
//    Map<String, List<CqMatCombinationEntryDetailVO>> getAllMaterialCombinations();
//}
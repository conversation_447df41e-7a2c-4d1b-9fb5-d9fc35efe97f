package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 批发通知单主表 DO
 *
 * <AUTHOR>
 */
@TableName("tk_yd_wholesalenoticebill")
@KeySequence("tk_yd_wholesalenoticebill_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode
@ToString
public class CqWholesaleNoticeBillDO {

    // ========== 基础字段 ==========
    
    /**
     * ID
     */
    @TableId(value = "FId")
    private Long id;

    /**
     * 单据编号
     */
    @TableField("fbillno")
    private String billNo;

    /**
     * 单据状态
     */
    @TableField("fbillstatus")
    private String billStatus;

    /**
     * 通知单号
     */
    @TableField("fk_yd_noticebillno")
    private String noticeBillNo;

    /**
     * sap单号
     */
    @TableField("fk_yd_sap_billlno")
    private String sapBillNo;

    /**
     * 下游单号
     */
    @TableField("fk_yd_targetbillno")
    private String targetBillNo;

    // ========== 时间字段 ==========
    
    /**
     * 创建时间
     */
    @TableField("fcreatetime")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("fmodifytime")
    private LocalDateTime modifyTime;

    /**
     * 审核日期
     */
    @TableField("fauditdate")
    private LocalDateTime auditDate;

    /**
     * 业务日期
     */
    @TableField("fk_yd_bizdate")
    private LocalDateTime bizDate;

    // ========== 人员字段 ==========
    
    /**
     * 创建人
     */
    @TableField("fcreatorid")
    private Long creatorId;

    /**
     * 修改人
     */
    @TableField("fmodifierid")
    private Long modifierId;

    /**
     * 审核人
     */
    @TableField("fauditorid")
    private Long auditorId;

    // ========== 渠道客户字段 ==========
    
    /**
     * 渠道编码
     */
    @TableField("fk_yd_channelno")
    private String channelNo;

    /**
     * 渠道名称
     */
    @TableField("fk_yd_channelname")
    private String channelName;

    /**
     * E3客户编码
     */
    @TableField("fk_yd_customerno")
    private String customerNo;

    /**
     * E3客户名称
     */
    @TableField("fk_yd_customername")
    private String customerName;

    /**
     * 苍穹客户ID
     */
    @TableField("fk_yd_cqcustomerid")
    private Long cqCustomerId;

    // ========== 组织字段 ==========
    
    /**
     * 组织ID（销售组织）
     */
    @TableField("fk_yd_orgid")
    private Long orgId;

    /**
     * 组织编码(旧)
     */
    @TableField("fk_yd_orgnum")
    private String orgNum;

    /**
     * 组织名称(旧)
     */
    @TableField("fk_yd_orgname")
    private String orgName;

    // ========== 仓库字段 ==========
    
    /**
     * E3仓库编码
     */
    @TableField("fk_yd_warehouseno")
    private String warehouseNo;

    /**
     * E3仓库名称
     */
    @TableField("fk_yd_warehousename")
    private String warehouseName;

    /**
     * 中台仓库编码
     */
    @TableField("fk_yd_finalwarehouseno")
    private String finalWarehouseNo;

    /**
     * 苍穹库存组织仓库ID
     */
    @TableField("fk_yd_cqinvorgstockid")
    private Long cqInvOrgStockId;

    // ========== 业务配置字段 ==========
    
    /**
     * 平台
     * 枚举:1:E3 2:旺店通 3:吉客云 4:万里牛 5:新E3
     */
    @TableField("fk_yd_platform")
    private String platform;

    /**
     * 发货类型
     * 枚举:1:E3 2:旺店通 3:吉客云 4:万里牛 5:新E3
     */
    @TableField("fk_yd_shippingtype")
    private String shippingType;

    /**
     * 业务场景
     * 枚举:0:京东买断 1:传统分销
     */
    @TableField("fk_yd_businessscene")
    private String businessScene;

    /**
     * 是否按品牌分单
     */
    @TableField("fk_yd_isbrandsplitbill")
    private Boolean isBrandSplitBill;

    /**
     * 结算状态
     * 枚举:1:主表待结算 2:OMS明细待结算 3:拆单明细待结算 4:已结算+待下推
     */
    @TableField("fk_yd_settlestatus")
    private String settleStatus;

    // ========== 异常标志字段 ==========
    
    /**
     * 渠道组织不存在（销售组织）
     */
    @TableField("fk_yd_noorg")
    private Boolean noOrg;

    /**
     * 仓库不存在
     */
    @TableField("fk_yd_nowarehouse")
    private Boolean noWarehouse;

    /**
     * 客户不存在
     */
    @TableField("fk_yd_nocustomer")
    private Boolean noCustomer;

    /**
     * 物料不存在
     */
    @TableField("fk_yd_nomaterial")
    private Boolean noMaterial;

    /**
     * 物料对应关系重复
     */
    @TableField("fk_yd_matrepeat")
    private Boolean matRepeat;

    /**
     * 库存组织不存在
     */
    @TableField("fk_yd_invorgnotexist")
    private Boolean invOrgNotExist;

    /**
     * 渠道组织不存在标志
     */
    @TableField("fk_yd_notexistchannelorg")
    private Boolean notExistChannelOrg;

    /**
     * 客户不存在标志
     */
    @TableField("fk_yd_notexistcustomer")
    private Boolean notExistCustomer;

    /**
     * 销售组织仓库不存在标志
     */
    @TableField("fk_yd_notexistsalorgstock")
    private Boolean notExistSalOrgStock;

    /**
     * 库存组织仓库不存在标志
     */
    @TableField("fk_yd_notexistinvorgstock")
    private Boolean notExistInvOrgStock;

    /**
     * 物料不存在标志
     */
    @TableField("fk_yd_notexistmaterial")
    private Boolean notExistMaterial;

    // ========== 剔除标志字段 ==========
    
    /**
     * 剔除仓库标志
     */
    @TableField("fk_yd_excludestock")
    private Boolean excludeStock;

    /**
     * 剔除物料标志
     */
    @TableField("fk_yd_excludematerial")
    private Boolean excludeMaterial;

    // ========== 其他字段 ==========
    
    /**
     * 失败原因
     */
    @TableField("fk_yd_failreason")
    private String failReason;

    /**
     * 备注
     */
    @TableField("fk_yd_description")
    private String description;
} 
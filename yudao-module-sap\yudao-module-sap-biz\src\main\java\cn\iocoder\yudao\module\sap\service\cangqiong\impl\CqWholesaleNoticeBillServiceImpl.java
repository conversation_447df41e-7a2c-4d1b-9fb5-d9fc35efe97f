package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.BatchWholesaleNoticeBillEntrySettleUpdateDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqWholesaleNoticeBillDetailDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.WholesaleNoticeBillOMSEntrySettleUpdateDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.WholesaleNoticeBillSettleUpdateDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesaleNoticeBillDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesaleNoticeBillOMSEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesaleNoticeSplitEntryDO;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqWholesaleNoticeBillEntryMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqWholesaleNoticeBillMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqWholesaleNoticeSplitEntryMapper;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWholesaleNoticeBillService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.BatchWholesaleNoticeSplitEntrySettleUpdateDTO;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;
import java.time.LocalDateTime;

/**
 * 批发通知单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
@DS("cq_scm")
public class CqWholesaleNoticeBillServiceImpl implements CqWholesaleNoticeBillService {

    @Resource
    private CqWholesaleNoticeBillMapper wholesaleNoticeBillMapper;

    @Resource
    private CqWholesaleNoticeBillEntryMapper wholesaleNoticeBillEntryMapper;

    @Resource
    private CqWholesaleNoticeSplitEntryMapper wholesaleNoticeSplitEntryMapper;

    @Override
    public CqWholesaleNoticeBillDO getWholesaleNoticeBill(Long id) {
        return wholesaleNoticeBillMapper.selectById(id);
    }

    @Override
    public CqWholesaleNoticeBillDO getWholesaleNoticeBillByBillNo(String billNo) {
        return wholesaleNoticeBillMapper.selectByBillNo(billNo);
    }

    @Override
    public List<CqWholesaleNoticeBillOMSEntryDO> getWholesaleNoticeBillEntries(Long mainId) {
        return wholesaleNoticeBillEntryMapper.selectListByMainId(mainId);
    }

    @Override
    public List<CqWholesaleNoticeSplitEntryDO> getWholesaleNoticeSplitEntries(Long mainId) {
        return wholesaleNoticeSplitEntryMapper.selectListByMainId(mainId);
    }

    @Override
    public List<CqWholesaleNoticeBillDO> getWholesaleNoticeBillList(String billNo, String platform, 
                                                                  String channelNo, String customerNo) {
        return wholesaleNoticeBillMapper.selectList(billNo, platform, channelNo, customerNo);
    }

    @Override
    public boolean existsByBillNo(String billNo) {
        return wholesaleNoticeBillMapper.countByBillNo(billNo) > 0;
    }

    @Override
    public CqWholesaleNoticeBillDetailDTO getWholesaleNoticeBillDetail(String billNo) {
        // 1. 获取主单据
        CqWholesaleNoticeBillDO bill = this.getWholesaleNoticeBillByBillNo(billNo);
        if (bill == null) {
            return null;
        }
        
        // 2. 获取明细条目
        List<CqWholesaleNoticeBillOMSEntryDO> entries = this.getWholesaleNoticeBillEntries(bill.getId());
        
        // 3. 获取拆分条目
        List<CqWholesaleNoticeSplitEntryDO> splitEntries = this.getWholesaleNoticeSplitEntries(bill.getId());
        
        // 4. 构建并返回DTO
        return new CqWholesaleNoticeBillDetailDTO()
                .setBill(bill)
                .setEntries(entries)
                .setSplitEntries(splitEntries);
    }
    
    @Override
    public List<CqWholesaleNoticeBillDetailDTO> getWholesaleNoticeBillDetailsBySettleStatuses(String billNo, List<String> settleStatuses) {
        // 1. 参数校验
        if (settleStatuses == null || settleStatuses.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 2. 获取符合条件的主单据列表
        List<CqWholesaleNoticeBillDO> bills = wholesaleNoticeBillMapper.selectListBySettleStatuses(billNo, settleStatuses);
        if (bills.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 3. 构建详情DTO列表
        List<CqWholesaleNoticeBillDetailDTO> resultList = new ArrayList<>(bills.size());
        for (CqWholesaleNoticeBillDO bill : bills) {
            // 4. 获取明细条目
            List<CqWholesaleNoticeBillOMSEntryDO> entries = this.getWholesaleNoticeBillEntries(bill.getId());
            
            // 5. 获取拆分条目
            List<CqWholesaleNoticeSplitEntryDO> splitEntries = this.getWholesaleNoticeSplitEntries(bill.getId());
            
            // 6. 构建DTO并添加到结果列表
            CqWholesaleNoticeBillDetailDTO detailDTO = new CqWholesaleNoticeBillDetailDTO()
                    .setBill(bill)
                    .setEntries(entries)
                    .setSplitEntries(splitEntries);
            
            resultList.add(detailDTO);
        }
        
        return resultList;
    }
    
    @Override
    public List<CqWholesaleNoticeBillDetailDTO> getWholesaleNoticeBillDetailsBySettleStatuses(List<String> billNos, List<String> settleStatuses) {
        // 1. 参数校验
        if (settleStatuses == null || settleStatuses.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 2. 获取符合条件的主单据列表
        List<CqWholesaleNoticeBillDO> bills = wholesaleNoticeBillMapper.selectListBySettleStatuses(billNos, settleStatuses);
        if (bills.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 3. 构建详情DTO列表
        List<CqWholesaleNoticeBillDetailDTO> resultList = new ArrayList<>(bills.size());
        for (CqWholesaleNoticeBillDO bill : bills) {
            // 4. 获取明细条目
            List<CqWholesaleNoticeBillOMSEntryDO> entries = this.getWholesaleNoticeBillEntries(bill.getId());
            
            // 5. 获取拆分条目
            List<CqWholesaleNoticeSplitEntryDO> splitEntries = this.getWholesaleNoticeSplitEntries(bill.getId());
            
            // 6. 构建DTO并添加到结果列表
            CqWholesaleNoticeBillDetailDTO detailDTO = new CqWholesaleNoticeBillDetailDTO()
                    .setBill(bill)
                    .setEntries(entries)
                    .setSplitEntries(splitEntries);
            
            resultList.add(detailDTO);
        }
        
        return resultList;
    }
    
    @Override
    public boolean updateWholesaleNoticeBillSettleInfoByBillNo(String billNo, WholesaleNoticeBillSettleUpdateDTO updateDTO) {
        // 1. 根据单据编号查询批发通知单
        CqWholesaleNoticeBillDO bill = wholesaleNoticeBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[updateWholesaleNoticeBillSettleInfoByBillNo][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 2. 创建更新对象
        CqWholesaleNoticeBillDO updateObj = new CqWholesaleNoticeBillDO();
        updateObj.setId(bill.getId()); // 设置批发通知单ID
        updateObj.setOrgId(updateDTO.getOrgId()); // 设置组织ID
        updateObj.setNotExistChannelOrg(updateDTO.getNotExistChannelOrg()); // 设置渠道组织是否不存在标志
        updateObj.setCqCustomerId(updateDTO.getCqCustomerId()); // 设置苍穹客户ID
        updateObj.setNotExistCustomer(updateDTO.getNotExistCustomer()); // 设置客户是否不存在标志
        updateObj.setIsBrandSplitBill(updateDTO.getIsBrandSplitBill()); // 设置是否按品牌分单
        updateObj.setExcludeStock(updateDTO.getExcludeStock()); // 设置是否剔除库存
//        updateObj.setInvOrgId(updateDTO.getInvOrgId()); // 设置库存组织ID（取消表头，改到表体）
        updateObj.setSettleStatus(updateDTO.getSettleStatus()); // 设置结算状态
        updateObj.setModifyTime(LocalDateTime.now()); // 设置修改时间（当前时间）
        updateObj.setBusinessScene(updateDTO.getYdBusinessscene()); // 设置业务场景

        // 3. 执行更新
        int updated = wholesaleNoticeBillMapper.updateById(updateObj);
        if (updated > 0) {
            log.info("[updateWholesaleNoticeBillSettleInfoByBillNo][单据编号: {} 结算信息更新成功]", billNo);
            return true;
        } else {
            log.warn("[updateWholesaleNoticeBillSettleInfoByBillNo][单据编号: {} 结算信息更新失败]", billNo);
            return false;
        }
    }
    
    @Override
    public boolean updateWholesaleNoticeBillEntrySettleInfo(String billNo, Long entryId, WholesaleNoticeBillOMSEntrySettleUpdateDTO updateDTO) {
        // 1. 根据单据编号查询批发通知单
        CqWholesaleNoticeBillDO bill = wholesaleNoticeBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[updateWholesaleNoticeBillEntrySettleInfo][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 2. 查询明细条目是否存在
        CqWholesaleNoticeBillOMSEntryDO entry = wholesaleNoticeBillEntryMapper.selectById(entryId);
        if (entry == null || !entry.getBillId().equals(bill.getId())) {
            log.warn("[updateWholesaleNoticeBillEntrySettleInfo][单据编号: {}, 明细ID: {} 不存在或不属于该单据]", billNo, entryId);
            return false;
        }
        
        // 3. 创建更新对象
        CqWholesaleNoticeBillOMSEntryDO updateObj = new CqWholesaleNoticeBillOMSEntryDO();
        updateObj.setEntryId(entryId);
        updateObj.setSingleMaterialId(updateDTO.getSingleMaterialId());
        updateObj.setNotExistMaterialOms(updateDTO.getIsNotExistMaterialOms());
        updateObj.setIsBom(updateDTO.getIsBom());
        updateObj.setIsMaterialRelationRepeat(updateDTO.getIsMaterialRelationRepeat());
        
        // 4. 执行更新
        int updated = wholesaleNoticeBillEntryMapper.updateById(updateObj);
        if (updated > 0) {
            log.info("[updateWholesaleNoticeBillEntrySettleInfo][单据编号: {}, 明细ID: {} 结算信息更新成功]", billNo, entryId);
            return true;
        } else {
            log.warn("[updateWholesaleNoticeBillEntrySettleInfo][单据编号: {}, 明细ID: {} 结算信息更新失败]", billNo, entryId);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateWholesaleNoticeBillEntriesSettleInfo(BatchWholesaleNoticeBillEntrySettleUpdateDTO batchUpdateDTO) {
        // 1. 获取参数
        String billNo = batchUpdateDTO.getBillNo();
        String mainSettleStatus = batchUpdateDTO.getMainSettleStatus();
        List<WholesaleNoticeBillOMSEntrySettleUpdateDTO> entries = batchUpdateDTO.getEntries();
        
        // 2. 参数校验
        if (billNo == null || billNo.isEmpty()) {
            log.warn("[batchUpdateWholesaleNoticeBillEntriesSettleInfo][单据编号为空]");
            return false;
        }
        
        if (CollectionUtils.isEmpty(entries)) {
            log.warn("[batchUpdateWholesaleNoticeBillEntriesSettleInfo][明细条目为空]");
            return false;
        }
        
        // 3. 根据单据编号查询批发通知单
        CqWholesaleNoticeBillDO bill = wholesaleNoticeBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[batchUpdateWholesaleNoticeBillEntriesSettleInfo][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 4. 更新明细条目
        boolean allSuccess = true;
        for (WholesaleNoticeBillOMSEntrySettleUpdateDTO entryUpdate : entries) {
            // 创建更新对象
            CqWholesaleNoticeBillOMSEntryDO updateObj = new CqWholesaleNoticeBillOMSEntryDO();
            updateObj.setEntryId(entryUpdate.getEntryId());
            updateObj.setSingleMaterialId(entryUpdate.getSingleMaterialId());
            updateObj.setExcludeMaterialOms(entryUpdate.getIsExcludedMaterialOms());
            updateObj.setIsBom(entryUpdate.getIsBom());
            updateObj.setIsMaterialRelationRepeat(entryUpdate.getIsMaterialRelationRepeat());
            updateObj.setIsErrorBill(entryUpdate.getIsErrorBill());
            updateObj.setErrorReason(entryUpdate.getErrorReason());
            updateObj.setNotExistMaterialOms(entryUpdate.getIsNotExistMaterialOms());
            
            // 执行更新
            int updated = wholesaleNoticeBillEntryMapper.updateById(updateObj);
            if (updated <= 0) {
                log.warn("[batchUpdateWholesaleNoticeBillEntriesSettleInfo][单据编号: {}, 明细ID: {} 结算信息更新失败]", 
                        billNo, entryUpdate.getEntryId());
                allSuccess = false;
            }
        }
        
        // 5. 如果主单据状态需要更新
        if (mainSettleStatus != null && !mainSettleStatus.isEmpty()) {
            CqWholesaleNoticeBillDO updateObj = new CqWholesaleNoticeBillDO();
            updateObj.setId(bill.getId());
            // 设置结算状态
            updateObj.setSettleStatus(mainSettleStatus);
            // 设置物料关系重复标识
            updateObj.setMatRepeat(batchUpdateDTO.getIsMatRepeat());
            // 设置修改时间为当前时间
            updateObj.setModifyTime(LocalDateTime.now());
            // 设置物料不存在标识
            updateObj.setNotExistMaterial(batchUpdateDTO.getIsNotExistMaterial());
            // 设置整单剔除物料标识
            updateObj.setExcludeMaterial(batchUpdateDTO.getIsWholeOrderExcludeMaterial());

            int updated = wholesaleNoticeBillMapper.updateById(updateObj);
            if (updated <= 0) {
                log.warn("[batchUpdateWholesaleNoticeBillEntriesSettleInfo][单据编号: {} 状态更新失败]", billNo);
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSplitEntries(BatchWholesaleNoticeSplitEntrySettleUpdateDTO batchUpdateDTO) {
        // 记录开始时间，用于性能日志
        long startTime = System.currentTimeMillis();
        
        // 1. 获取参数
        String billNo = batchUpdateDTO.getBillNo();
        String mainSettleStatus = batchUpdateDTO.getMainSettleStatus();
        List<CqWholesaleNoticeSplitEntryDO> entries = batchUpdateDTO.getEntries();
        
        log.info("[batchUpdateSplitEntries][开始处理批发通知单拆单明细, 单据编号: {}, 明细数量: {}]",
                billNo, entries != null ? entries.size() : 0);
        
        // 2. 参数校验
        if (billNo == null || billNo.isEmpty()) {
            log.warn("[batchUpdateSplitEntries][单据编号为空]");
            return false;
        }
        
        // 3. 根据单据编号查询批发通知单
        CqWholesaleNoticeBillDO bill = wholesaleNoticeBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[batchUpdateSplitEntries][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 4. 更新主表状态
        boolean updateMainSuccess = true;
        if (mainSettleStatus != null && !mainSettleStatus.isEmpty()) {
            CqWholesaleNoticeBillDO updateObj = new CqWholesaleNoticeBillDO();
            updateObj.setId(bill.getId());
            updateObj.setSettleStatus(mainSettleStatus);
            updateObj.setMatRepeat(batchUpdateDTO.getIsMatRepeat());
            updateObj.setModifyTime(LocalDateTime.now());// 修改时间（当前时间）  
            updateObj.setNotExistSalOrgStock(batchUpdateDTO.getIsNotExistSaleOrgStock());
            updateObj.setExcludeMaterial(batchUpdateDTO.getIsWholeOrderExcludeMaterial());
            updateObj.setInvOrgNotExist(batchUpdateDTO.getIsNotExistInvOrg());
            updateObj.setNotExistInvOrgStock(batchUpdateDTO.getIsNotExistInvOrgStock());
            // 注释掉可能不存在的方法调用
            // updateObj.setNotExistSaleOrgStock(batchUpdateDTO.getIsNotExistSaleOrgStock());
            
            int updated = wholesaleNoticeBillMapper.updateById(updateObj);
            if (updated <= 0) {
                log.warn("[batchUpdateSplitEntries][单据编号: {} 状态更新失败]", billNo);
                updateMainSuccess = false;
            } else {
                log.info("[batchUpdateSplitEntries][单据编号: {} 状态更新成功, 新状态: {}]", 
                        billNo, mainSettleStatus);
            }
        }
        
        // 5. 删除该主表ID下的所有拆单明细
        int deletedCount = wholesaleNoticeSplitEntryMapper.deleteByMainId(bill.getId());
        log.info("[batchUpdateSplitEntries][单据编号: {}, 已删除: {} 条拆单明细记录]", 
                billNo, deletedCount);
        
        // 6. 批量插入新的拆单明细
        boolean insertSuccess = true;
        if (CollectionUtils.isNotEmpty(entries)) {
            try {
                // 使用批量插入优化性能
                wholesaleNoticeSplitEntryMapper.insertBatch(entries);
                log.info("[batchUpdateSplitEntries][单据编号: {}, 已插入: {} 条拆单明细记录]", 
                        billNo, entries.size());
            } catch (Exception e) {
                log.warn("[batchUpdateSplitEntries][单据编号: {} 批量插入拆单明细失败: {}]", 
                        billNo, e.getMessage(), e);
                insertSuccess = false;
            }
        }
        
        // 记录处理完成及耗时
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        log.info("[batchUpdateSplitEntries][单据编号: {} 处理完成, 耗时: {}ms, 删除记录数: {}, 插入记录数: {}]", 
                billNo, executionTime, deletedCount, entries != null ? entries.size() : 0);
        
        return updateMainSuccess && insertSuccess;
    }
} 
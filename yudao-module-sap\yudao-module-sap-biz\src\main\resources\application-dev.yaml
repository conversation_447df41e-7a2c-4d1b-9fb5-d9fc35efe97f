spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 50 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        socket-timeout: 300000
        connect-timeout: 300000
        connection-error-retry-attempts: 3
      primary: master
      datasource:
        master:
          #name: ruoyi-vue-pro
          url: *******************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: user
          password: user@2021
        bidb: 
          url: *************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: bi_test_owner
          password: _$SlREoWJ^lj+8ko4PP*E-mfG^Ez4L
#        ddy_system:
#          url: ******************************************************************************************************************************************************************************************
#          driver-class-name: com.mysql.jdbc.Driver
#          username: ddy_owner
#          password: 51PU@v440%sc74a+Q=ny
#        jecloud_erp: # 模拟从库，可根据自己需要修改
#          url: ************************************************************************************************************************************************
#          driver-class-name: com.mysql.jdbc.Driver
#          username: root
#          password: "%7Bl4@&Rz&d7CpwxBHHPeJQDf4ePoY"
        cq_scm: # 模拟从库，可根据自己需要修改
          url: ***********************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: ds_write
          password: FWeGPv#233uMk9Y
        cq_sys: # 模拟从库，可根据自己需要修改
          url: ***********************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: ds_write
          password: FWeGPv#233uMk9Y
#        bidbtest:
#          url: ****************************************************************************************************************************************
#          driver-class-name: com.mysql.jdbc.Driver
#          username: biuser_test
#          password: g5O$WS#-3jBSCu*9+&b0

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: ************ # 地址
    port: 6379 # 端口
    database: 1 # 数据库索引

--- #################### MQ 消息队列相关配置 ####################
spring:
  cloud:
    stream:
      rocketmq:
        # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
        binder:
          name-server: ************:9876 # RocketMQ Namesrv 地址

--- #################### 定时任务相关配置 ####################
xxl:
  job:
    admin:
      addresses: http://xxljob-test.i-mybest.com/xxl-job-admin # 调度中心部署跟地址
    accessToken: Jn80UsekF870EYtJlI4h

--- #################### 服务保障相关配置 ####################
# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################
# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring Boot Admin Server 的上下文路径

--- #################### 芋道相关配置 ####################
# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  xss:
    enable: false
    exclude-urls:
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  demo: false # 关闭演示模式
  tenant: # 多租户相关配置项
    ignore-tables:
      - sys_tenant
      - sys_user
  sap:
    csp:
      base-url: http://my.cspuat.by-health.com/orderapply # CSP API开发环境基础URL

mybatis-plus:
  configuration:
    # 使用自定义日志实现
    log-impl: cn.iocoder.yudao.module.sap.framework.config.MybatisPlusLogImpl

# SAP 系统配置
sap:
  jco:
    client: "800" # SAP 客户端
    user: "RFC_USER" # SAP 用户名
    passwd: "123456" # SAP 密码
    ashost: "sap.dev.company.com" # SAP 应用服务器
    sysnr: "00" # SAP 系统编号
    lang: "ZH" # SAP 登录语言

e3-config:
  e3:
    - company: "maiyou"
      ipPort: "http://*************/e3/webopm/web/?app_act=api/ec&app_mode=func"
      key: "BSAKr1ah3LamONBa3Zoa"
      version: "3.0"
      secret: "BSAKSdrOGGwALYnYcAfv"
      apiUrl: "http://*************/e3/e3_api/tc/index.php"
      zdyUrl: "*************/e3/webopm/web/?app_mode=func&app_act=a_tc/api_tc_zdy/ec"
    - company: "baiyue"
      ipPort: "http://*************/e3/webopm/web/?app_act=api/ec&app_mode=func"
      key: "717vkml03fam8mf1"
      version: "3.0"
      secret: "rq4bja5038s83mszog7pc2x1hnddev6v"
      apiUrl: "http://*************/e3/e3_api/tc/index.php"
      zdyUrl: "*************/e3/webopm/web/?app_mode=func&app_act=a_tc/api_tc_zdy/ec"

# 苍穹
cangqiong:
  api:
    base-url: http://*************:8088/ierp  # 苍穹API的基础URL
    app-key: ec_token              # 苍穹API的应用Key
    app-secret: ScalpelCoding#2025          # 苍穹API的应用Secret
    user: 18902263458                 # 苍穹API的用户名

# 特定组织配置
org:
  specific-orgs:
    - 广东佰悦网络科技有限公司
    - 二十八辰（广东）网络科技有限公司

--- #################### 钉钉通知任务责任人配置 ####################

# 钉钉机器人和任务责任人配置
ding:
  # 是否启用钉钉通知
  enabled: true
  # 钉钉机器人配置列表
  robots:
    - name: default
      accessToken: f4a42277e181e023a315c77d1a76a46ccd41b3784ef64b6cbae9c196936f5308
      secret: SECefab5170a39667b3e30d3a59345a8c1b201b86fae24012436b23058b4ceb6b2d
    - name: exception-notify
      accessToken: 059ee89cb9c1395032a03c2f09c0589893faa1507dcce061852b30a0013573f1
      secret: SECea5f805b2c1e78ddd95c928c5ec4a1ad023535c73574b6f7024e288e03570593
    # 可以配置多个机器人
    #- name: error-bot
    #  accessToken: xxx
    #  secret: xxx  
  # 任务责任人配置
  task-responsibles:
    # 异常采购入库单检查任务责任人
    tasks:
      abnormal-purinbill-check:
        mobiles:
          - "***********"
        display-names:
          - "IT"  
      # 异常销售出库单检查任务责任人
      abnormal-saloutbill-check:
        userIds:
          - "ng470tq"
        mobiles:
          - "***********"
        display-names:
          - "IT"
      # 物料库存信息检查任务责任人
      material-inventory-info:
        mobiles:
          - "***********"
        display-names:
          - "IT"
      # 异常发货明细检查任务责任人
      abnormal-delivery-details:
        mobiles:
          - "***********"
        display-names:
          - "IT"  



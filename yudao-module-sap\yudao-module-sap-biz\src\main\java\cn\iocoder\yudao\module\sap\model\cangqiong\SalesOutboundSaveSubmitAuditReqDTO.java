package cn.iocoder.yudao.module.sap.model.cangqiong;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售出库单保存-提交-审核请求DTO
 * 
 * 用于调用苍穹财务系统的销售出库单批量保存接口
 * 接口URL: /kapi/v2/yd/im/im_saloutbill/ec_batchSave
 */
@Data
public class SalesOutboundSaveSubmitAuditReqDTO {
    /**
     * 业务日期
     * 必填，格式：yyyy-MM-dd
     */
    private String biztime;
    
    /**
     * 是否初始单据
     * 非必填，默认false
     */
    private Boolean isinitbill;
    
    /**
     * 销售组织编号
     * 必填，销售组织的编码
     */
    @SerializedName("bizorg_number")
    private String bizorgNumber;
    
    /**
     * 客户编号
     * 必填，客户的编码
     */
    @SerializedName("customer_number")
    private String customerNumber;
    
    /**
     * 汇率
     * 必填
     */
    private BigDecimal exchangerate;
    
    /**
     * 同步来源系统
     * 非必填，1:苍穹财务系统-E3, 2:苍穹财务系统-旺店通, 3:苍穹财务系统-旺店通, 4:PCP, 5:万里牛
     */
    @SerializedName("yd_tbly")
    private String ydTbly;
    
    /**
     * 单据生成类型
     * 非必填
     */
    private String billcretype;
    
    /**
     * 业务类型编号
     * 必填，业务类型的编码
     */
    @SerializedName("biztype_number")
    private String biztypeNumber;
    
    /**
     * 销售部门编号
     * 必填，销售部门的编码
     */
    @SerializedName("bizdept_number")
    private String bizdeptNumber;
    
    /**
     * 单据类型编号
     * 必填，单据类型的编码
     */
    @SerializedName("billtype_number")
    private String billtypeNumber;
    
    /**
     * 记账日期
     * 非必填，格式：yyyy-MM-dd
     */
    private String bookdate;
    
    /**
     * 汇率日期
     * 必填，格式：yyyy-MM-dd
     */
    private String exratedate;
    
    /**
     * 汇率表编号
     * 必填，汇率表的编码
     */
    @SerializedName("exratetable_number")
    private String exratetableNumber;
    
    /**
     * 币别编号
     * 必填，本位币的货币代码
     */
    @SerializedName("currency_number")
    private String currencyNumber;
    
    /**
     * 明细列表
     * 必填，物料明细数组
     */
    private List<BillEntry> billentry;
    
    /**
     * 付款方式
     * 必填，如：CREDIT
     */
    private String paymode;
    
    /**
     * 组织编号
     * 必填，库存组织的编码
     */
    @SerializedName("org_number")
    private String orgNumber;
    
    /**
     * 对账客户编号
     * 必填，对账客户的编码
     */
    @SerializedName("yd_dzdkh_number")
    private String ydDzdkhNumber;
    
    /**
     * 库存事务编号
     * 必填，库存事务的编码
     */
    @SerializedName("invscheme_number")
    private String invschemeNumber;
    
    /**
     * 备注
     * 非必填
     */
    private String comment;
    
    /**
     * ID
     * 非必填
     */
    private Long id;
    
    /**
     * 结算币别编号
     * 必填，结算币别的货币代码
     */
    @SerializedName("settlecurrency_number")
    private String settlecurrencyNumber;
    
    /**
     * 是否含税
     * 必填
     */
    private Boolean istax;
    
    /**
     * 单据编号
     * 必填
     */
    private String billno;
    
    /**
     * SAP单据类型
     * 非必填，ZORC:2C订单, ZREC:2C退货订单
     */
    @SerializedName("yd_sapbilltype")
    private String ydSapbilltype;
    
    /**
     * SAP销售订单号
     * 非必填
     */
    @SerializedName("yd_sapsaleorderno")
    private String ydSapsaleorderno;

    /**
     * 同步来源系统
     * 非必填，来源系统 [1:E3, 2:旺店通, 3:吉客云, 4:万里牛, 5:新E3]
     */
    @SerializedName("yd_sourcesys")
    private String ydSourcesys;

    /**
     * 是否分按品牌分单
     * 非必填，默认false
     */
    @SerializedName("yd_issplit")
    private Boolean ydIssplit;

    /**
     * 是否退货
     * 非必填，默认false
     */
    @SerializedName("yd_isreturn")
    private Boolean ydIsreturn;

    /**
     * 来源单据类型（来源单据类型 [1:批发退货单, 2:批发通知单, 3:PCP销售出库单, 4:PCP库存调拨单, 5:其他出库单, 6:发货明细]）
     * 非必填，默认0
     */
    @SerializedName("yd_sourcebilltype")
    private String ydSourcebilltype;

    /**
     * 明细条目
     */
    @Data
    public static class BillEntry {
        /**
         * 出库保管者类型
         * 必填，bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
         */
        private String outkeepertype;
        
        /**
         * 出库保管者编号
         * 必填，出库保管者的编码
         */
        @SerializedName("outkeeper_number")
        private String outkeeperNumber;
        
        /**
         * 基本单位编号
         * 必填，基本单位的编码
         */
        @SerializedName("baseunit_number")
        private String baseunitNumber;
        
        /**
         * 出库库存类型编号
         * 必填，出库库存类型的编码
         */
        @SerializedName("outinvtype_number")
        private String outinvtypeNumber;
        
        /**
         * 行类型编号
         * 必填，行类型的编码
         */
        @SerializedName("linetype_number")
        private String linetypeNumber;
        
        /**
         * 基本数量
         * 必填
         */
        private BigDecimal baseqty;
        
        /**
         * 折扣类型
         * 必填，A:折扣率(%), NULL:无, B:单位折扣额
         */
        private String discounttype;
        
        /**
         * 仓库编号（销售组织仓库）
         * 必填，仓库的编码
         */
        @SerializedName("warehouse_number")
        private String warehouseNumber;
        
        /**
         * 结算组织编号
         * 必填，结算组织的编码
         */
        @SerializedName("entrysettleorg_number")
        private String entrysettleorgNumber;
        
        /**
         * 出库库存状态编号
         * 必填，出库库存状态的编码
         */
        @SerializedName("outinvstatus_number")
        private String outinvstatusNumber;
        
        /**
         * 出库货主类型
         * 必填，bos_org:核算组织, bd_supplier:供应商, bd_customer:客户
         */
        private String outownertype;
        
        /**
         * 是否赠品
         * 非必填，默认false
         */
        private Boolean ispresent;
        
        /**
         * 出库货主编号
         * 必填，出库货主的编码
         */
        @SerializedName("outowner_number")
        private String outownerNumber;
        
        /**
         * 单位编号
         * 必填，计量单位的编码
         */
        @SerializedName("unit_number")
        private String unitNumber;
        
        /**
         * 物料编号
         * 必填，物料编码
         */
        @SerializedName("material_number")
        private String materialNumber;
        
        /**
         * 数量
         * 必填
         */
        private BigDecimal qty;
        
        /**
         * ID
         * 非必填
         */
        private Long id;
        
        /**
         * 含税单价
         * 非必填
         */
        private BigDecimal priceandtax;

        /**
         * 价税合计(本位币)
         * 非必填
         */
        private BigDecimal curamountandtax;

        /**
         * 价税合计
         * 非必填
         */
        private BigDecimal amountandtax;
        
        /**
         * 金额
         * 非必填
         */
        private BigDecimal amount;
        
        /**
         * 本位币金额
         * 非必填
         */
        private BigDecimal curamount;
        
        /**
         * 单价
         * 非必填
         */
        private BigDecimal price;
        
        /**
         * 本位币税额
         * 非必填
         */
        private BigDecimal curtaxamount;
        
        /**
         * 税额
         * 非必填
         */
        private BigDecimal taxamount;
        
        /**
         * 税率
         * 非必填，百分比
         */
        private BigDecimal taxrate;
        
        /**
         * SAP行号
         * 非必填
         */
        @SerializedName("yd_saprowno")
        private Integer ydSaprowno;
        
        /**
         * 行类型
         * 非必填，TAN:常规销售类别, TANN:免费行, REN:退货行, RENN:免费行退货
         */
        @SerializedName("yd_rowtype")
        private String ydRowtype;
        
        /**
         * 是否BOM
         * 非必填，是否组套
         */
        @SerializedName("yd_isbom")
        private Boolean ydIsbom;
        
        /**
         * 货架价
         * 非必填，商品上架单价
         */
        @SerializedName("yd_shelves_price")
        private BigDecimal ydShelvesPrice;
        
        /**
         * 折扣
         * 非必填，商品总折扣
         */
        @SerializedName("yd_discount")
        private BigDecimal ydDiscount;
        
        /**
         * 运费
         * 非必填，商品总运费（均摊运费）
         */
        @SerializedName("yd_decimalfield1")
        private BigDecimal ydShareFreight;

        /**
         * 均摊金额
         * 非必填，商品均摊金额
         */
        @SerializedName("yd_shareamount")
        private BigDecimal ydShareAmount;
        
        /**
         * BOM编号
         * 非必填，组套编码
         */
        @SerializedName("yd_bomnum")
        private String ydBomnum;
        
        /**
         * 库存组织仓库编号
         * 必填，库存组织仓库编码
         */
        @SerializedName("yd_invorgstock_number")
        private String ydInvorgstockNumber;        

        /**
         * 物料明细.库存组织.编码
         */
        @SerializedName("yd_invorg_number")
        private String ydInvorgNumber;

        /**
         * 物料类型编码
         * 非必填，物料组的编码
         */
        @SerializedName("yd_matgroup_number")
        private String ydMatgroupNumber;

        /**
         * 产品类型编码
         * 非必填，产品类型的编码
         */
        @SerializedName("yd_producttype")
        private String ydProducttype;
        
    }

    /**
     * 发货明细实体列表
     * 非必填
     */
    @SerializedName("yd_entryentity")
    private List<YdEntryEntity> ydEntryentity;

    /**
     * 发货明细实体
     */
    @Data
    public static class YdEntryEntity {
        /**
         * ID
         * 非必填
         */
        private Long id;
        
        /**
         * 序号
         * 非必填
         */
        private Integer seq;
        
        /**
         * 发货明细单号
         * 非必填
         */
        @SerializedName("yd_textfield_fhmxdh")
        private String ydTextfieldFhmxdh;

        /**
         * 发货明细单号.行号
         * 非必填
         */ 
        @SerializedName("yd_rowno")
        private String ydRowno;

        /**
         * 发货明细单号.行ID
         * 非必填
         */
        @SerializedName("yd_rowid")
        private String ydRowid;
        
    }
} 
package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.sap.utils.EnhancedCollectionUtils;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailRespVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailQueryVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqAbnormalDeliveryDetailVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailSettleInfoVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailEntrySettleInfoVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailWithSourceEntriesDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.OrderDealInfoVO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqDeliveryDetailSimpleDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.BatchDeliveryDetailEntrySettleUpdateDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailEntrySettleUpdateDTO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDateStatDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqDeliveryDetailDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqDeliveryDetailEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqSourceDeliveryDetailEntryDO;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqDeliveryDetailEntryMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqDeliveryDetailMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqSourceDeliveryDetailEntryMapper;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDeliveryDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 苍穹发货明细 Service 实现类
 */
@Service
@Validated
@Slf4j
@DS("cq_scm")
public class CqDeliveryDetailServiceImpl implements CqDeliveryDetailService {

    /**
     * 批量查询大小阈值，超过此值将进行性能警告
     */
    private static final int BATCH_QUERY_SIZE_THRESHOLD = 1000;

    /**
     * 内存使用警告阈值，超过此值将记录内存使用警告
     */
    private static final int MEMORY_WARNING_THRESHOLD = 5000;

    @Resource
    private CqDeliveryDetailMapper deliveryDetailMapper;

    @Resource
    private CqDeliveryDetailEntryMapper deliveryDetailEntryMapper;
    
    @Resource
    private CqSourceDeliveryDetailEntryMapper sourceDeliveryDetailEntryMapper;

    @Override
    public CqDeliveryDetailRespVO getDeliveryDetail(Long id) {
        // 1. 查询主表
        CqDeliveryDetailDO deliveryDetailDO = deliveryDetailMapper.selectById(id);
        if (deliveryDetailDO == null) {
            return null;
        }

        // 2. 查询分录
        List<CqDeliveryDetailEntryDO> entries = deliveryDetailEntryMapper.selectListByMainId(id);

        // 3. 组装返回结果
        return convertToRespVO(deliveryDetailDO, entries);
    }

    // @Override
    // public List<CqDeliveryDetailRespVO> getDeliveryDetailsByOrderNo(String orderNo) {
    //     // 调用新方法，传入null表示不过滤结算状态
    //     return getDeliveryDetailsByOrderNo(orderNo, null);
    // }

    /**
     * 根据单据编号获取发货明细列表
     * 
     * @param billNos 单据编号列表，用于查询对应的发货明细
     * @param settleStatus 结算状态，必需参数，用于筛选特定结算状态的记录
     * @param shopNos 店铺编号列表，可选参数，用于筛选特定店铺的记录
     * @param startDate 开始日期，可选参数，用于筛选发货日期范围。如果为null，默认为31天前
     * @param endDate 结束日期，可选参数，用于筛选发货日期范围。如果为null，默认为当前日期
     * @return 发货明细响应对象列表
     * @throws IllegalArgumentException 当结算状态为空时
     */
    @Override
    public List<CqDeliveryDetailRespVO> getDeliveryDetailsByBillNo(List<String> billNos, String settleStatus, List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        // 参数校验
        if (StrUtil.isBlank(settleStatus)) {
            log.error("[getDeliveryDetailsByBillNo][结算状态不能为空，单据数量: {}, 店铺数量: {}, 日期范围: {} 到 {}]", 
                    billNos != null ? billNos.size() : 0, 
                    shopNos != null ? shopNos.size() : 0, startDate, endDate);
            throw new IllegalArgumentException("结算状态不能为空");
        }
        
        // 设置默认时间范围
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(31);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        log.info("[getDeliveryDetailsByBillNo][开始分层查询，单据数量: {}, 结算状态: {}, 店铺数量: {}, 日期范围: {} 到 {}]", 
                billNos != null ? billNos.size() : 0, settleStatus, 
                shopNos != null ? shopNos.size() : 0, startDate, endDate);
        
        // 直接执行原始查询方式
        return executeOriginalQuery(billNos, settleStatus, shopNos, startDate, endDate);
    }
    
    /**
     * 根据订单信息获取发货明细列表
     * 
     * @param orderNo 订单编号，用于查询对应的发货明细
     * @param shopNo 店铺编号，用于查询对应的发货明细
     * @param platform 平台，用于查询对应的发货明细
     * @return 发货明细响应对象列表
     */
    @Override
    public List<CqDeliveryDetailRespVO> getDeliveryDetailsByOrderInfo(String orderNo, String shopNo, String platform) {
        // 构建查询条件
        LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(CqDeliveryDetailDO::getOrderNo, orderNo);
        queryWrapper.eq(CqDeliveryDetailDO::getShopNo, shopNo);
        queryWrapper.eq(CqDeliveryDetailDO::getPlatform, platform);
        
        log.info("[getDeliveryDetailsByOrderInfo][查询订单信息，订单编号: {}, 店铺编号: {}, 平台: {}]", orderNo, shopNo, platform);
        
        // 执行查询
        List<CqDeliveryDetailDO> deliveryDetails = deliveryDetailMapper.selectList(queryWrapper);
        
        log.info("[getDeliveryDetailsByOrderInfo][查询结果，记录数: {}]", deliveryDetails.size());
        
        return convertToRespVOList(deliveryDetails);
    }
    
    /**
     * 更新单据的结算状态
     *
     * @param billNo 单据编号
     * @param settleStatus 结算状态
     * @param failReason 失败原因
     * @return 是否更新成功
     */
    @Override
    public boolean updateSettleStatusByBillNo(String billNo, String settleStatus, String failReason) {
        if (StrUtil.isBlank(billNo) || StrUtil.isBlank(settleStatus)) {
            log.error("[updateSettleStatusByBillNo][参数不能为空，单据编号: {}, 结算状态: {}]", billNo, settleStatus);
            return false;
        }
        
        try {
            // 如果失败原因超过1500字符，进行截取
            if (StrUtil.isNotBlank(failReason) && failReason.length() > 1500) {
                failReason = failReason.substring(0, 1500);
                log.warn("[updateSettleStatusByBillNo][失败原因超过1500字符，已截取，单据编号: {}]", billNo);
            }
            
            // 构建更新条件
            LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CqDeliveryDetailDO::getBillNo, billNo)
                         .set(CqDeliveryDetailDO::getSettleStatus, settleStatus)
                         .set(CqDeliveryDetailDO::getModifyTime, LocalDateTime.now())
                         .set(CqDeliveryDetailDO::getFailReason, failReason);
            
            // 执行更新
            int rows = deliveryDetailMapper.update(null, updateWrapper);
            
            log.info("[updateSettleStatusByBillNo][更新单据结算状态成功，单据编号: {}, 结算状态: {}, 影响行数: {}]", 
                    billNo, settleStatus, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updateSettleStatusByBillNo][更新单据结算状态异常，单据编号: {}, 结算状态: {}]", 
                    billNo, settleStatus, e);
            return false;
        }
    }

    @Override
    public List<CqDeliveryDetailRespVO> getDeliveryDetailsByDealCode(String dealCode) {
        // 根据交易号查询主表
        List<CqDeliveryDetailDO> deliveryDetails = deliveryDetailMapper.selectList(
                CqDeliveryDetailDO::getDealCode, dealCode);
        
        return convertToRespVOList(deliveryDetails);
    }

    @Override
    public List<CqDeliveryDetailRespVO> getDeliveryDetailsByExpressNo(String expressNo) {
        // 根据快递单号查询主表
        List<CqDeliveryDetailDO> deliveryDetails = deliveryDetailMapper.selectList(
                CqDeliveryDetailDO::getExpressNo, expressNo);
        
        return convertToRespVOList(deliveryDetails);
    }

    @Override
    public boolean existsDeliveryDetail(String orderNo, String platform, Boolean isReturn, String shopNo) {
        // 使用订单编号、平台、是否退货和店铺编号作为唯一标识检查是否存在
        return deliveryDetailMapper.selectCount(new LambdaQueryWrapper<CqDeliveryDetailDO>()
                .eq(CqDeliveryDetailDO::getOrderNo, orderNo)
                .in(CqDeliveryDetailDO::getPlatform, Arrays.asList("1", "5"))
                .eq(CqDeliveryDetailDO::getIsReturn, isReturn)
                .eq(CqDeliveryDetailDO::getShopNo, shopNo)) > 0;
    }

    /**
     * 根据订单编号、交易号和店铺编号判断发货明细是否已存在
     *
     * @param orderNo 订单编号
     * @param dealCode 交易号
     * @param shopNo 店铺编号
     * @return 是否存在
     */
    @Override
    public boolean existsDeliveryDetailByOrderAndDealAndShop(String orderNo, String dealCode, String shopNo) {
        // 使用订单编号、交易号和店铺编号作为唯一标识检查是否存在
        return deliveryDetailMapper.selectCount(new LambdaQueryWrapper<CqDeliveryDetailDO>()
                .eq(CqDeliveryDetailDO::getOrderNo, orderNo)
                // 只有当交易号不为空时才添加交易号条件，避免空值查询导致的问题
                .eq(StringUtils.isNotBlank(dealCode), CqDeliveryDetailDO::getDealCode, dealCode)
                .eq(CqDeliveryDetailDO::getShopNo, shopNo)) > 0;
    }

    @Override
    public List<CqDeliveryDetailDO> getTopNByOrderByFkYdDatefieldFhrqDesc(int limit) {
        return deliveryDetailMapper.selectList(
            new LambdaQueryWrapperX<CqDeliveryDetailDO>()
                .orderByDesc(CqDeliveryDetailDO::getDeliveryDate)
                .last("LIMIT " + limit)
        );
    }

    /**
     * 获取最近指定天数内的发货明细数据
     *
     * @param days 天数
     * @return 发货明细数据列表
     */
    @Override
    public List<CqDeliveryDetailQueryVO> getRecentDeliveryDetails(int days) {
        return deliveryDetailMapper.queryRecentDeliveryDetails(days);
    }

    /**
     * 查询本月和上月的异常发货明细数据
     * 
     * @return 异常发货明细数据列表
     */
    @Override
    public List<CqAbnormalDeliveryDetailVO> queryAbnormalDeliveryDetails() {
        log.info("[queryAbnormalDeliveryDetails][查询本月和上月的异常发货明细数据]");
        return deliveryDetailMapper.queryAbnormalDeliveryDetails();
    }

    /**
     * 根据单据编号查询结算状态为待结算或结算异常的发货明细
     * 如果billNo为空，则查询所有结算状态为待结算或结算异常的发货明细
     *
     * @param billNos 单据编号列表，可为null
     * @param settleStatus 结算状态，不能为空
     * @return 发货明细列表
     * @throws IllegalArgumentException 当结算状态为空时
     */
    @Override
    public List<CqDeliveryDetailDO> getDeliveryDetailsBySettleStatus(List<String> billNos, String settleStatus) {
        // 参数校验
        if (StrUtil.isBlank(settleStatus)) {
            log.error("[getDeliveryDetailsBySettleStatus][结算状态不能为空，单据编号列表: {}, 结算状态: {}]", billNos, settleStatus);
            throw new IllegalArgumentException("结算状态不能为空");
        }
        
        // 构建查询条件
        LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        
        // 添加billNo条件（如果不为空）
        if (EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            queryWrapper.in(CqDeliveryDetailDO::getBillNo, billNos);
        }
        
        // 添加结算状态条件
        queryWrapper.in(CqDeliveryDetailDO::getSettleStatus, settleStatus);
        
        // 添加异常状态不等于"1"的过滤条件
        queryWrapper.ne(CqDeliveryDetailDO::getIsError, "1");
        
        // 执行查询并返回结果
        return deliveryDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据单据编号更新客户不存在标记
     *
     * @param billNo 单据编号
     * @param khbczValue 客户不存在标记值
     * @return 是否更新成功
     */
    @Override
    public boolean updateKhbczByBillNo(String billNo, String khbczValue) {
        // 参数校验
        if (StrUtil.isBlank(billNo)) {
            log.error("[updateKhbczByBillNo][单据编号为空]");
            return false;
        }
        
        // 创建更新条件包装器
        LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CqDeliveryDetailDO::getBillNo, billNo)
                     .set(CqDeliveryDetailDO::getCustomerNotExist, khbczValue);
        
        // 执行更新
        try {
            int rows = deliveryDetailMapper.update(null, updateWrapper);
            log.info("[updateKhbczByBillNo][单据编号: {}, 客户不存在值: {}, 影响行数: {}]", billNo, khbczValue, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updateKhbczByBillNo][更新失败，单据编号: {}, 客户不存在值: {}]", billNo, khbczValue, e);
            return false;
        }
    }

    /**
     * 根据单据编号更新批次出库标记
     *
     * @param billNo 单据编号
     * @param pcckValue 批次出库标记值
     * @return 是否更新成功
     */
    @Override
    public boolean updatePcckByBillNo(String billNo, String pcckValue) {
        // 参数校验
        if (StrUtil.isBlank(billNo)) {
            log.error("[updatePcckByBillNo][单据编号为空]");
            return false;
        }
        
        // 创建更新条件包装器
        LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CqDeliveryDetailDO::getBillNo, billNo)
                     .set(CqDeliveryDetailDO::getExcludeWarehouse, pcckValue);
        
        // 执行更新
        try {
            int rows = deliveryDetailMapper.update(null, updateWrapper);
            log.info("[updatePcckByBillNo][单据编号: {}, 批次出库值: {}, 影响行数: {}]", billNo, pcckValue, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updatePcckByBillNo][更新失败，单据编号: {}, 批次出库值: {}]", billNo, pcckValue, e);
            return false;
        }
    }

    /**
     * 根据单据编号更新物料不存在标记
     *
     * @param billNo 单据编号
     * @param wlbczValue 物料不存在标记值
     * @return 是否更新成功
     */
    @Override
    public boolean updateWlbczByBillNo(String billNo, String wlbczValue) {
        // 参数校验
        if (StrUtil.isBlank(billNo)) {
            log.error("[updateWlbczByBillNo][单据编号为空]");
            return false;
        }
        
        // 创建更新条件包装器
        LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CqDeliveryDetailDO::getBillNo, billNo)
                     .set(CqDeliveryDetailDO::getMaterialNotExist, wlbczValue);
        
        // 执行更新
        try {
            int rows = deliveryDetailMapper.update(null, updateWrapper);
            log.info("[updateWlbczByBillNo][单据编号: {}, 物料不存在值: {}, 影响行数: {}]", billNo, wlbczValue, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updateWlbczByBillNo][更新失败，单据编号: {}, 物料不存在值: {}]", billNo, wlbczValue, e);
            return false;
        }
    }
    
    /**
     * 根据单据编号更新订单中剔除物料标记
     *
     * @param billNo 单据编号
     * @param isExcludeAllMaterial 是否整单剔除物料
     * @return 是否更新成功
     */
    @Override
    public boolean updateExcludeMaterialInOrderByBillNo(String billNo, String isExcludeAllMaterial) {
        // 参数校验
        if (StrUtil.isBlank(billNo)) {
            log.error("[updateExcludeMaterialInOrderByBillNo][单据编号为空]");
            return false;
        }
        
        // 创建更新条件包装器
        LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CqDeliveryDetailDO::getBillNo, billNo)
                     .set(CqDeliveryDetailDO::getExcludeMaterialInOrder, isExcludeAllMaterial);
        
        // 执行更新
        try {
            int rows = deliveryDetailMapper.update(null, updateWrapper);
            log.info("[updateExcludeMaterialInOrderByBillNo][单据编号: {}, 是否整单剔除物料: {}, 影响行数: {}]", billNo, isExcludeAllMaterial, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updateExcludeMaterialInOrderByBillNo][更新失败，单据编号: {}, 是否整单剔除物料: {}]", billNo, isExcludeAllMaterial, e);
            return false;
        }
    }

    /**
     * 更新分录物料相关标记
     *
     * @param mainId 主表ID
     * @param entryId 分录ID
     * @param isMainMatNumCombined 是否组装品标记
     * @param isExcludedMaterial 剔除物料标记
     * @return 是否更新成功
     */
    @Override
    public boolean updateDeliveryDetailEntryMaterialFlags(Long mainId, Long entryId, 
                                                         boolean isMainMatNumCombined, boolean isExcludedMaterial) {
        // 参数校验
        if (mainId == null || entryId == null) {
            log.error("[updateDeliveryDetailEntryMaterialFlags][主表ID或分录ID为空，mainId: {}, entryId: {}]", mainId, entryId);
            return false;
        }
        
        // 创建更新条件包装器
        LambdaUpdateWrapper<CqDeliveryDetailEntryDO> updateWrapper = new LambdaUpdateWrapper<>();
        // 设置查询条件：根据主表ID和分录ID
        updateWrapper.eq(CqDeliveryDetailEntryDO::getMainId, mainId)
                     .eq(CqDeliveryDetailEntryDO::getEntryId, entryId)
                     // 设置是否组装品标记：1表示是，0表示否
                     .set(CqDeliveryDetailEntryDO::getIsAssembly, isMainMatNumCombined ? "1" : "0")
                     // 设置剔除物料标记：1表示剔除，0表示不剔除
                     .set(CqDeliveryDetailEntryDO::getExcludeMaterial, isExcludedMaterial ? "1" : "0");
        
        // 执行更新
        try {
            int rows = deliveryDetailEntryMapper.update(null, updateWrapper);
            log.info("[updateDeliveryDetailEntryMaterialFlags][更新分录物料标记成功，mainId: {}, entryId: {}, 是否组装品: {}, 剔除物料: {}, 影响行数: {}]", 
                    mainId, entryId, isMainMatNumCombined, isExcludedMaterial, rows);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updateDeliveryDetailEntryMaterialFlags][更新分录物料标记失败，mainId: {}, entryId: {}, 物料不存在: {}, 是否组装品: {}, 剔除物料: {}]", 
                    mainId, entryId, isMainMatNumCombined, isExcludedMaterial, e);
            return false;
        }
    }

    /**
     * 将DO对象转换为RespVO对象
     */
    private CqDeliveryDetailRespVO convertToRespVO(CqDeliveryDetailDO deliveryDetailDO, 
                                                  List<CqDeliveryDetailEntryDO> entries) {
        // 查询源分录数据
        List<CqSourceDeliveryDetailEntryDO> sourceEntries = sourceDeliveryDetailEntryMapper.selectListByMainId(deliveryDetailDO.getId());
        return convertToRespVO(deliveryDetailDO, entries, sourceEntries);
    }

    /**
     * 将DO对象转换为RespVO对象（批量查询优化版本）
     */
    private CqDeliveryDetailRespVO convertToRespVO(CqDeliveryDetailDO deliveryDetailDO, 
                                                  List<CqDeliveryDetailEntryDO> entries,
                                                  List<CqSourceDeliveryDetailEntryDO> sourceEntries) {
        // 创建新的响应VO对象
        CqDeliveryDetailRespVO respVO = new CqDeliveryDetailRespVO();
        
        // 调用各个分层映射方法
        mapBaseFields(deliveryDetailDO, respVO);       // 基础信息映射
        mapAuditFields(deliveryDetailDO, respVO);      // 审计信息映射
        mapBusinessFields(deliveryDetailDO, respVO);   // 业务属性映射
        mapStatusFields(deliveryDetailDO, respVO);     // 状态标记映射
        mapFinancialFields(deliveryDetailDO, respVO);  // 金额信息映射
        mapLogisticsFields(deliveryDetailDO, respVO);  // 物流信息映射
        mapAddressFields(deliveryDetailDO, respVO);    // 地址信息映射
        mapTimeFields(deliveryDetailDO, respVO);       // 时间信息映射
        mapReferenceFields(deliveryDetailDO, respVO);  // 关联信息映射
        
        // 处理分录信息映射
        if (CollUtil.isNotEmpty(entries)) {
            respVO.setEntries(convertEntries(entries));
        }
        
        // 处理组装明细分录数据
        if (CollUtil.isNotEmpty(sourceEntries)) {
            respVO.setSourceEntries(convertSourceEntries(sourceEntries));
        }
        
        return respVO;
    }
    
    /**
     * 映射基础信息字段
     */
    private void mapBaseFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setId(source.getId());
        target.setFbillno(source.getBillNo());
        target.setBillNo(source.getBillNo());
        target.setFbillstatus(source.getBillStatus());
        target.setBillStatus(source.getBillStatus());
    }
    
    /**
     * 映射审计信息字段
     */
    private void mapAuditFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setCreatorId(source.getCreatorId());
        target.setModifierId(source.getModifierId());
        target.setFauditorid(source.getAuditorId());
        target.setAuditorId(source.getAuditorId());
        target.setFauditdate(source.getAuditDate());
        target.setAuditDate(source.getAuditDate());
        target.setModifyTime(source.getModifyTime());
        target.setCreateTime(source.getCreateTime());
        target.setUpdateTime(source.getModifyTime()); // 新增：updateTime映射到modifyTime
    }
    
    /**
     * 映射业务属性字段
     */
    private void mapBusinessFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setPlatform(source.getPlatform());
        target.setOrderNo(source.getOrderNo());
        target.setShopNo(source.getShopNo());
        target.setDownstreamNo(source.getDownstreamNo());
        target.setPlatformDeliveryTime(source.getPlatformDeliveryTime());
        target.setDownstreamType(source.getDownstreamType());
        target.setRemark(source.getRemark());
        target.setDealCode(source.getDealCode());
        target.setShopName(source.getShopName());
        target.setChannelCode(source.getChannelCode());
        target.setChannelName(source.getChannelName());
        target.setOrderType(source.getOrderType());
        target.setSourceType(source.getSourceType());
        target.setStoreId(source.getStoreId());
        target.setNotSaleShop(source.getNotSaleShop());
        target.setShopOrg(source.getShopOrg());
        target.setOrderStatus(source.getOrderStatus());
        target.setPayStatus(source.getPayStatus());
        target.setSettleStatus(source.getSettleStatus());
        target.setCustomerId(source.getCustomerId());
        target.setMerchantRemark(source.getMerchantRemark());
        target.setCqCustomerId(source.getCustomerId());
    }
    
    /**
     * 映射状态标记字段
     */
    private void mapStatusFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setIsInvoiced(source.getIsInvoiced());
        target.setIsManual(source.getIsManual());
        target.setCustomerNotExist(source.getCustomerNotExist());
//        target.setWarehouseNotExist(source.getWarehouseNotExist());
        target.setMaterialNotExist(source.getMaterialNotExist());
        target.setNotTransferToEAS(source.getNotTransferToEAS());
        target.setIsReturn(source.getIsReturn());
        target.setParticipateInCombine(source.getParticipateInCombine());
        target.setExcludeMaterialInOrder(source.getExcludeMaterialInOrder());
        target.setMaterialNotAudited(source.getMaterialNotAudited());
        target.setExcludeWarehouse(source.getExcludeWarehouse());
        target.setMaterialRefDuplicated(source.getMaterialRefDuplicated());
        target.setSyncToEASSuccess(source.getSyncToEASSuccess());
        target.setIsError(source.getIsError());
        target.setIsExchangeOrder(source.getIsExchangeOrder());
        target.setIsSplit(source.getIsSplit());
        target.setIsSplitNew(source.getIsSplitNew());
        target.setIsCombined(source.getIsCombined());
        target.setIsCombinedNew(source.getIsCombinedNew());
        target.setIsCopy(source.getIsCopy());
        target.setHasAssembly(source.getHasAssembly());
        target.setIsAssemblySplit(source.getIsAssemblySplit());
        target.setIsAmountAndTax(source.getIsAmountAndTax());
        target.setIsNotSale(source.getIsNotSale());
        target.setIsBrandSplitBill(source.getIsBrandSplitBill());
        
        // 新增：销售组织仓库不存在标记映射
        target.setSaleOrgStockNotExist(source.getSaleOrgStockNotExist());
        
        // 新增：库存组织仓库不存在标记映射
        target.setInvOrgStockNotExist(source.getInvOrgStockNotExist());
    }
    
    /**
     * 映射金额信息字段
     */
    private void mapFinancialFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setFreight(source.getFreight());
        target.setTotalAmount(source.getTotalAmount());
        target.setPayment(source.getPayment());
        target.setPlatformActivityPrice(source.getPlatformActivityPrice());
        target.setOtherDiscountFee(source.getOtherDiscountFee());
        target.setOrderAmount(source.getOrderAmount());
        target.setWeight(source.getWeight());
        target.setGoodOffer(source.getGoodOffer());
        target.setSellerOffer(source.getSellerOffer());
        target.setExRate(source.getExRate());
        target.setExcludeAmount(source.getExcludeAmount());
        target.setSettleCurrencyId(source.getSettleCurrencyId());
        target.setTax(source.getTax());
        target.setTotalAmountAmz(source.getTotalAmountAmz());
        target.setCurrency(source.getCurrency());
    }
    
    /**
     * 映射物流信息字段
     */
    private void mapLogisticsFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setWarehouse(source.getWarehouse());
        target.setWarehouseName(source.getWarehouseName());
        target.setExpressCode(source.getExpressCode());
        target.setExpressName(source.getExpressName());
        target.setExpressNo(source.getExpressNo());
        target.setShippingStatus(source.getShippingStatus());
        // target.setCqWarehouseId(source.getWarehouseId());
    }
    
    /**
     * 映射地址信息字段
     */
    private void mapAddressFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setProvince(source.getProvince());
        target.setCity(source.getCity());
        target.setDistrict(source.getDistrict());
        target.setAddress(source.getAddress());
    }
    
    /**
     * 映射时间信息字段
     */
    private void mapTimeFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setDeliveryDate(source.getDeliveryDate());
        target.setShippingTimeCk(source.getShippingTimeCk());
        target.setShippingTimeFh(source.getShippingTimeFh());
        target.setAddTime(source.getAddTime());
        target.setPayTime(source.getPayTime());
    }
    
    /**
     * 映射关联信息字段
     */
    private void mapReferenceFields(CqDeliveryDetailDO source, CqDeliveryDetailRespVO target) {
        target.setOriDealCode(source.getOriDealCode());
        target.setOriOrderSn(source.getOriOrderSn());
        target.setRelatingOrderSn(source.getRelatingOrderSn());
        target.setRelatedOrderNotExists(source.getRelatedOrderNotExists());
        target.setErrorState(source.getErrorState());
        target.setFailReason(source.getFailReason());
        target.setBudgetCompany(source.getBudgetCompany());
        target.setBudgetDepartment(source.getBudgetDepartment());
        target.setBudgetAccount(source.getBudgetAccount());
        target.setApplyDepartment(source.getApplyDepartment());
        target.setRequisitionUse(source.getRequisitionUse());
        target.setCostCenter(source.getCostCenter());
    }
    
    /**
     * 转换分录信息
     */
    private List<CqDeliveryDetailRespVO.CqDeliveryDetailEntryRespVO> convertEntries(
            List<CqDeliveryDetailEntryDO> entries) {
        
        if (CollUtil.isEmpty(entries)) {
            return new ArrayList<>();
        }
        
        return entries.stream().map(entryDO -> {
            CqDeliveryDetailRespVO.CqDeliveryDetailEntryRespVO entryResp = 
                new CqDeliveryDetailRespVO.CqDeliveryDetailEntryRespVO();
            
            // 映射分录基本信息
            entryResp.setEntryId(entryDO.getEntryId());
            entryResp.setMainId(entryDO.getMainId());
            entryResp.setSeq(entryDO.getSeq());
            
            // 映射商品信息
            entryResp.setMaterialNo(entryDO.getMaterialNo());
            entryResp.setGoodsSn(entryDO.getGoodsSn());
            entryResp.setGoodsId(entryDO.getGoodsId());
            entryResp.setGoodsName(entryDO.getGoodsName());
            entryResp.setBarcode(entryDO.getBarcode());
            entryResp.setSkuId(entryDO.getSkuId());
            
            // 映射数量和金额信息
            entryResp.setQuantity(entryDO.getQuantity());
            entryResp.setPrice(entryDO.getPrice());
            entryResp.setTotalAmount(entryDO.getTotalAmount());
            entryResp.setGoodsPrice(entryDO.getGoodsPrice());
            entryResp.setShopPrice(entryDO.getShopPrice());
            entryResp.setAmountAndTax(entryDO.getAmountAndTax());
            entryResp.setPaidPrice(entryDO.getPaidPrice());
            entryResp.setE3Qty(entryDO.getE3Qty());
            entryResp.setDetailTotalAmount(entryDO.getDetailTotalAmount());
            
            // 映射批次信息
            entryResp.setBatchNo(entryDO.getBatchNo());
            entryResp.setProductionDate(entryDO.getProductionDate());
            entryResp.setExpirationDate(entryDO.getExpirationDate());
            
            // 映射订单和交易信息
            entryResp.setOriginalOrderSn(entryDO.getOriginalOrderSn());
            entryResp.setOriginalDealCode(entryDO.getOriginalDealCode());
            entryResp.setSubDealCode(entryDO.getSubDealCode());
            entryResp.setNumIid(entryDO.getNumIid());
            entryResp.setRowId(entryDO.getRowId());
            
            // 映射物流和成本信息
            // entryResp.setShareShippingFee(entryDO.getShareShippingFee());
            entryResp.setNumberReturnSj(entryDO.getNumberReturnSj());
            entryResp.setShareAmount(entryDO.getShareAmount());
            entryResp.setAvLogisticsCost(entryDO.getAvLogisticsCost());
            
            // 映射平台特定信息
            entryResp.setTradeRebate(entryDO.getTradeRebate());
            entryResp.setLazFreight(entryDO.getLazFreight());
            entryResp.setTcSku(entryDO.getTcSku());
            
            // 映射达人/主播信息
            entryResp.setAnchorId(entryDO.getAnchorId());
            entryResp.setAnchorName(entryDO.getAnchorName());
            
            // 映射商品上架价和折扣信息
            entryResp.setShelvesPrice(entryDO.getShelvesPrice());
            entryResp.setTotalDiscountAmount(entryDO.getTotalDiscountAmount());
            
            // 映射物料处理标记
            entryResp.setMxMaterialNotExist(entryDO.getMxMaterialNotExist());
            entryResp.setMxMaterialRefDuplicated(entryDO.getMxMaterialRefDuplicated());
            entryResp.setIsAssembly(entryDO.getIsAssembly());
            entryResp.setExcludeMaterial(entryDO.getExcludeMaterial());
            entryResp.setMainMatNum(entryDO.getMainMatNum());
            entryResp.setSingleMaterialId(entryDO.getSingleMaterialId());
            
            return entryResp;
        }).collect(Collectors.toList());
    }

    /**
     * 转换组装明细分录信息
     */
    private List<CqDeliveryDetailRespVO.CqSourceDeliveryDetailEntryRespVO> convertSourceEntries(
            List<CqSourceDeliveryDetailEntryDO> sourceEntries) {
        
        if (CollUtil.isEmpty(sourceEntries)) {
            return new ArrayList<>();
        }
        
        return sourceEntries.stream().map(sourceDO -> {
            CqDeliveryDetailRespVO.CqSourceDeliveryDetailEntryRespVO sourceResp = 
                new CqDeliveryDetailRespVO.CqSourceDeliveryDetailEntryRespVO();
            
            // 映射基础标识字段
            sourceResp.setEntryId(sourceDO.getEntryId());
            sourceResp.setMainId(sourceDO.getMainId());
            sourceResp.setSeq(sourceDO.getSeq());
            
            // 映射物料基本信息
            sourceResp.setMaterialNoOld(sourceDO.getMaterialNoOld());
            sourceResp.setMaterialId(sourceDO.getMaterialId());
            sourceResp.setBatchNo(sourceDO.getBatchNo());
            sourceResp.setProductionDate(sourceDO.getProductionDate());
            sourceResp.setExpirationDate(sourceDO.getExpirationDate());
            
            // 映射数量与价格信息
            sourceResp.setQuantityOld(sourceDO.getQuantityOld());
            sourceResp.setPriceOld(sourceDO.getPriceOld());
            sourceResp.setTotalAmountOld(sourceDO.getTotalAmountOld());
            sourceResp.setQuantity(sourceDO.getQuantity());
            sourceResp.setTotalTaxAmount(sourceDO.getTotalTaxAmount());
            sourceResp.setDistSupplyPrice(sourceDO.getDistSupplyPrice());
            sourceResp.setTaxPrice(sourceDO.getTaxPrice());
            sourceResp.setAvgLogisticsCost(sourceDO.getAvgLogisticsCost());
            sourceResp.setShelvesPrice(sourceDO.getShelvesPrice());
            sourceResp.setTotalDiscountAmount(sourceDO.getTotalDiscountAmount());
            
            // 映射仓储、客户与组织信息
            sourceResp.setInvOrgStockId(sourceDO.getInvOrgStockId());
            sourceResp.setSaleOrgStockId(sourceDO.getSaleOrgStockId());
            sourceResp.setStockTypeId(sourceDO.getStockTypeId());
            sourceResp.setSaleOrgId(sourceDO.getSaleOrgId());
            sourceResp.setInvOrgId(sourceDO.getInvOrgId());
            sourceResp.setCustomerId(sourceDO.getCqCustomerId());
            
            // 映射状态标记信息
            sourceResp.setIsAssembly(sourceDO.getIsAssembly());
            sourceResp.setIsPageSplit(sourceDO.getIsPageSplit());
            sourceResp.setIsGift(sourceDO.getIsGift());
            sourceResp.setSrcError(sourceDO.getSrcError());
            
            // 映射OMS相关信息
            sourceResp.setOmsRowNum(sourceDO.getOmsRowNum());
            sourceResp.setOmsGoodsSn(sourceDO.getOmsGoodsSn());
            
            // 映射分销供货价不存在标记
            sourceResp.setDistPriceNotExist(sourceDO.getDistPriceNotExist());
            
            // 映射物料分组信息
            sourceResp.setMatgroupId(sourceDO.getMatgroupId());
            sourceResp.setProductType(sourceDO.getProductType());
            
            return sourceResp;
        }).collect(Collectors.toList());
    }

    /**
     * 将DO列表转换为RespVO列表（批量查询优化版本）
     */
    private List<CqDeliveryDetailRespVO> convertToRespVOList(List<CqDeliveryDetailDO> deliveryDetails) {
        if (CollUtil.isEmpty(deliveryDetails)) {
            return new ArrayList<>();
        }
        
        long startTime = System.currentTimeMillis();
        int totalCount = deliveryDetails.size();
        
        // 性能警告检查
        if (totalCount > BATCH_QUERY_SIZE_THRESHOLD) {
            log.warn("[convertToRespVOList][批量转换记录数({})超过阈值({}), 请注意性能影响]", 
                    totalCount, BATCH_QUERY_SIZE_THRESHOLD);
        }
        
        if (totalCount > MEMORY_WARNING_THRESHOLD) {
            log.warn("[convertToRespVOList][批量转换记录数({})超过内存警告阈值({}), 请注意内存使用]", 
                    totalCount, MEMORY_WARNING_THRESHOLD);
        }
        
        try {
            // 1. 提取所有主表ID
            List<Long> mainIds = deliveryDetails.stream()
                    .map(CqDeliveryDetailDO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            if (CollUtil.isEmpty(mainIds)) {
                log.warn("[convertToRespVOList][所有主表记录的ID都为空]");
                return new ArrayList<>();
            }
            
            // 分批处理，避免IN子句过长
            int batchSize = 800;
            Map<Long, List<CqDeliveryDetailEntryDO>> entriesMap = new HashMap<>();
            Map<Long, List<CqSourceDeliveryDetailEntryDO>> sourceEntriesMap = new HashMap<>();

            for (int i = 0; i < mainIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, mainIds.size());
                List<Long> batchMainIds = mainIds.subList(i, endIndex);

                // 2. 批量查询分录数据
                Map<Long, List<CqDeliveryDetailEntryDO>> batchEntriesMap = batchQueryEntriesByMainIds(batchMainIds);
                if (CollUtil.isNotEmpty(batchEntriesMap)) {
                    entriesMap.putAll(batchEntriesMap);
                }

                // 3. 批量查询源分录数据
                Map<Long, List<CqSourceDeliveryDetailEntryDO>> batchSourceEntriesMap = batchQuerySourceEntriesByMainIds(batchMainIds);
                if (CollUtil.isNotEmpty(batchSourceEntriesMap)) {
                    sourceEntriesMap.putAll(batchSourceEntriesMap);
                }
            }
            
            // 4. 组装结果
            List<CqDeliveryDetailRespVO> result = new ArrayList<>(totalCount);
            for (CqDeliveryDetailDO deliveryDetailDO : deliveryDetails) {
                Long mainId = deliveryDetailDO.getId();
                if (mainId == null) {
                    log.warn("[convertToRespVOList][跳过ID为空的主表记录，单据编号: {}]", deliveryDetailDO.getBillNo());
                    continue;
                }
                
                // 获取对应的分录数据
                List<CqDeliveryDetailEntryDO> entries = entriesMap.getOrDefault(mainId, new ArrayList<>());
                List<CqSourceDeliveryDetailEntryDO> sourceEntries = sourceEntriesMap.getOrDefault(mainId, new ArrayList<>());
                
                // 转换为RespVO（使用批量查询优化版本，避免重复查询）
                CqDeliveryDetailRespVO respVO = convertToRespVO(deliveryDetailDO, entries, sourceEntries);
                
                result.add(respVO);
            }
            
            long endTime = System.currentTimeMillis();
            log.info("[convertToRespVOList][批量转换完成，记录数: {}, 耗时: {}ms, 平均耗时: {}ms/条]", 
                    totalCount, endTime - startTime, (endTime - startTime) / totalCount);
            
            return result;
            
        } catch (Exception e) {
            log.error("[convertToRespVOList][批量转换异常，回退到逐个查询模式，记录数: {}]", totalCount, e);
            // 异常时回退到原始实现
            return deliveryDetails.stream().map(deliveryDetailDO -> {
                List<CqDeliveryDetailEntryDO> entries = deliveryDetailEntryMapper.selectListByMainId(deliveryDetailDO.getId());
                return convertToRespVO(deliveryDetailDO, entries);
            }).collect(Collectors.toList());
        }
    }

    /**
     * 批量查询分录数据
     * 
     * @param mainIds 主表ID列表
     * @return 按主表ID分组的分录数据Map
     */
    private Map<Long, List<CqDeliveryDetailEntryDO>> batchQueryEntriesByMainIds(List<Long> mainIds) {
        if (CollUtil.isEmpty(mainIds)) {
            return new HashMap<>();
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 批量查询所有分录数据
            LambdaQueryWrapper<CqDeliveryDetailEntryDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(CqDeliveryDetailEntryDO::getMainId, mainIds);
            List<CqDeliveryDetailEntryDO> allEntries = deliveryDetailEntryMapper.selectList(queryWrapper);
            
            // 按主表ID分组
            Map<Long, List<CqDeliveryDetailEntryDO>> entriesMap = allEntries.stream()
                    .collect(Collectors.groupingBy(CqDeliveryDetailEntryDO::getMainId));
            
            long endTime = System.currentTimeMillis();
            log.info("[batchQueryEntriesByMainIds][批量查询分录完成，主表ID数: {}, 分录数: {}, 耗时: {}ms]", 
                    mainIds.size(), allEntries.size(), endTime - startTime);
            
            return entriesMap;
            
        } catch (Exception e) {
            log.error("[batchQueryEntriesByMainIds][批量查询分录异常，主表ID数: {}]", mainIds.size(), e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询源分录数据
     * 
     * @param mainIds 主表ID列表
     * @return 按主表ID分组的源分录数据Map
     */
    private Map<Long, List<CqSourceDeliveryDetailEntryDO>> batchQuerySourceEntriesByMainIds(List<Long> mainIds) {
        if (CollUtil.isEmpty(mainIds)) {
            return new HashMap<>();
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 批量查询所有源分录数据
            LambdaQueryWrapper<CqSourceDeliveryDetailEntryDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(CqSourceDeliveryDetailEntryDO::getMainId, mainIds);
            List<CqSourceDeliveryDetailEntryDO> allSourceEntries = sourceDeliveryDetailEntryMapper.selectList(queryWrapper);
            
            // 按主表ID分组
            Map<Long, List<CqSourceDeliveryDetailEntryDO>> sourceEntriesMap = allSourceEntries.stream()
                    .collect(Collectors.groupingBy(CqSourceDeliveryDetailEntryDO::getMainId));
            
            long endTime = System.currentTimeMillis();
            log.info("[batchQuerySourceEntriesByMainIds][批量查询源分录完成，主表ID数: {}, 源分录数: {}, 耗时: {}ms]", 
                    mainIds.size(), allSourceEntries.size(), endTime - startTime);
            
            return sourceEntriesMap;
            
        } catch (Exception e) {
            log.error("[batchQuerySourceEntriesByMainIds][批量查询源分录异常，主表ID数: {}]", mainIds.size(), e);
            return new HashMap<>();
        }
    }

    /**
     * 更新发货明细结算信息
     * 
     * 根据传入的结算信息VO对象，更新苍穹发货明细的各项结算相关属性，包括客户信息、仓库信息、
     * 结算标记、非销售相关字段等。此方法在结算流程中用于调整发货明细的结算状态和相关标记。
     *
     * @param settleInfo 结算信息VO对象，包含需要更新的各项结算属性
     * @return 更新是否成功
     */
    @Override
    public boolean updateDeliveryDetailSettleInfo(CqDeliveryDetailSettleInfoVO settleInfo) {
        // 参数校验：如果传入的结算信息对象为空，则无法进行更新，直接返回失败
        if (settleInfo == null) {
            return false;
        }
        
        // 根据ID查询发货明细记录
        // 如果找不到记录，说明该结算信息无对应的发货明细，无法更新
        CqDeliveryDetailDO deliveryDetail = null;
        if (settleInfo.getId() != null) {
            deliveryDetail = deliveryDetailMapper.selectById(settleInfo.getId());
        }
        
        if (deliveryDetail == null) {
            return false;
        }
        
        // ========== 客户相关字段更新 ==========
        // 更新客户不存在标记 - 用于标识客户在主数据中不存在的情况
        if (settleInfo.getIsNotExitCustomer() != null) {
            deliveryDetail.setCustomerNotExist(settleInfo.getIsNotExitCustomer().toString());
        }
        
        // 更新客户ID - 设置苍穹系统中的客户ID
        if (settleInfo.getCqCustomerId() != null) {
            deliveryDetail.setCustomerId(settleInfo.getCqCustomerId());
        }
        
        // ========== 仓库相关字段更新 ==========
        // 更新排除仓库标记 - 用于标识该发货明细是否排除特定仓库
        if (settleInfo.getIsExcludedWarehouse() != null) {
            deliveryDetail.setExcludeWarehouse(settleInfo.getIsExcludedWarehouse());
        }
        
        // ========== 组织相关字段更新 ==========
        // 更新店铺组织ID - 设置店铺所属的组织ID
        if (settleInfo.getShopOrgId() != null) {
            deliveryDetail.setShopOrg(settleInfo.getShopOrgId());
        }
        
        // 更新销售组织是否不存在标志 - 标识销售组织是否不存在
        if (settleInfo.getIsNotExitSaleOrg() != null) {
            deliveryDetail.setSaleOrgNotExist(settleInfo.getIsNotExitSaleOrg());
        }
        
        // 更新库存组织是否不存在标志 - 标识库存组织是否不存在
        if (settleInfo.getIsNotExitInvOrg() != null) {
            deliveryDetail.setInvOrgNotExist(settleInfo.getIsNotExitInvOrg());
        }
        
        // 更新销售组织仓库是否不存在标志 - 标识销售组织仓库是否不存在
        if (settleInfo.getIsNotExitSaleOrgStock() != null) {
            deliveryDetail.setSaleOrgStockNotExist(settleInfo.getIsNotExitSaleOrgStock());
        }
        
        // 更新库存组织仓库是否不存在标志 - 标识库存组织仓库是否不存在
        if (settleInfo.getIsNotExitInvOrgStock() != null) {
            deliveryDetail.setInvOrgStockNotExist(settleInfo.getIsNotExitInvOrgStock());
        }
        
        // ========== 物料相关字段更新 ==========
        // 更新物料是否不存在标志 - 标识物料是否不存在
        if (settleInfo.getIsNotExitMaterial() != null) {
            deliveryDetail.setMaterialNotExist(settleInfo.getIsNotExitMaterial());
        }
        
        // ========== 单据相关字段更新 ==========
        // 更新下游单据类型 - 设置下游系统的单据类型信息
        if (StringUtils.isNotBlank(settleInfo.getDownstreamBillType())) {
            deliveryDetail.setDownstreamType(settleInfo.getDownstreamBillType());
        }
        
        // 更新退单关联订单是否不存在标志 - 标识退单关联订单是否不存在
        if (settleInfo.getRelatedOrderNotExists() != null) {
            deliveryDetail.setRelatedOrderNotExists(settleInfo.getRelatedOrderNotExists().toString());
        }
        
        // 更新品牌分单标志 - 标识是否按品牌拆分单据
        if (settleInfo.getIsBrandSplitBill() != null) {
            deliveryDetail.setIsBrandSplitBill(settleInfo.getIsBrandSplitBill());
        }
        
        // ========== 系统传输相关字段更新 ==========
        // 更新EAS传输标记 - 标识是否不传输到EAS系统
        if (settleInfo.getIsNotSendEas() != null) {
            deliveryDetail.setNotTransferToEAS(settleInfo.getIsNotSendEas().toString());
        }
        
        // ========== 销售标记相关字段更新 ==========
        // 更新非销售标记 - 标识该发货明细是否为非销售业务
        if (settleInfo.getIsNotSale() != null) {
            deliveryDetail.setIsNotSale(settleInfo.getIsNotSale());
        }
        
        // ========== 非销售相关字段更新 ==========
        // 更新预算公司 - 非销售时的预算公司信息
        if (StringUtils.isNotBlank(settleInfo.getYdBudgetcompany())) {
            deliveryDetail.setBudgetCompany(settleInfo.getYdBudgetcompany());
        }
        
        // 更新非销售店铺 - 非销售时使用的店铺信息
        if (StringUtils.isNotBlank(settleInfo.getYdNotsaleshop())) {
            deliveryDetail.setNotSaleShop(settleInfo.getYdNotsaleshop());
        }
        
        // 更新申请部门 - 非销售时的申请部门信息
        if (StringUtils.isNotBlank(settleInfo.getYdApplydepart())) {
            deliveryDetail.setApplyDepartment(settleInfo.getYdApplydepart());
        }
        
        // 更新申请用途 - 非销售时的申请用途信息
        if (StringUtils.isNotBlank(settleInfo.getYdRequisitionuse())) {
            deliveryDetail.setRequisitionUse(settleInfo.getYdRequisitionuse());
        }
        
        // 更新预算部门 - 非销售时的预算部门信息
        if (StringUtils.isNotBlank(settleInfo.getYdBudgetdepart())) {
            deliveryDetail.setBudgetDepartment(settleInfo.getYdBudgetdepart());
        }
        
        // 更新预算科目 - 非销售时的预算科目信息
        if (StringUtils.isNotBlank(settleInfo.getYdBudgetaccount())) {
            deliveryDetail.setBudgetAccount(settleInfo.getYdBudgetaccount());
        }
        
        // ========== 备注相关字段更新 ==========
        // 更新商家备注
        if (settleInfo.getMerchantRemark() != null) {
            deliveryDetail.setMerchantRemark(settleInfo.getMerchantRemark());
        }

        // 更新订单备注
        if (settleInfo.getOrderRemark() != null) {
            deliveryDetail.setRemark(settleInfo.getOrderRemark());
        }
        
        // ========== 成本中心相关字段更新 ==========
        // 更新成本中心 - 设置成本中心信息
        if (StringUtils.isNotBlank(settleInfo.getCostCenter())) {
            deliveryDetail.setCostCenter(settleInfo.getCostCenter());
        }
        
        // 执行数据库更新操作，返回是否更新成功
        // 通过影响行数判断更新是否成功，大于0表示更新成功
        return deliveryDetailMapper.updateById(deliveryDetail) > 0;
    }

    /**
     * 统一更新发货明细结算信息和状态
     * 将结算信息更新和状态更新合并为一次数据库操作，提升性能
     *
     * @param settleInfo 结算信息VO，包含需要更新的各项结算属性
     * @param settleStatus 结算状态
     * @param failReason 失败原因
     * @return 是否更新成功
     */
    @Override
    public boolean updateDeliveryDetailSettleInfoAndStatus(CqDeliveryDetailSettleInfoVO settleInfo, String settleStatus, String failReason) {
        long startTime = System.currentTimeMillis();
        
        // 参数校验：如果传入的结算信息对象为空，则无法进行更新，直接返回失败
        if (settleInfo == null) {
            log.error("[updateDeliveryDetailSettleInfoAndStatus][结算信息对象为空]");
            return false;
        }
        
        if (StrUtil.isBlank(settleStatus)) {
            log.error("[updateDeliveryDetailSettleInfoAndStatus][结算状态不能为空，ID: {}]", settleInfo.getId());
            return false;
        }
        
        // 根据ID查询发货明细记录
        // 如果找不到记录，说明该结算信息无对应的发货明细，无法更新
        CqDeliveryDetailDO deliveryDetail = null;
        if (settleInfo.getId() != null) {
            deliveryDetail = deliveryDetailMapper.selectById(settleInfo.getId());
        }
        
        if (deliveryDetail == null) {
            log.error("[updateDeliveryDetailSettleInfoAndStatus][发货明细记录不存在，ID: {}]", settleInfo.getId());
            return false;
        }
        
        try {
            // 更新客户相关标记字段
            // 更新客户不存在标记 - 用于标识客户在主数据中不存在的情况
            if (settleInfo.getIsNotExitCustomer() != null) {
                deliveryDetail.setCustomerNotExist(settleInfo.getIsNotExitCustomer().toString());
            }
            
            // 更新仓库相关标记字段
            // 更新排除仓库标记 - 用于标识该发货明细是否排除特定仓库
            if (settleInfo.getIsExcludedWarehouse() != null) {
                deliveryDetail.setExcludeWarehouse(settleInfo.getIsExcludedWarehouse());
            }
            
            // 更新下游单据和EAS传输相关字段
            // 更新下游单据类型 - 设置下游系统的单据类型信息
            if (StringUtils.isNotBlank(settleInfo.getDownstreamBillType())) {
                deliveryDetail.setDownstreamType(settleInfo.getDownstreamBillType());
            }
            
            // 更新EAS传输标记 - 标识是否不传输到EAS系统
            if (settleInfo.getIsNotSendEas() != null) {
                deliveryDetail.setNotTransferToEAS(settleInfo.getIsNotSendEas().toString());
            }
            
            // 更新客户ID - 设置苍穹系统中的客户ID
            if (settleInfo.getCqCustomerId() != null) {
                deliveryDetail.setCustomerId(settleInfo.getCqCustomerId());
            }
            
            // 更新非销售标记 - 标识该发货明细是否为非销售业务
            if (settleInfo.getIsNotSale() != null) {
                deliveryDetail.setIsNotSale(settleInfo.getIsNotSale());
            }
            
            // 更新非销售相关字段
            // 更新预算公司 - 非销售时的预算公司信息
            if (StringUtils.isNotBlank(settleInfo.getYdBudgetcompany())) {
                deliveryDetail.setBudgetCompany(settleInfo.getYdBudgetcompany());
            }
            
            // 更新非销售店铺 - 非销售时使用的店铺信息
            if (StringUtils.isNotBlank(settleInfo.getYdNotsaleshop())) {
                deliveryDetail.setNotSaleShop(settleInfo.getYdNotsaleshop());
            }
            
            // 更新申请部门 - 非销售时的申请部门信息
            if (StringUtils.isNotBlank(settleInfo.getYdApplydepart())) {
                deliveryDetail.setApplyDepartment(settleInfo.getYdApplydepart());
            }
            
            // 更新申请用途 - 非销售时的申请用途信息
            if (StringUtils.isNotBlank(settleInfo.getYdRequisitionuse())) {
                deliveryDetail.setRequisitionUse(settleInfo.getYdRequisitionuse());
            }
            
            // 更新预算部门 - 非销售时的预算部门信息
            if (StringUtils.isNotBlank(settleInfo.getYdBudgetdepart())) {
                deliveryDetail.setBudgetDepartment(settleInfo.getYdBudgetdepart());
            }
            
            // 更新预算科目 - 非销售时的预算科目信息
            if (StringUtils.isNotBlank(settleInfo.getYdBudgetaccount())) {
                deliveryDetail.setBudgetAccount(settleInfo.getYdBudgetaccount());
            }
            
            // 更新组织和仓库ID
            // 更新店铺组织ID - 设置店铺所属的组织ID
            if (settleInfo.getShopOrgId() != null) {
                deliveryDetail.setShopOrg(settleInfo.getShopOrgId());
            }

            // // 更新仓库ID - 设置苍穹系统中的仓库ID
            // if (settleInfo.getCqWarehouseId() != null) {
            //     deliveryDetail.setWarehouseId(settleInfo.getCqWarehouseId());
            // }

            // 更新品牌分单标志 - 标识是否按品牌拆分单据
            if (settleInfo.getIsBrandSplitBill() != null) {
                deliveryDetail.setIsBrandSplitBill(settleInfo.getIsBrandSplitBill());
            }
            
            // 更新退单关联订单是否不存在标志 - 标识退单关联订单是否不存在
            if (settleInfo.getRelatedOrderNotExists() != null) {
                deliveryDetail.setRelatedOrderNotExists(settleInfo.getRelatedOrderNotExists().toString());
            }
            
            // 更新销售组织仓库是否不存在标志 - 标识销售组织仓库是否不存在
            if (settleInfo.getIsNotExitSaleOrgStock() != null) {
                deliveryDetail.setSaleOrgStockNotExist(settleInfo.getIsNotExitSaleOrgStock());
            }
            
            // 更新库存组织仓库是否不存在标志 - 标识库存组织仓库是否不存在
            if (settleInfo.getIsNotExitInvOrgStock() != null) {
                deliveryDetail.setInvOrgStockNotExist(settleInfo.getIsNotExitInvOrgStock());
            }
            
            // 更新物料是否不存在标志 - 标识物料是否不存在
            if (settleInfo.getIsNotExitMaterial() != null) {
                deliveryDetail.setMaterialNotExist(settleInfo.getIsNotExitMaterial());
            }

            // 更新销售组织是否不存在标志 - 标识销售组织是否不存在
            if (settleInfo.getIsNotExitSaleOrg() != null) {
                deliveryDetail.setSaleOrgNotExist(settleInfo.getIsNotExitSaleOrg());
            }

            // 更新库存组织是否不存在标志 - 标识库存组织是否不存在
            if (settleInfo.getIsNotExitInvOrg() != null) {
                deliveryDetail.setInvOrgNotExist(settleInfo.getIsNotExitInvOrg());
            }

            // 更新商家备注
            if (settleInfo.getMerchantRemark() != null) {
                deliveryDetail.setMerchantRemark(settleInfo.getMerchantRemark());
            }

            // 更新订单备注
            if (settleInfo.getOrderRemark() != null) {
                deliveryDetail.setRemark(settleInfo.getOrderRemark());
            }
            
            // ========== 成本中心相关字段更新 ==========
            // 更新成本中心 - 设置成本中心信息
            if (StringUtils.isNotBlank(settleInfo.getCostCenter())) {
                deliveryDetail.setCostCenter(settleInfo.getCostCenter());
            }
            
            // 统一更新结算状态和失败原因
            deliveryDetail.setSettleStatus(settleStatus);
            deliveryDetail.setModifyTime(LocalDateTime.now());
            
            // 如果失败原因超过1500字符，进行截取
            if (StrUtil.isNotBlank(failReason) && failReason.length() > 1500) {
                failReason = failReason.substring(0, 1500);
                log.warn("[updateDeliveryDetailSettleInfoAndStatus][失败原因超过1500字符，已截取，ID: {}]", settleInfo.getId());
            }
            deliveryDetail.setFailReason(failReason);
            
            // 执行数据库更新操作，返回是否更新成功
            // 通过影响行数判断更新是否成功，大于0表示更新成功
            boolean success = deliveryDetailMapper.updateById(deliveryDetail) > 0;
            
            long endTime = System.currentTimeMillis();
            log.info("[updateDeliveryDetailSettleInfoAndStatus][统一更新发货明细结算信息和状态完成，ID: {}, 结算状态: {}, 耗时: {}ms, 成功: {}]", 
                    settleInfo.getId(), settleStatus, endTime - startTime, success);
            
            return success;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("[updateDeliveryDetailSettleInfoAndStatus][统一更新发货明细结算信息和状态异常，ID: {}, 结算状态: {}, 耗时: {}ms]", 
                    settleInfo.getId(), settleStatus, endTime - startTime, e);
            return false;
        }
    }

    @Override
    public boolean batchUpdateDeliveryDetailEntryFlags(String billNo, List<CqDeliveryDetailEntrySettleInfoVO> entrySettleInfos) {
        // 参数校验
        if (StrUtil.isBlank(billNo)) {
            log.error("[batchUpdateDeliveryDetailEntryFlags][单据编号为空]");
            return false;
        }
        
        if (CollectionUtils.isEmpty(entrySettleInfos)) {
            log.warn("[batchUpdateDeliveryDetailEntryFlags][分录结算信息列表为空，单据编号: {}]", billNo);
            return true; // 没有数据需要更新，视为成功
        }
        
        // 记录所有分录的更新结果
        boolean allSuccess = true;
        int totalEntries = entrySettleInfos.size();
        int successCount = 0;
        
        // 遍历分录结算信息列表，逐个更新
        for (CqDeliveryDetailEntrySettleInfoVO entrySettleInfo : entrySettleInfos) {
            Long mainId = entrySettleInfo.getMainId();
            Long entryId = entrySettleInfo.getEntryId();
            
            if (mainId == null || entryId == null) {
                log.error("[batchUpdateDeliveryDetailEntryFlags][主表ID或分录ID为空，单据编号: {}, 物料ID: {}]", 
                        billNo, entrySettleInfo.getSinglematerialid());
                allSuccess = false;
                continue;
            }
            
            try {
                // 查询分录对象
                CqDeliveryDetailEntryDO entryDO = deliveryDetailEntryMapper.selectOne(
                        new LambdaQueryWrapper<CqDeliveryDetailEntryDO>()
                                .eq(CqDeliveryDetailEntryDO::getMainId, mainId)
                                .eq(CqDeliveryDetailEntryDO::getEntryId, entryId));
                
                if (entryDO == null) {
                    log.error("[batchUpdateDeliveryDetailEntryFlags][分录不存在，单据编号: {}, 主表ID: {}, 分录ID: {}]", 
                            billNo, mainId, entryId);
                    allSuccess = false;
                    continue;
                }
                
                // 设置分录标志位
                if (entrySettleInfo.getIsMainMatNumCombined() != null) {
                    entryDO.setIsAssembly(entrySettleInfo.getIsMainMatNumCombined());
                }
                // 物料不存在
                if (entrySettleInfo.getIsNotExitMaterial() != null) {
                    entryDO.setMxMaterialNotExist(entrySettleInfo.getIsNotExitMaterial().toString());
                }
                
                if (entrySettleInfo.getIsMaterialRelationRepeat() != null) {
                    entryDO.setMxMaterialRefDuplicated(entrySettleInfo.getIsMaterialRelationRepeat().toString());
                }
                // 剔除物料
                if (entrySettleInfo.getIsExcludedMaterial() != null) {
                    entryDO.setExcludeMaterial(entrySettleInfo.getIsExcludedMaterial().toString());
                }
                // 组装品
                if (entrySettleInfo.getIsMainMatNumCombined() != null) {
                    entryDO.setIsAssembly(entrySettleInfo.getIsMainMatNumCombined());
                }
                // 物料ID
                if (entrySettleInfo.getSinglematerialid() != null) {
                    entryDO.setSingleMaterialId(entrySettleInfo.getSinglematerialid());
                }
                
                // 更新分录
                int rows = deliveryDetailEntryMapper.updateById(entryDO);
                if (rows > 0) {
                    successCount++;
                    log.info("[batchUpdateDeliveryDetailEntryFlags][更新分录标志位成功，单据编号: {}, 货品ID: {}, 主表ID: {}, 分录ID: {}]", 
                            billNo, entrySettleInfo.getSinglematerialid(), mainId, entryId);
                } else {
                    allSuccess = false;
                    log.error("[batchUpdateDeliveryDetailEntryFlags][更新分录标志位失败，单据编号: {}, 货品ID: {}, 主表ID: {}, 分录ID: {}]", 
                            billNo, entrySettleInfo.getSinglematerialid(), mainId, entryId);
                }
                
            } catch (Exception e) {
                allSuccess = false;
                log.error("[batchUpdateDeliveryDetailEntryFlags][更新分录标志位异常，单据编号: {}, 货品ID: {}, 主表ID: {}, 分录ID: {}]", 
                        billNo, entrySettleInfo.getSinglematerialid(), mainId, entryId, e);
            }
        }
        
        log.info("[batchUpdateDeliveryDetailEntryFlags][批量更新分录标志位完成，单据编号: {}, 总数: {}, 成功数: {}, 全部成功: {}]", 
                billNo, totalEntries, successCount, allSuccess);
        
        return allSuccess;
    }

    /**
     * 根据主表ID删除拆单明细分录
     *
     * @param mainId 主表ID
     * @return 删除结果，true表示成功，false表示失败
     */
    @Override
    public boolean deleteSourceDeliveryDetailEntries(Long mainId) {
        if (mainId == null) {
            log.error("[deleteSourceDeliveryDetailEntries][主表ID为空]");
            return false;
        }
        
        try {
            // 构建删除条件
            LambdaQueryWrapper<CqSourceDeliveryDetailEntryDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CqSourceDeliveryDetailEntryDO::getMainId, mainId);
            
            // 执行删除操作
            int rows = sourceDeliveryDetailEntryMapper.delete(queryWrapper);
            
            log.info("[deleteSourceDeliveryDetailEntries][删除源发货明细分录成功，主表ID: {}, 删除记录数: {}]", mainId, rows);
            return true;
        } catch (Exception e) {
            log.error("[deleteSourceDeliveryDetailEntries][删除源发货明细分录异常，主表ID: {}]", mainId, e);
            return false;
        }
    }
    
    /**
     * 批量保存拆单明细分录
     *
     * @param entries 拆单明细分录列表
     * @return 保存结果，true表示成功，false表示失败
     */
    @Override
    public boolean batchSaveSourceDeliveryDetailEntries(List<CqSourceDeliveryDetailEntryDO> entries) {
        if (CollectionUtils.isEmpty(entries)) {
            log.warn("[batchSaveSourceDeliveryDetailEntries][拆单明细分录列表为空]");
            return true;
        }
        
        try {
            // 执行批量插入操作
            sourceDeliveryDetailEntryMapper.insertBatch(entries);
            
            log.info("[batchSaveSourceDeliveryDetailEntries][批量保存拆单明细分录成功，记录数: {}]", entries.size());
            return true;
        } catch (Exception e) {
            log.error("[batchSaveSourceDeliveryDetailEntries][批量保存拆单明细分录异常]", e);
            return false;
        }
    }
    
    /**
     * 根据单据编号列表查询发货明细主表和源明细分录数据
     *
     * @param billNos 单据编号列表
     * @return 包含主表和源明细分录数据的DTO对象列表
     */
    @Override
    public List<CqDeliveryDetailWithSourceEntriesDTO> getDeliveryDetailsWithSourceEntriesByBillNos(List<String> billNos) {
        // 参数校验
        if (!EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            log.warn("[getDeliveryDetailsWithSourceEntriesByBillNos][单据编号列表为空]");
            return new ArrayList<>();
        }
        
        log.info("[getDeliveryDetailsWithSourceEntriesByBillNos][开始查询，单据编号数量: {}]", billNos.size());
        
        try {
            // 1. 查询主表数据
            LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.in(CqDeliveryDetailDO::getBillNo, billNos);
            List<CqDeliveryDetailDO> deliveryDetails = deliveryDetailMapper.selectList(queryWrapper);
            
            if (CollectionUtils.isEmpty(deliveryDetails)) {
                log.info("[getDeliveryDetailsWithSourceEntriesByBillNos][未找到匹配的发货明细记录]");
                return new ArrayList<>();
            }
            
            log.info("[getDeliveryDetailsWithSourceEntriesByBillNos][查询到发货明细记录数: {}]", deliveryDetails.size());
            
            // 2. 提取主表ID列表
            List<Long> mainIds = deliveryDetails.stream()
                    .map(CqDeliveryDetailDO::getId)
                    .collect(Collectors.toList());
            
            // 3. 查询所有关联的源明细分录数据
            List<CqSourceDeliveryDetailEntryDO> allSourceEntries = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(mainIds)) {
                LambdaQueryWrapper<CqSourceDeliveryDetailEntryDO> sourceQueryWrapper = new LambdaQueryWrapper<>();
                sourceQueryWrapper.in(CqSourceDeliveryDetailEntryDO::getMainId, mainIds);
                allSourceEntries = sourceDeliveryDetailEntryMapper.selectList(sourceQueryWrapper);
                log.info("[getDeliveryDetailsWithSourceEntriesByBillNos][查询到源明细分录记录数: {}]", allSourceEntries.size());
            }
            
            // 4. 将源明细分录按mainId分组
            Map<Long, List<CqSourceDeliveryDetailEntryDO>> sourceEntriesMap = allSourceEntries.stream()
                    .collect(Collectors.groupingBy(CqSourceDeliveryDetailEntryDO::getMainId));
            
            // 5. 组装DTO对象
            List<CqDeliveryDetailWithSourceEntriesDTO> result = new ArrayList<>(deliveryDetails.size());
            for (CqDeliveryDetailDO deliveryDetail : deliveryDetails) {
                // 获取当前主表ID对应的源明细分录列表
                List<CqSourceDeliveryDetailEntryDO> sourceEntries = sourceEntriesMap.getOrDefault(
                        deliveryDetail.getId(), new ArrayList<>());
                
                // 创建DTO对象并添加到结果列表
                CqDeliveryDetailWithSourceEntriesDTO dto = CqDeliveryDetailWithSourceEntriesDTO.build(
                        deliveryDetail, sourceEntries);
                result.add(dto);
            }
            
            log.info("[getDeliveryDetailsWithSourceEntriesByBillNos][查询完成，返回DTO对象数: {}]", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("[getDeliveryDetailsWithSourceEntriesByBillNos][查询异常]", e);
            return new ArrayList<>();
        }
    }   

    /**
     * 根据单据编号列表更新下游单号
     *
     * @param billNos 单据编号列表
     * @param downstreamNo 下游单号
     * @return 是否更新成功
     */
    @Override
    public boolean updateDownstreamNoByBillNo(List<String> billNos, String downstreamNo) {
        if (!EnhancedCollectionUtils.hasValidStringElements(billNos) || StrUtil.isBlank(downstreamNo)) {
            log.error("[updateDownstreamNoByBillNo][单据编号或下游单号为空]");
            return false;
        }

        try {
            // 构建更新条件
            LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(CqDeliveryDetailDO::getBillNo, billNos);    
            updateWrapper.set(CqDeliveryDetailDO::getDownstreamNo, downstreamNo);       

            // 执行更新操作
            int rows = deliveryDetailMapper.update(null, updateWrapper);
            return rows > 0;
        } catch (Exception e) {
            log.error("[updateDownstreamNoByBillNo][更新下游单号异常]", e);
            return false;
        }
    }
    
    /**
     * 根据单据编号列表更新参与合单标记
     *
     * @param billNos 单据编号列表
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateParticipateInCombineByBillNo(List<String> billNos) {
        if (!EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            log.error("[updateParticipateInCombineByBillNo][单据编号列表为空]");
            return false;
        }

        try {
            // 分批处理，避免IN子句超过MySQL最大值限制
            int batchSize = 1000; // 每批处理1000条记录
            int totalUpdated = 0;
            
            for (int i = 0; i < billNos.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, billNos.size());
                List<String> batchBillNos = billNos.subList(i, endIndex);
                
                // 构建更新条件
                LambdaUpdateWrapper<CqDeliveryDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(CqDeliveryDetailDO::getBillNo, batchBillNos);    
                updateWrapper.set(CqDeliveryDetailDO::getParticipateInCombine, "1");  
                // 修改时间     
                updateWrapper.set(CqDeliveryDetailDO::getModifyTime, LocalDateTime.now());

                // 执行更新操作
                int rows = deliveryDetailMapper.update(null, updateWrapper);
                totalUpdated += rows;
                
                log.debug("[updateParticipateInCombineByBillNo][批次更新完成，批次大小: {}, 影响行数: {}]", batchBillNos.size(), rows);
            }
            
            log.info("[updateParticipateInCombineByBillNo][分批更新完成，总记录数: {}, 总影响行数: {}]", billNos.size(), totalUpdated);
            return totalUpdated > 0;
        } catch (Exception e) {
            log.error("[updateParticipateInCombineByBillNo][更新参与合单标记异常]", e);
            return false;
        }
    }
    
    /**
     * 根据分录ID列表更新下游单号
     *
     * @param entryIds 分录ID列表
     * @param downstreamNo 下游单号
     * @param mergeFailReason 合单失败原因
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDownstreamBillNoByEntryId(List<Long> entryIds, String downstreamNo, String mergeFailReason) {
        if (!EnhancedCollectionUtils.hasValidElements(entryIds)) {
            log.error("[updateDownstreamBillNoByEntryId][分录ID列表为空]");
            return false;
        }
        
        // 参数校验：下游单号和合单失败原因不能同时为空
        if (StrUtil.isBlank(downstreamNo) && StrUtil.isBlank(mergeFailReason)) {
            log.error("[updateDownstreamBillNoByEntryId][下游单号和合单失败原因不能同时为空]");
            return false;
        }

        try {
            // 分批处理，避免IN子句超过MySQL最大值限制
            int batchSize = 500; // 每批处理500条记录
            int totalUpdated = 0;
            
            for (int i = 0; i < entryIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, entryIds.size());
                List<Long> batchEntryIds = entryIds.subList(i, endIndex);
                
                // 构建更新条件
                LambdaUpdateWrapper<CqSourceDeliveryDetailEntryDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(CqSourceDeliveryDetailEntryDO::getEntryId, batchEntryIds);    
                
                // 设置下游单号（可能为空字符串，表示清空）
                updateWrapper.set(CqSourceDeliveryDetailEntryDO::getDownstreamBillNo, downstreamNo);
                
                // 处理合单失败原因：截取确保不超过数据库字段长度限制
                String processedMergeFailReason = mergeFailReason;
                if (StrUtil.isNotBlank(mergeFailReason) && mergeFailReason.length() > 1000) {
                    processedMergeFailReason = mergeFailReason.substring(0, 1000);
                    log.warn("[updateDownstreamBillNoByEntryId][合单失败原因超长，已截取至1000字符]");
                }
                updateWrapper.set(CqSourceDeliveryDetailEntryDO::getMergeFailReason, processedMergeFailReason);
                
                // 执行更新操作
                int rows = sourceDeliveryDetailEntryMapper.update(null, updateWrapper);
                totalUpdated += rows;
                
                log.debug("[updateDownstreamBillNoByEntryId][批次更新完成，批次大小: {}, 影响行数: {}]", batchEntryIds.size(), rows);
            }
            
            log.info("[updateDownstreamBillNoByEntryId][分批更新完成，总记录数: {}, 总影响行数: {}]", entryIds.size(), totalUpdated);
            return totalUpdated > 0;
        } catch (Exception e) {
            log.error("[updateDownstreamBillNoByEntryId][更新分录下游单号异常]", e);
            return false;
        }
    }
    
    /**
     * 根据发货日期范围查询订单编号和交易号
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param isReturn 是否退货
     * @return 订单编号和交易号信息列表
     */
    @Override
    public List<OrderDealInfoVO> getOrderNoAndDealCodeByDeliveryDate(LocalDate startDate, LocalDate endDate, Boolean isReturn) {
        // 参数校验
        if (startDate == null || endDate == null) {
            log.warn("[getOrderNoAndDealCodeByDeliveryDate][发货日期范围参数为空, 开始日期:{}, 结束日期:{}]", startDate, endDate);
            return new ArrayList<>();
        }
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.between(CqDeliveryDetailDO::getDeliveryDate, startDate, endDate);
            // 平台等于1和5（枚举:1:E3 2:旺店通 3:吉客云 4:万里牛 5:新E3）
            queryWrapper.in(CqDeliveryDetailDO::getPlatform, Arrays.asList(1, 5));
            // 是否退货
            queryWrapper.eq(CqDeliveryDetailDO::getIsReturn, isReturn);
            // 查询订单编号和交易号
            queryWrapper.select(CqDeliveryDetailDO::getOrderNo, CqDeliveryDetailDO::getDealCode);
            
            // 执行查询
            List<CqDeliveryDetailDO> detailDOs = deliveryDetailMapper.selectList(queryWrapper);
            
            // 转换结果
            return detailDOs.stream()
                    .filter(detail -> StrUtil.isNotBlank(detail.getOrderNo()) || StrUtil.isNotBlank(detail.getDealCode()))
                    .map(detail -> {
                        OrderDealInfoVO vo = new OrderDealInfoVO();
                        vo.setOrderNo(detail.getOrderNo());
                        vo.setDealCode(detail.getDealCode());
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[getOrderNoAndDealCodeByDeliveryDate][查询异常，开始日期:{}, 结束日期:{}]", startDate, endDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据单据编号和结算状态查询发货明细（优化版，只返回单号和结算状态）
     *
     * @param billNos 单据编号列表，可为null。只有包含有效字符串元素的列表才会作为查询条件
     * @param settleStatus 结算状态，可为null
     * @return 简化的发货明细列表
     */
    @Override
    public List<CqDeliveryDetailSimpleDTO> getDeliveryDetailSimpleBySettleStatus(List<String> billNos, String settleStatus) {
        // 构建查询条件
        LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        
        // 只查询需要的字段
        queryWrapper.select(CqDeliveryDetailDO::getBillNo, CqDeliveryDetailDO::getSettleStatus);
        
        // 添加billNo条件（如果不为空）
        if (EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            queryWrapper.in(CqDeliveryDetailDO::getBillNo, billNos);
        }
        
        // 添加结算状态条件
        if (StrUtil.isBlank(settleStatus)) {
            log.error("[getDeliveryDetailSimpleBySettleStatus][结算状态不能为空]");
            throw new IllegalArgumentException("结算状态不能为空");
        }
        queryWrapper.eq(CqDeliveryDetailDO::getSettleStatus, settleStatus);
        
        // 执行查询
        List<CqDeliveryDetailDO> results = deliveryDetailMapper.selectList(queryWrapper);
        
        // 转换为简化DTO
        return results.stream()
                .map(detail -> {
                    CqDeliveryDetailSimpleDTO dto = new CqDeliveryDetailSimpleDTO();
                    dto.setBillNo(detail.getBillNo());
                    dto.setSettleStatus(detail.getSettleStatus());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CqDeliveryDetailSimpleDTO> getDeliveryDetailSimpleBySettleStatus(List<String> billNos, String settleStatus, List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        // 构建查询条件
        LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        
        // 只查询需要的字段
        queryWrapper.select(CqDeliveryDetailDO::getBillNo, CqDeliveryDetailDO::getSettleStatus);
        
        // 添加billNo条件（如果不为空）
        if (EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            queryWrapper.in(CqDeliveryDetailDO::getBillNo, billNos);
        }
        
        // 添加结算状态条件
        if (StrUtil.isBlank(settleStatus)) {
            log.error("[getDeliveryDetailSimpleBySettleStatus][结算状态不能为空]");
            throw new IllegalArgumentException("结算状态不能为空");
        }
        queryWrapper.eq(CqDeliveryDetailDO::getSettleStatus, settleStatus);
        
        // 添加店铺编号条件（如果不为空）
        if (EnhancedCollectionUtils.hasValidStringElements(shopNos)) {
            queryWrapper.in(CqDeliveryDetailDO::getShopNo, shopNos);
        }
        
        // 添加发货日期范围条件（如果不为空）
        if (startDate != null && endDate != null) {
            queryWrapper.between(CqDeliveryDetailDO::getDeliveryDate, 
                    startDate.atStartOfDay(), 
                    endDate.atTime(23, 59, 59));
        } else if (startDate != null) {
            queryWrapper.ge(CqDeliveryDetailDO::getDeliveryDate, startDate.atStartOfDay());
        } else if (endDate != null) {
            queryWrapper.le(CqDeliveryDetailDO::getDeliveryDate, endDate.atTime(23, 59, 59));
        }
        
        // 执行查询
        List<CqDeliveryDetailDO> results = deliveryDetailMapper.selectList(queryWrapper);
        
        // 转换为简化DTO
        return results.stream()
                .map(detail -> {
                    CqDeliveryDetailSimpleDTO dto = new CqDeliveryDetailSimpleDTO();
                    dto.setBillNo(detail.getBillNo());
                    dto.setSettleStatus(detail.getSettleStatus());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 将DTO转换为结算信息VO
     */
    private CqDeliveryDetailEntrySettleInfoVO convertToEntrySettleInfoVO(DeliveryDetailEntrySettleUpdateDTO entryDTO) {
        CqDeliveryDetailEntrySettleInfoVO vo = new CqDeliveryDetailEntrySettleInfoVO();
        vo.setEntryId(entryDTO.getEntryId());
        vo.setMainId(entryDTO.getMainId());
        vo.setSinglematerialid(entryDTO.getSingleMaterialId());
        vo.setIsExcludedMaterial(Boolean.TRUE.equals(entryDTO.getIsExcludedMaterial()) ? 1 : 0);
        vo.setIsNotExitMaterial(Boolean.TRUE.equals(entryDTO.getIsNotExitMaterial()) ? 1 : 0);
        vo.setIsMaterialRelationRepeat(Boolean.TRUE.equals(entryDTO.getIsMaterialRelationRepeat()) ? 1 : 0);
        vo.setIsMainMatNumCombined(Boolean.TRUE.equals(entryDTO.getIsMainMatNumCombined()) ? 1 : 0);
        return vo;
    }

    /**
     * 批量更新发货明细分录的结算信息并更新主表状态
     * 
     * 该方法执行三个主要操作：
     * 1. 批量更新所有分录的结算标志位信息
     * 2. 更新主表的物料不存在标志(wlbcz)
     * 3. 更新主表的结算状态
     * 
     * 整个过程在一个事务中执行，任何步骤失败都会导致回滚
     *
     * @param batchUpdateDTO 包含单据编号、分录更新信息和主表新状态的批量更新DTO
     * @return 所有更新操作是否全部成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateDeliveryDetailEntriesSettleInfo(BatchDeliveryDetailEntrySettleUpdateDTO batchUpdateDTO) {
        // 如果没有需要更新的分录，直接返回成功
        if (CollectionUtils.isEmpty(batchUpdateDTO.getEntries())) {
            return true;
        }
        
        // 将DTO转换为VO对象，准备批量更新分录信息
        List<CqDeliveryDetailEntrySettleInfoVO> entrySettleInfos = new ArrayList<>();
        for (DeliveryDetailEntrySettleUpdateDTO entryDTO : batchUpdateDTO.getEntries()) {
            CqDeliveryDetailEntrySettleInfoVO entrySettleInfo = convertToEntrySettleInfoVO(entryDTO);
            entrySettleInfos.add(entrySettleInfo);
        }
        
        // 调用现有方法批量更新所有分录的标志位信息
        boolean entriesUpdated = batchUpdateDeliveryDetailEntryFlags(batchUpdateDTO.getBillNo(), entrySettleInfos);
        
        // 判断是否存在"物料不存在"但不是"剔除物料"的情况
        // 注意：只有当物料不存在且不是被剔除的物料时，才标记主表的物料不存在标志为"1"
        boolean hasNotExistMaterial = batchUpdateDTO.getEntries().stream()
                .anyMatch(entry -> Boolean.TRUE.equals(entry.getIsNotExitMaterial()) && 
                        !Boolean.TRUE.equals(entry.getIsExcludedMaterial()));
        // 更新主表的物料不存在标志(wlbcz)："1"表示存在物料不存在情况，"0"表示不存在
        boolean wlbczUpdated = updateWlbczByBillNo(batchUpdateDTO.getBillNo(), hasNotExistMaterial ? "1" : "0");
        
        // 更新主表的结算状态为传入的目标状态，失败原因置空
        boolean statusUpdated = updateSettleStatusByBillNo(batchUpdateDTO.getBillNo(), batchUpdateDTO.getMainSettleStatus(), "");
        
        // 只有当所有三个更新操作都成功时，整个方法才返回true
        return entriesUpdated && wlbczUpdated && statusUpdated;
    }

    /**
     * 根据源明细分录的下游单号状态批量更新单据结算状态
     * 如果单据所有源明细分录的downstreamBillNo不为空，则更新settleStatus为5
     * 如果单据部分源明细分录的downstreamBillNo为空，则更新settleStatus为4
     *
     * @param billNos 单据编号列表
     * @return 包含每个单据处理结果的映射，key为单据编号，value为处理是否成功
     */
    @Override
    public Map<String, Boolean> batchUpdateSettleStatusByDownstreamBillNo(List<String> billNos) {
        // 参数校验
        if (!EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            log.warn("[batchUpdateSettleStatusByDownstreamBillNo][单据编号列表为空]");
            return new HashMap<>();
        }
        
        log.info("[batchUpdateSettleStatusByDownstreamBillNo][开始处理，单据数量: {}]", billNos.size());
        
        // 查询所有单据及其源明细分录
        List<CqDeliveryDetailWithSourceEntriesDTO> detailsWithSourceEntries = 
            getDeliveryDetailsWithSourceEntriesByBillNos(billNos);
        
        if (CollUtil.isEmpty(detailsWithSourceEntries)) {
            log.warn("[batchUpdateSettleStatusByDownstreamBillNo][未找到匹配的单据记录]");
            return new HashMap<>();
        }
        
        // 创建单据编号到新状态的映射
        Map<String, String> billNoToNewStatus = new HashMap<>();
        
        // 遍历所有单据，确定新状态
        for (CqDeliveryDetailWithSourceEntriesDTO dto : detailsWithSourceEntries) {
            CqDeliveryDetailDO deliveryDetail = dto.getDeliveryDetail();
            String billNo = deliveryDetail.getBillNo();
            List<CqSourceDeliveryDetailEntryDO> sourceEntries = dto.getSourceEntries();
            
            // 确定新的结算状态
            String newStatus = determineNewSettleStatus(sourceEntries);
            
            // 如果需要更新状态，添加到映射
            if (newStatus != null) {
                log.info("[batchUpdateSettleStatusByDownstreamBillNo][单据: {}, 当前状态: {}, 新状态: {}]", 
                        billNo, deliveryDetail.getSettleStatus(), newStatus);
                billNoToNewStatus.put(billNo, newStatus);
            }
        }
        
        // 批量更新不同状态的单据
        Map<String, Boolean> results = batchUpdateByStatusGroups(billNoToNewStatus);
        
        log.info("[batchUpdateSettleStatusByDownstreamBillNo][处理完成，总数: {}, 更新数: {}]", 
                billNos.size(), billNoToNewStatus.size());
        
        return results;
    }

    /**
     * 根据源明细分录列表确定新的结算状态
     *
     * @param sourceEntries 源明细分录列表
     * @return 新的结算状态，如果不需要更新则返回null
     */
    private String determineNewSettleStatus(List<CqSourceDeliveryDetailEntryDO> sourceEntries) {
        // 检查列表是否为空，为空则返回null（不需要更新）
        if (CollUtil.isEmpty(sourceEntries)) {
            return null;
        }
        
        // 检查是否所有分录的downstreamBillNo都不为空，是则返回"5"
        boolean allHaveDownstreamBillNo = true;
        boolean anyHaveDownstreamBillNo = false;
        
        for (CqSourceDeliveryDetailEntryDO entry : sourceEntries) {
            if (StrUtil.isBlank(entry.getDownstreamBillNo())) {
                allHaveDownstreamBillNo = false;
            } else {
                anyHaveDownstreamBillNo = true;
            }
        }
        
        // 如果所有分录都有下游单号，返回"5"（已完全下推）
        if (allHaveDownstreamBillNo) {
            return "5";
        }
        
        // 如果部分分录有下游单号，返回"4"（已部分下推）
        if (anyHaveDownstreamBillNo) {
            return "4";
        }
        
        // 如果没有分录有下游单号，不需要更新状态
        return null;
    }

    /**
     * 批量更新不同状态组的单据
     *
     * @param billNoToNewStatus 单据编号到新状态的映射
     * @return 处理结果映射
     */
    private Map<String, Boolean> batchUpdateByStatusGroups(Map<String, String> billNoToNewStatus) {
        Map<String, Boolean> results = new HashMap<>();
        if (CollUtil.isEmpty(billNoToNewStatus)) {
            return results;
        }
        
        // 按状态值对单据编号进行分组
        Map<String, List<String>> statusToBillNos = new HashMap<>();
        for (Map.Entry<String, String> entry : billNoToNewStatus.entrySet()) {
            String billNo = entry.getKey();
            String status = entry.getValue();
            
            statusToBillNos.computeIfAbsent(status, k -> new ArrayList<>())
                           .add(billNo);
        }
        
        // 对每个状态组进行批量更新
        for (Map.Entry<String, List<String>> entry : statusToBillNos.entrySet()) {
            String status = entry.getKey();
            List<String> billNosForStatus = entry.getValue();
            
            log.info("[batchUpdateByStatusGroups][开始更新状态组，状态: {}, 单据数量: {}]", 
                    status, billNosForStatus.size());
            
            // 逐个更新每个单据的状态，记录结果
            for (String billNo : billNosForStatus) {
                try {
                    boolean success = updateSettleStatusByBillNo(billNo, status, "");
                    results.put(billNo, success);
                    
                    if (!success) {
                        log.error("[batchUpdateByStatusGroups][更新单据状态失败，单据编号: {}, 状态: {}]", 
                                billNo, status);
                    }
                } catch (Exception e) {
                    log.error("[batchUpdateByStatusGroups][更新单据状态异常，单据编号: {}, 状态: {}]", 
                            billNo, status, e);
                    results.put(billNo, false);
                }
            }
        }
        
        return results;
    }

    /**
     * 根据明细ID列表获取发货明细表的数量和金额统计
     *
     * @param entryIds 明细ID列表
     * @return 包含totalQty(总数量)和totalAmount(总金额)的Map
     */
    @Override
    public Map<String, java.math.BigDecimal> getTotalsByEntryIds(List<Long> entryIds) {
        Map<String, java.math.BigDecimal> result = new HashMap<>();
        
        // 参数校验
        if (!EnhancedCollectionUtils.hasValidElements(entryIds)) {
            log.warn("[getTotalsByEntryIds][明细ID列表为空]");
            result.put("totalQty", java.math.BigDecimal.ZERO);
            result.put("totalAmount", java.math.BigDecimal.ZERO);
            return result;
        }

        try {
            // 查询源发货明细条目表，根据明细ID列表
            List<CqSourceDeliveryDetailEntryDO> entries = sourceDeliveryDetailEntryMapper.selectList(
                    new LambdaQueryWrapper<CqSourceDeliveryDetailEntryDO>()
                            .in(CqSourceDeliveryDetailEntryDO::getEntryId, entryIds)
            );

            if (CollUtil.isEmpty(entries)) {
                log.warn("[getTotalsByEntryIds][未找到匹配的源发货明细条目，明细ID列表数量={}]", entryIds.size());
                result.put("totalQty", java.math.BigDecimal.ZERO);
                result.put("totalAmount", java.math.BigDecimal.ZERO);
                return result;
            }

            // 计算总数量
            java.math.BigDecimal totalQty = entries.stream()
                    .filter(entry -> entry.getQuantity() != null)
                    .map(CqSourceDeliveryDetailEntryDO::getQuantity)
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

            // 计算总金额（含税金额）
            java.math.BigDecimal totalAmount = entries.stream()
                    .filter(entry -> entry.getTotalTaxAmount() != null)
                    .map(CqSourceDeliveryDetailEntryDO::getTotalTaxAmount)
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

            // 设置返回结果
            result.put("totalQty", totalQty);
            result.put("totalAmount", totalAmount);
            
            log.info("[getTotalsByEntryIds][统计完成，明细ID数量={}, 总数量={}, 总金额={}]", 
                    entryIds.size(), totalQty, totalAmount);
            
            return result;
        } catch (Exception e) {
            log.error("[getTotalsByEntryIds][统计异常，明细ID列表数量={}]", entryIds.size(), e);
            // 发生异常时返回零值
            result.put("totalQty", java.math.BigDecimal.ZERO);
            result.put("totalAmount", java.math.BigDecimal.ZERO);
            return result;
        }
    }
    


    /**
     * 执行原始查询方式
     */
    private List<CqDeliveryDetailRespVO> executeOriginalQuery(List<String> billNos, String settleStatus, 
                                                             List<String> shopNos, LocalDate startDate, LocalDate endDate) {
        // 构建查询条件
        LambdaQueryWrapperX<CqDeliveryDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        
        // 添加billNo条件（如果不为空）
        if (EnhancedCollectionUtils.hasValidStringElements(billNos)) {
            queryWrapper.in(CqDeliveryDetailDO::getBillNo, billNos);
        }
        
        // 添加结算状态条件（不能为空）
        if (StrUtil.isBlank(settleStatus)) {
            throw new IllegalArgumentException("结算状态不能为空");
        }
        queryWrapper.eq(CqDeliveryDetailDO::getSettleStatus, settleStatus);
        
        // 添加店铺编号条件（如果不为空）
        if (EnhancedCollectionUtils.hasValidStringElements(shopNos)) {
            queryWrapper.in(CqDeliveryDetailDO::getShopNo, shopNos);
        }
        
        // 添加发货日期范围条件
        if (startDate != null && endDate != null) {
            queryWrapper.between(CqDeliveryDetailDO::getDeliveryDate, 
                    startDate.atStartOfDay(), 
                    endDate.atTime(23, 59, 59));
        } else if (startDate != null) {
            queryWrapper.ge(CqDeliveryDetailDO::getDeliveryDate, startDate.atStartOfDay());
        } else if (endDate != null) {
            queryWrapper.le(CqDeliveryDetailDO::getDeliveryDate, endDate.atTime(23, 59, 59));
        }
        
        // 执行查询
        List<CqDeliveryDetailDO> deliveryDetails = deliveryDetailMapper.selectList(queryWrapper);
        
        return convertToRespVOList(deliveryDetails);
    }

} 
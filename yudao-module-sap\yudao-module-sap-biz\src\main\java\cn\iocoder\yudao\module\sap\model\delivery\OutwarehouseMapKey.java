package cn.iocoder.yudao.module.sap.model.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 委外品牌仓库映射关系查询组合键
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class OutwarehouseMapKey {
    
    /**
     * 默认物料组ID常量
     */
    public static final Long DEFAULT_MATGROUP_ID = 0L;
    
    /**
     * 默认产品类型常量
     */
    public static final String DEFAULT_PRODUCT_TYPE = "";
    
    /**
     * 股份共享仓ID
     */
    private Long shareWarehouseId;
    
    /**
     * 委外品牌ID
     */
    private Long brandId;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 物料组ID
     */
    private Long matgroupId;
    
    /**
     * 产品类型
     */
    private String mulProductType;
    
    /**
     * 三参数构造函数（向后兼容）
     * 
     * @param shareWarehouseId 股份共享仓ID
     * @param brandId 委外品牌ID
     * @param customerId 客户ID
     */
    public OutwarehouseMapKey(Long shareWarehouseId, Long brandId, Long customerId) {
        this.shareWarehouseId = shareWarehouseId;
        this.brandId = brandId;
        this.customerId = customerId;
        this.matgroupId = DEFAULT_MATGROUP_ID;
        this.mulProductType = DEFAULT_PRODUCT_TYPE;
    }
    
    /**
     * 创建三维组合键（向后兼容）
     * 
     * @param shareWarehouseId 股份共享仓ID
     * @param brandId 委外品牌ID
     * @param customerId 客户ID
     * @return 三维组合键
     */
    public static OutwarehouseMapKey of(Long shareWarehouseId, Long brandId, Long customerId) {
        return new OutwarehouseMapKey(shareWarehouseId, brandId, customerId);
    }
    
    /**
     * 创建完整的五维组合键
     * 
     * @param shareWarehouseId 股份共享仓ID
     * @param brandId 委外品牌ID
     * @param customerId 客户ID
     * @param matgroupId 物料组ID
     * @param mulProductType 产品类型
     * @return 完整的五维组合键
     */
    public static OutwarehouseMapKey of(Long shareWarehouseId, Long brandId, Long customerId, 
            Long matgroupId, String mulProductType) {
        return new OutwarehouseMapKey(shareWarehouseId, brandId, customerId, matgroupId, mulProductType);
    }
    
    @Override
    public String toString() {
        return String.format("OutwarehouseMapKey{shareWarehouseId=%d, brandId=%d, customerId=%d, matgroupId=%d, mulProductType='%s'}", 
                shareWarehouseId, brandId, customerId, matgroupId, mulProductType);
    }
} 
package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 批发通知单结算信息更新DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WholesaleNoticeBillSettleUpdateDTO {
    /**
     * 组织ID（销售组织）
     */
    private Long orgId = 0L;
    
    /**
     * 渠道组织不存在标志（销售组织）
     */
    private Boolean notExistChannelOrg = false;
    
    /**
     * 苍穹客户ID
     */
    private Long cqCustomerId = 0L;
    
    /**
     * 客户不存在标志
     */
    private Boolean notExistCustomer = false;
    
    /**
     * 是否按品牌分单
     */
    private Boolean isBrandSplitBill = false;
    
    /**
     * 剔除仓库标志
     */
    private Boolean excludeStock = false;
    
    // /**
    //  * 库存组织仓库不存在标志
    //  */
    // private Boolean notExistInvOrgStock = false;
    
    // /**
    //  * 苍穹库存组织仓库ID
    //  */
    // private Long cqInvOrgStockId = 0L;
    
    // /**
    //  * 库存组织ID
    //  */
    // private Long invOrgId = 0L;
    
    /**
     * 结算状态
     */
    private String settleStatus = "1";

    /**
     * 业务场景
     */
    private String ydBusinessscene = "";
} 
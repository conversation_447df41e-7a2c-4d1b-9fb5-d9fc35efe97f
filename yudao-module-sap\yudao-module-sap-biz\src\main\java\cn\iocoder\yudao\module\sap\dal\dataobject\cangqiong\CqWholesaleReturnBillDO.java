package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.time.LocalDateTime;

/**
 * 批发退货单 DO
 */
@TableName("tk_yd_wholesalereturnbill")
@KeySequence("tk_yd_wholesalereturnbill_seq") 
@Data
@ToString(callSuper = true)
public class CqWholesaleReturnBillDO {

    /**
     * ID
     */
    @TableId("FId")
    private Long id;

    /**
     * 单据编号
     */
    @TableField("fbillno")
    private String billNo;

    /**
     * 单据状态，枚举: A:暂存 B:已提交 C:已审核
     */
    @TableField("fbillstatus")
    private String billStatus;

    /**
     * 创建人ID
     */
    @TableField("fcreatorid")
    private Long creatorId;

    /**
     * 审核人ID
     */
    @TableField("fauditorid")
    private Long auditorId;

    /**
     * 审核日期
     */
    @TableField("fauditdate")
    private Date auditDate;

    /**
     * 平台
     */
    @TableField("fk_yd_platform")
    private String platform;

    /**
     * 渠道编码
     */
    @TableField("fk_yd_channelno")
    private String channelNo;

    /**
     * 客户编码
     */
    @TableField("fk_yd_customerno")
    private String customerNo;

    /**
     * 仓库编码
     */
    @TableField("fk_yd_warehouseno")
    private String warehouseNo;

    /**
     * 发货类型名称
     */
    @TableField("fk_yd_shippingtype")
    private String shippingType;

    /**
     * 退货日期/业务日期
     */
    @TableField("fk_yd_returndate")
    private Date bizDate;

    /**
     * 结算状态 1 主表待结算 2 OMS明细待结算 3 拆单明细待结算 4 结算完成
     */
    @TableField("fk_yd_settleStatus")
    private String settleStatus;

    /**
     * 业务场景
     */
    @TableField("fk_yd_businessscene")
    private String ydBusinessscene;

    /**
     * 通知单号
     */
    @TableField("fk_yd_noticebillno")
    private String noticeBillNo;

    /**
     * 下游单号
     */
    @TableField("fk_yd_targetbillno")
    private String targetBillNo;

    /**
     * SAP单号
     */
    @TableField("fk_yd_sap_billlno")
    private String sapBillNo;

    /**
     * 销售组织ID
     */
    @TableField("fk_yd_orgid")
    private Long orgId;

    /**
     * 苍穹客户ID
     */
    @TableField("fk_yd_cqcustomerid")
    private Long cqCustomerId;

    /**
     * 是否按品牌分单
     */
    @TableField("fk_yd_isbrandsplitbill")
    private Boolean isBrandSplitBill;

    /**
     * 是否渠道组织不存在
     */
    @TableField("fk_yd_notexistchannelorg")
    private Boolean notExistChannelOrg;

    /**
     * 是否客户不存在
     */
    @TableField("fk_yd_notexistcustomer")
    private Boolean notExistCustomer;

    /**
     * 是否剔除仓库
     */
    @TableField("fk_yd_excludestock")
    private Boolean excludeStock;

    /**
     * 是否库存组织仓库不存在
     */
    @TableField("fk_yd_notexistinvorgstock")
    private Boolean notExistInvOrgStock;
    
    /**
     * 是否销售组织仓库不存在
     */
    @TableField("fk_yd_notexistsalorgstock")
    private Boolean notExistSalOrgStock;

    /**
     * 是否物料不存在
     */
    @TableField("fk_yd_notexistmaterial")
    private Boolean notExistMaterial;

    /**
     * 是否剔除物料
     */
    @TableField("fk_yd_excludematerial")
    private Boolean excludeMaterial;

    /**
     * 是否物料关系重复
     */
    @TableField("fk_yd_matrepeat")
    private Boolean matRepeat;

    /**
     * 修改时间
     */
    @TableField("fmodifytime")
    private LocalDateTime modifyTime;

    /**
     * E3创建人
     */
    @TableField("fk_yd_e3creator")
    private String e3Creator;
    
    /**
     * 备注
     */
    @TableField("fk_yd_description")
    private String remark;

    /**
     * 是否库存组织不存在
     */
    @TableField("fk_yd_invorgnotexist")
    private Boolean invOrgNotExist;

}
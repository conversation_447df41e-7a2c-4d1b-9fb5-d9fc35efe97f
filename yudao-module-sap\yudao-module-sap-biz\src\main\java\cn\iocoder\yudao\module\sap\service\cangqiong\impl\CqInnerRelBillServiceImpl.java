package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqInnerRelBillDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqInnerRelEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMulinnerrelbrandDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqMulinnerrelmatgroupDO;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqInnerRelBillMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqInnerRelEntryMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqMulinnerrelbrandMapper;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.CqMulinnerrelmatgroupMapper;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqInnerRelBillService;
import cn.iocoder.yudao.module.sap.model.delivery.InnerRelKey;
import cn.iocoder.yudao.module.sap.model.delivery.InnerRelCombinedInfo;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Collections;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import java.util.ArrayList;
import java.util.stream.Collectors;


/**
 * 苍穹内部交易关系表 Service 实现类
 * 
 * 该类负责处理内部交易关系单据的所有业务逻辑，包括：
 * 1. 单据的创建、修改、删除和查询
 * 2. 单据的审核流程
 */
@Service
@Validated
@Slf4j
@DS("cq_scm")
public class CqInnerRelBillServiceImpl implements CqInnerRelBillService {

    /**
     * 品牌和物料组映射关系数据结构
     */
    private static class BrandMatgroupMaps {
        private final Map<Long, List<Long>> billToBrandMap;
        private final Map<Long, List<Long>> billToMatgroupMap;
        
        public BrandMatgroupMaps(Map<Long, List<Long>> billToBrandMap, Map<Long, List<Long>> billToMatgroupMap) {
            this.billToBrandMap = billToBrandMap != null ? billToBrandMap : new HashMap<>();
            this.billToMatgroupMap = billToMatgroupMap != null ? billToMatgroupMap : new HashMap<>();
        }
        
        public List<Long> getBrandIds(Long billId) {
            List<Long> brandIds = billToBrandMap.get(billId);
            return CollectionUtils.isNotEmpty(brandIds) ? brandIds : 
                Collections.singletonList(0L);
        }
        
        public List<Long> getMatgroupIds(Long billId) {
            List<Long> matgroupIds = billToMatgroupMap.get(billId);
            return CollectionUtils.isNotEmpty(matgroupIds) ? matgroupIds : 
                Collections.singletonList(0L);
        }
    }

    @Resource
    private CqInnerRelBillMapper cqInnerRelBillMapper;
    
    @Resource
    private CqInnerRelEntryMapper cqInnerRelEntryMapper;
    
    @Resource
    private CqMulinnerrelbrandMapper cqMulinnerrelbrandMapper;
    
    @Resource
    private CqMulinnerrelmatgroupMapper cqMulinnerrelmatgroupMapper;

    /**
     * 根据ID获取内部交易关系单据
     * 
     * @param id 单据ID
     * @return 内部交易关系单据主表数据
     */
    @Override
    public CqInnerRelBillDO getInnerRelBill(Long id) {
        return cqInnerRelBillMapper.selectById(id);
    }
    
    /**
     * 根据单据编号获取内部交易关系单据
     * 
     * @param billNo 单据编号
     * @return 内部交易关系单据主表数据
     */
    @Override
    public CqInnerRelBillDO getInnerRelBillByBillNo(String billNo) {
        return cqInnerRelBillMapper.selectByBillNo(billNo);
    }

    /**
     * 批量获取内部交易关系单据列表
     * 
     * @param ids 单据ID集合
     * @return 内部交易关系单据主表数据列表
     */
    @Override
    public List<CqInnerRelBillDO> getInnerRelBillList(Collection<Long> ids) {
        return cqInnerRelBillMapper.selectBatchIds(ids);
    }

    /**
     * 获取指定主表ID的明细列表
     * 
     * @param mainId 主表ID
     * @return 内部交易关系单据明细数据列表
     */
    @Override
    public List<CqInnerRelEntryDO> getInnerRelEntryList(Long mainId) {
        return cqInnerRelEntryMapper.selectListByMainId(mainId);
    }
    
    /**
     * 根据原客户ID和新客户ID获取有效的内部交易关系单
     * 
     * @param oriCustomerId 原客户ID
     * @param newCustomerId 新客户ID
     * @return 内部交易关系单
     */
    @Override
    public CqInnerRelBillDO getValidInnerRelBill(Long oriCustomerId, Long newCustomerId) {
        return cqInnerRelBillMapper.selectByCustomerIds(oriCustomerId, newCustomerId);
    }

    /**
     * 根据品牌ID查询内部交易关系单据列表
     * 
     * @param brandId 品牌ID
     * @return 内部交易关系单据列表
     */
    @Override
    public List<CqInnerRelBillDO> getInnerRelBillListByBrandId(Long brandId) {
        return cqInnerRelBillMapper.selectByBrandId(brandId);
    }
    
    /**
     * 根据原客户ID和品牌ID查询内部交易关系单据列表
     * 
     * @param oriCustomerId 原客户ID
     * @param brandId 品牌ID
     * @return 内部交易关系单据列表
     */
    @Override
    public List<CqInnerRelBillDO> getInnerRelBillListByOriCustomerIdAndBrandId(Long oriCustomerId, Long brandId) {
        LambdaQueryWrapper<CqInnerRelBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CqInnerRelBillDO::getOriCustomerId, oriCustomerId)
               .eq(CqInnerRelBillDO::getBrandId, brandId)
               .eq(CqInnerRelBillDO::getBillStatus, "C"); // C表示已审核
        return cqInnerRelBillMapper.selectList(wrapper);
    }
    
    /**
     * 根据原客户ID和品牌ID查询内部交易关系分录列表
     * 
     * @param oriCustomerId 原客户ID
     * @param brandId 品牌ID
     * @return 内部交易关系分录列表
     */
    @Override
    public List<CqInnerRelEntryDO> getInnerRelEntryListByOriCustomerIdAndBrandId(Long oriCustomerId, Long brandId) {
        // 参数校验
        if (oriCustomerId == null || brandId == null) {
            log.debug("参数为空，oriCustomerId: {}, brandId: {}", oriCustomerId, brandId);
            return new ArrayList<>();
        }
        
        // 使用子查询一次性获取分录列表，避免两次查询
        // 子查询：从主表中查询符合条件的单据ID
        String subSql = String.format(
            "SELECT FId FROM tk_yd_innerrelbill WHERE fk_yd_oricustomerid = %d AND fk_yd_brandid = %d AND fbillstatus = 'C'",
            oriCustomerId, brandId
        );
        
        LambdaQueryWrapper<CqInnerRelEntryDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.inSql(CqInnerRelEntryDO::getId, subSql);
        
        List<CqInnerRelEntryDO> result = cqInnerRelEntryMapper.selectList(wrapper);
        
        log.debug("根据原客户ID[{}]和品牌ID[{}]查询内部交易关系分录，共查询到{}条记录", 
                  oriCustomerId, brandId, result.size());
        
        return result;
    }
    
    /**
     * 根据组织ID查询内部交易关系分录列表
     * 
     * @param orgId 组织ID
     * @return 内部交易关系分录列表
     */
    @Override
    public List<CqInnerRelEntryDO> getInnerRelEntryListByOrgId(Long orgId) {
        return cqInnerRelEntryMapper.selectListByOrgId(orgId);
    }
    
    /**
     * 根据组织级次查询内部交易关系分录列表
     * 
     * @param orgLevel 组织级次
     * @return 内部交易关系分录列表
     */
    @Override
    public List<CqInnerRelEntryDO> getInnerRelEntryListByOrgLevel(String orgLevel) {
        return cqInnerRelEntryMapper.selectListByOrgLevel(orgLevel);
    }

    /**
     * 批量获取所有内部交易关系组合数据
     * 用于性能优化，一次性查询所有数据并构建缓存
     * 
     * 主要功能：
     * 1. 查询所有已审核的内部交易关系主表记录
     * 2. 根据主表ID查询对应的分录记录
     * 3. 关联品牌和物料组信息
     * 4. 构建四维组合键映射关系
     * 5. 处理重复的组合键，支持一对多关系
     * 
     * 性能优化说明：
     * - 使用批量查询避免N+1问题
     * - 预构建映射关系减少重复查询
     * - 支持产品类型拆分，将逗号分隔的值展开为独立条目
     * 
     * 四维组合键说明：
     * - 原客户ID (oriCustomerId)
     * - 品牌ID (brandId)
     * - 产品类型 (mulProductType)
     * - 物料组ID (matgroupId)
     * 
     * @return 以四维组合键为key，内部交易关系组合信息为value的映射
     */
    @Override
    public Map<InnerRelKey, InnerRelCombinedInfo> getAllInnerRelCombinedData() {
        log.info("[getAllInnerRelCombinedData][开始批量查询所有内部交易关系]");
        long startTime = System.currentTimeMillis();
        
        // 第一步：查询所有已审核状态的内部交易关系主表数据
        LambdaQueryWrapper<CqInnerRelBillDO> billWrapper = new LambdaQueryWrapper<>();
        billWrapper.eq(CqInnerRelBillDO::getBillStatus, "C"); // C表示已审核
        List<CqInnerRelBillDO> allBills = cqInnerRelBillMapper.selectList(billWrapper);
        
        long billQueryTime = System.currentTimeMillis();
        log.info("[getAllInnerRelCombinedData][查询到 {} 条内部交易关系主表记录，耗时: {}ms]", 
                allBills.size(), billQueryTime - startTime);
        
        // 如果没有已审核的主表记录，直接返回空映射
        if (CollectionUtils.isEmpty(allBills)) {
            return new HashMap<>();
        }
        
        // 第二步：根据主表ID列表查询所有分录记录
        List<Long> billIds = allBills.stream()
                .map(CqInnerRelBillDO::getId)
                .collect(Collectors.toList());
        
        List<CqInnerRelEntryDO> allEntries = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(billIds)) {
            LambdaQueryWrapper<CqInnerRelEntryDO> entryWrapper = new LambdaQueryWrapper<>();
            entryWrapper.in(CqInnerRelEntryDO::getId, billIds);
            allEntries = cqInnerRelEntryMapper.selectList(entryWrapper);
        }
        
        long entryQueryTime = System.currentTimeMillis();
        log.info("[getAllInnerRelCombinedData][查询到 {} 条内部交易关系分录记录，耗时: {}ms]", 
                allEntries.size(), entryQueryTime - billQueryTime);
        
        // 第三步：批量构建品牌和物料组映射关系，避免在循环中重复查询数据库
        BrandMatgroupMaps brandMatgroupMaps = buildBrandAndMatgroupMaps(allBills);
        long mappingTime = System.currentTimeMillis();
        log.info("[getAllInnerRelCombinedData][品牌和物料组映射构建耗时: {}ms]", mappingTime - entryQueryTime);
        
        // 第四步：按单据ID分组分录数据
        Map<Long, List<CqInnerRelEntryDO>> entriesGroupedByBillId = allEntries.stream()
                .collect(Collectors.groupingBy(CqInnerRelEntryDO::getId));
        
        // 第五步：按四维组合键分组并处理产品类型拆分
        Map<InnerRelKey, InnerRelCombinedInfo> resultMap = new HashMap<>();
        int originalBillCount = allBills.size(); // 记录原始单据数量
        int splitEntryCount = 0; // 记录拆分后的条目数量
        
        // 遍历所有主表记录，为每个记录生成所有可能的四维组合
        for (CqInnerRelBillDO bill : allBills) {
            // 拆分产品类型字符串，支持逗号分隔的多个产品类型
            List<String> productTypes = splitProductTypes(bill.getMulProductType());
            
            // 从预构建的映射关系中获取品牌和物料组信息，避免重复查询数据库
            List<Long> brandIds = brandMatgroupMaps.getBrandIds(bill.getId());
            List<Long> matgroupIds = brandMatgroupMaps.getMatgroupIds(bill.getId());
            
            // 获取当前单据对应的分录列表，如果没有分录则使用空列表
            List<CqInnerRelEntryDO> relatedEntries = entriesGroupedByBillId.getOrDefault(bill.getId(), new ArrayList<>());
            
                         // 生成所有可能的四维组合：原客户ID × 品牌ID × 产品类型 × 物料组ID
             for (String productType : productTypes) {
                 for (Long brandId : brandIds) {
                     for (Long matgroupId : matgroupIds) {
                         splitEntryCount++; // 统计拆分后的条目数量
                         
                         // 标准化产品类型，确保数据一致性
                         String sanitizedProductType = sanitizeProductType(productType);
                         
                         // 为每个组合创建独立的单据对象副本，避免对象状态污染
                         CqInnerRelBillDO billCopy = createBillWithExtendedInfo(bill, brandId, matgroupId, sanitizedProductType);
                         
                         // 构建四维组合键
                         InnerRelKey key = new InnerRelKey(bill.getOriCustomerId(), brandId, sanitizedProductType, matgroupId);
                         
                         // 创建组合信息对象，包含单据主表信息和对应的分录列表
                         InnerRelCombinedInfo combinedInfo = new InnerRelCombinedInfo(billCopy, relatedEntries);
                         
                         // 检查是否存在重复的组合键，支持一对多关系
                         if (resultMap.containsKey(key)) {
                             // 发现重复组合键时记录警告日志
                             log.warn("[getAllInnerRelCombinedData][发现重复的四维组合键: 原客户ID:{}, 品牌ID:{}, 产品类型:{}, 物料组ID:{}]", 
                                 bill.getOriCustomerId(), brandId, sanitizedProductType, matgroupId);
                         } else {
                             resultMap.put(key, combinedInfo);
                         }
                     }
                 }
             }
        }
        
        // 计算总耗时并记录统计信息
        long totalTime = System.currentTimeMillis() - startTime;
        log.info("[getAllInnerRelCombinedData][批量查询完成，原始单据: {}, 拆分后条目: {}, 最终组合键: {}, 总耗时: {}ms]", 
            originalBillCount, splitEntryCount, resultMap.size(), totalTime);
        
        return resultMap;
    }

    /**
     * 批量构建品牌和物料组映射关系
     * 
     * 主要功能：
     * 1. 批量查询所有单据的品牌和物料组信息
     * 2. 构建billId到品牌ID列表和物料组ID列表的映射关系
     * 3. 优化性能，避免在循环中重复查询数据库
     * 
     * @param allBills 单据列表
     * @return 包含品牌和物料组映射关系的数据结构
     */
    private BrandMatgroupMaps buildBrandAndMatgroupMaps(List<CqInnerRelBillDO> allBills) {
        if (CollectionUtils.isEmpty(allBills)) {
            return new BrandMatgroupMaps(new HashMap<>(), new HashMap<>());
        }
        
        // 收集所有的billId
        List<Long> billIds = allBills.stream()
                .map(CqInnerRelBillDO::getId)
                .collect(Collectors.toList());
        
        // 批量查询品牌信息
        List<CqMulinnerrelbrandDO> brandList = cqMulinnerrelbrandMapper.selectByIds(billIds);
        Map<Long, List<Long>> billToBrandMap = brandList.stream()
                .collect(Collectors.groupingBy(
                        CqMulinnerrelbrandDO::getId,
                        Collectors.mapping(CqMulinnerrelbrandDO::getBrandId, Collectors.toList())
                ));
        
        // 批量查询物料组信息
        List<CqMulinnerrelmatgroupDO> matgroupList = cqMulinnerrelmatgroupMapper.selectByIds(billIds);
        Map<Long, List<Long>> billToMatgroupMap = matgroupList.stream()
                .collect(Collectors.groupingBy(
                        CqMulinnerrelmatgroupDO::getId,
                        Collectors.mapping(CqMulinnerrelmatgroupDO::getMatgroupId, Collectors.toList())
                ));
        
        log.info("[buildBrandAndMatgroupMaps][批量查询完成，品牌记录: {}, 物料组记录: {}, 单据数: {}]", 
            brandList.size(), matgroupList.size(), allBills.size());
        
        return new BrandMatgroupMaps(billToBrandMap, billToMatgroupMap);
    }

    /**
     * 拆分产品类型字符串
     * 
     * @param mulProductType 多产品类型字符串，可能包含逗号分隔的值
     * @return 产品类型列表
     */
    private List<String> splitProductTypes(String mulProductType) {
        if (mulProductType == null || mulProductType.trim().isEmpty()) {
            return Collections.singletonList("");
        }
        
        // 按逗号分割并去除空白字符
        String[] types = mulProductType.split(",");
        List<String> result = new ArrayList<>();
        
        for (String type : types) {
            String trimmed = type.trim();
            if (!trimmed.isEmpty()) {
                result.add(trimmed);
            }
        }
        
        // 如果分割后没有有效的产品类型，返回默认类型
        return result.isEmpty() ? 
            Collections.singletonList("") : result;
    }

    /**
     * 标准化产品类型
     * 
     * @param productType 原始产品类型
     * @return 标准化后的产品类型
     */
    private String sanitizeProductType(String productType) {
        if (productType == null || productType.trim().isEmpty()) {
            return "";
        }
        return productType.trim();
    }

    /**
     * 为指定品牌、物料组和产品类型创建 CqInnerRelBillDO 副本
     * 
     * @param originalBill 原始单据
     * @param brandId 品牌ID
     * @param matgroupId 物料组ID
     * @param productType 产品类型
     * @return 新的单据副本
     */
    private CqInnerRelBillDO createBillWithExtendedInfo(CqInnerRelBillDO originalBill, 
            Long brandId, Long matgroupId, String productType) {
        CqInnerRelBillDO billCopy = new CqInnerRelBillDO();
        
        // 复制原始单据的所有基础字段
        billCopy.setId(originalBill.getId());
        billCopy.setBillNo(originalBill.getBillNo());
        billCopy.setBillStatus(originalBill.getBillStatus());
        billCopy.setCreatorId(originalBill.getCreatorId());
        billCopy.setModifierId(originalBill.getModifierId());
        billCopy.setAuditorId(originalBill.getAuditorId());
        billCopy.setAuditDate(originalBill.getAuditDate());
        billCopy.setModifyTime(originalBill.getModifyTime());
        billCopy.setCreateTime(originalBill.getCreateTime());
        billCopy.setOriCustomerId(originalBill.getOriCustomerId());
        billCopy.setNewCustomerId(originalBill.getNewCustomerId());
        billCopy.setSyncFromYxy(originalBill.getSyncFromYxy());
        billCopy.setIsOutSettle(originalBill.getIsOutSettle());
        
        // 设置扩展信息
        billCopy.setBrandId(brandId);
        billCopy.setMatgroupId(matgroupId);
        billCopy.setMulProductType(productType);
        
        return billCopy;
    }
} 
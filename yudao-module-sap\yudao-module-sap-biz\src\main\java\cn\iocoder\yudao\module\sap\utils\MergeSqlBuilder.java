package cn.iocoder.yudao.module.sap.utils;

import java.util.List;
import java.util.StringJoiner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 合单SQL构建工具类
 * <p>
 * 该工具类根据单据编号列表，动态生成发货明细与拆单明细的联合查询SQL。
 * 字段注释参考实体：
 * - CqDeliveryDetailDO（发货明细 tk_yd_fhmx）
 * - CqSourceDeliveryDetailEntryDO（拆单明细 tk_yd_src_fhmxentry）
 */
public final class MergeSqlBuilder {

    private static final Logger log = LoggerFactory.getLogger(MergeSqlBuilder.class);
    
    /** 拆单明细表前缀 */
    private static final String ENTRY_PREFIX = "tk_yd_src_fhmxentry_";
    /** 发货明细表前缀 */
    private static final String DETAIL_PREFIX = "tk_yd_fhmx_";
    /** 拆单明细表别名 */
    private static final String ENTRY_ALIAS = "e";
    /** 发货明细表别名 */
    private static final String DETAIL_ALIAS = "d";
    /** 发货日期字段全名 */
    private static final String SHIP_DATE_FIELD = DETAIL_ALIAS + ".fk_yd_datefield_fhrq";

    private MergeSqlBuilder() {}

    /**
     * 构建合单SQL
     * @param mergeRuleScopeSql 合单规则范围sql脚本
     * @return SQL字符串
     */
    public static String buildMergeSql(String mergeRuleScopeSql) {
        StringBuilder sqlBuilder = new StringBuilder();

        sqlBuilder.append("SELECT ");

        // 添加基础信息字段（主键、单据编号等）
        sqlBuilder.append(BASE_FIELDS);
        // 添加物料相关字段（物料ID、批号、生产日期等）
        sqlBuilder.append(MATERIAL_FIELDS);
        // 添加金额相关字段（数量、单价、金额等）
        sqlBuilder.append(QUANTITY_AMOUNT_FIELDS);
        // 添加组织仓库字段（销售组织、库存组织等）
        sqlBuilder.append(ORGANIZATION_FIELDS);
        // 添加状态相关字段（单据状态、审核状态等）
        sqlBuilder.append(STATUS_FIELDS);
        // 添加订单相关字段（订单号、订单类型等）
        sqlBuilder.append(ORDER_FIELDS);
        // 添加地址相关字段（收货地址、联系人等）
        sqlBuilder.append(ADDRESS_FIELDS);
        // 添加日期相关字段（创建日期、审核日期等）
        sqlBuilder.append(DATE_FIELDS);
        // 添加其他辅助字段（备注、标签等）
        sqlBuilder.append(OTHER_FIELDS);

        // 构建FROM子句，从拆单明细表(tk_yd_src_fhmxentry)开始查询
        sqlBuilder.append("\nFROM tk_yd_src_fhmxentry ").append(ENTRY_ALIAS).append(" ");
        // 左连接发货明细表(tk_yd_fhmx)，通过主键ID(fid)关联
        sqlBuilder.append("\nLEFT JOIN tk_yd_fhmx ").append(DETAIL_ALIAS).append(" ON ").append(ENTRY_ALIAS).append(".fid = ").append(DETAIL_ALIAS).append(".fid ");
        // 添加WHERE条件，1=1是为了方便后续添加AND条件
        sqlBuilder.append("\nWHERE 1 = 1 AND (");
        // 添加合单规则的范围SQL，例如：d.fbillno IN ('DD2023001', 'DD2023002') AND d.fbillstatus = 'C'
        sqlBuilder.append(mergeRuleScopeSql);
        // 结束WHERE子句
        sqlBuilder.append(")");

        String totalSql = sqlBuilder.toString();
        log.info("生成的合单SQL: {}", totalSql);
        return totalSql;
    }

    /**
     * 构建按天去重的日期查询
     * @param mergeRuleScopeSql 合单规则范围sql脚本
     * @return SQL字符串
     * 
     * SQL示例:
     * SELECT DISTINCT DATE(d.fk_yd_datefield_fhrq) AS ship_date 
     * FROM tk_yd_src_fhmxentry e 
     * LEFT JOIN tk_yd_fhmx d ON e.fid = d.fid 
     * WHERE 1 = 1 AND (d.fbillno IN ('DD2023001', 'DD2023002') AND d.fbillstatus = 'C') 
     *   AND d.fk_yd_datefield_fhrq IS NOT NULL
     *   AND NOT EXISTS (
     *     SELECT 1 FROM tk_yd_fhmxdhqtck t1
     *     WHERE t1.fk_yd_textfield_fhmxdh = d.fbillno
     *       AND t1.fk_yd_rowno = e.fseq
     *       AND t1.fk_yd_rowid = e.fentryid
     *   )
     *   AND NOT EXISTS (
     *     SELECT 1 FROM tk_yd_fhmxdh t2
     *     WHERE t2.fk_yd_textfield_fhmxdh = d.fbillno
     *       AND t2.fk_yd_rowno = e.fseq
     *       AND t2.fk_yd_rowid = e.fentryid
     *   )
     * ORDER BY DATE(d.fk_yd_datefield_fhrq)
     * 
     * 索引优化建议:
     * 1. 主表索引 (tk_yd_fhmx):
     *    CREATE INDEX idx_fhmx_billno_status_date ON tk_yd_fhmx(fbillno, fbillstatus, fk_yd_datefield_fhrq);
     *    CREATE INDEX idx_fhmx_fid ON tk_yd_fhmx(fid);
     *    CREATE INDEX idx_fhmx_date_only ON tk_yd_fhmx(DATE(fk_yd_datefield_fhrq));
     * 
     * 2. 明细表索引 (tk_yd_src_fhmxentry):
     *    CREATE INDEX idx_fhmxentry_fid_seq_entryid ON tk_yd_src_fhmxentry(fid, fseq, fentryid);
     * 
     * 3. 其他出库表索引 (tk_yd_fhmxdhqtck):
     *    CREATE INDEX idx_fhmxdhqtck_composite ON tk_yd_fhmxdhqtck(fk_yd_textfield_fhmxdh, fk_yd_rowno, fk_yd_rowid);
     * 
     * 4. 发货明细对照表索引 (tk_yd_fhmxdh):
     *    CREATE INDEX idx_fhmxdh_composite ON tk_yd_fhmxdh(fk_yd_textfield_fhmxdh, fk_yd_rowno, fk_yd_rowid);
     * 
     * 5. 覆盖索引优化 (减少回表查询):
     *    CREATE INDEX idx_fhmx_covering ON tk_yd_fhmx(fbillno, fbillstatus, fk_yd_datefield_fhrq, fid);
     */
    public static String buildDateDistinctQuery(String mergeRuleScopeSql) {
        StringBuilder sqlBuilder = new StringBuilder();
        // 使用 DATE() 函数提取日期部分，去除时间部分
        sqlBuilder.append("SELECT DISTINCT DATE(").append(SHIP_DATE_FIELD).append(") AS ship_date ");
        sqlBuilder.append("\nFROM tk_yd_src_fhmxentry ").append(ENTRY_ALIAS).append(" ");
        sqlBuilder.append("\nLEFT JOIN tk_yd_fhmx ").append(DETAIL_ALIAS).append(" ON ").append(ENTRY_ALIAS).append(".fid = ").append(DETAIL_ALIAS).append(".fid ");
        sqlBuilder.append("\nWHERE 1 = 1 AND (");
        sqlBuilder.append(mergeRuleScopeSql);
        sqlBuilder.append(") AND ").append(SHIP_DATE_FIELD).append(" IS NOT NULL");
        sqlBuilder.append("\n  AND NOT EXISTS (");
        sqlBuilder.append("\n    SELECT 1 ");
        sqlBuilder.append("\n    FROM tk_yd_fhmxdhqtck t1");
        sqlBuilder.append("\n    WHERE t1.fk_yd_textfield_fhmxdh COLLATE utf8mb4_unicode_ci = ").append(DETAIL_ALIAS).append(".fbillno COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t1.fk_yd_rowid COLLATE utf8mb4_unicode_ci = CAST(").append(ENTRY_ALIAS).append(".fentryid AS CHAR) COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t1.fk_yd_textfield_fhmxdh IS NOT NULL ");
        sqlBuilder.append("\n      AND t1.fk_yd_textfield_fhmxdh != ''");
        sqlBuilder.append("\n      AND t1.fk_yd_rowid IS NOT NULL ");
        sqlBuilder.append("\n      AND t1.fk_yd_rowid != ''");
        sqlBuilder.append("\n  )");
        sqlBuilder.append("\n  AND NOT EXISTS (");
        sqlBuilder.append("\n    SELECT 1 ");
        sqlBuilder.append("\n    FROM tk_yd_fhmxdh t2");
        sqlBuilder.append("\n    WHERE t2.fk_yd_textfield_fhmxdh COLLATE utf8mb4_unicode_ci = ").append(DETAIL_ALIAS).append(".fbillno COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t2.fk_yd_rowid COLLATE utf8mb4_unicode_ci = CAST(").append(ENTRY_ALIAS).append(".fentryid AS CHAR) COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t2.fk_yd_textfield_fhmxdh IS NOT NULL ");
        sqlBuilder.append("\n      AND t2.fk_yd_textfield_fhmxdh != ''");
        sqlBuilder.append("\n      AND t2.fk_yd_rowid IS NOT NULL ");
        sqlBuilder.append("\n      AND t2.fk_yd_rowid != ''");
        sqlBuilder.append("\n  )");
        sqlBuilder.append("\nORDER BY DATE(").append(SHIP_DATE_FIELD).append(")");
        
        String sql = sqlBuilder.toString();
        log.info("生成的日期去重SQL: {}", sql);
        return sql;
    }

    /**
     * 构建带日期过滤的合单SQL
     * @param mergeRuleScopeSql 合单规则范围sql脚本
     * @param shipDate 发货日期，格式为 yyyy-MM-dd
     * @return SQL字符串
     * 
     * SQL示例:
     * SELECT 
     *   e.fentryid AS entryId,
     *   d.fbillno AS billNo,
     *   e.fk_yd_materialid AS materialId,
     *   e.fk_yd_qty AS qty,
     *   e.fk_yd_totaltaxamount AS totalTaxAmount,
     *   d.fk_yd_datefield_fhrq AS shipDate,
     *   e.fk_yd_customerid AS customerId,
     *   e.fk_yd_stockid AS stockId
     * FROM tk_yd_src_fhmxentry e 
     * LEFT JOIN tk_yd_fhmx d ON e.fid = d.fid 
     * WHERE 1 = 1 AND (d.fbillno IN ('DD2023001', 'DD2023002') AND d.fbillstatus = 'C') 
     *   AND DATE(d.fk_yd_datefield_fhrq) = '2023-12-01'
     */
    public static String buildMergeSqlWithDateFilter(String mergeRuleScopeSql, String shipDate) {
        StringBuilder sqlBuilder = new StringBuilder();

        sqlBuilder.append("SELECT ");

        // 添加基础信息字段（主键、单据编号等）
        sqlBuilder.append(BASE_FIELDS);
        // 添加物料相关字段（物料ID、批号、生产日期等）
        sqlBuilder.append(MATERIAL_FIELDS);
        // 添加金额相关字段（数量、单价、金额等）
        sqlBuilder.append(QUANTITY_AMOUNT_FIELDS);
        // 添加组织仓库字段（销售组织、库存组织等）
        sqlBuilder.append(ORGANIZATION_FIELDS);
        // 添加状态相关字段（单据状态、审核状态等）
        sqlBuilder.append(STATUS_FIELDS);
        // 添加订单相关字段（订单号、订单类型等）
        sqlBuilder.append(ORDER_FIELDS);
        // 添加地址相关字段（收货地址、联系人等）
        sqlBuilder.append(ADDRESS_FIELDS);
        // 添加日期相关字段（创建日期、审核日期等）
        sqlBuilder.append(DATE_FIELDS);
        // 添加其他辅助字段（备注、标签等）
        sqlBuilder.append(OTHER_FIELDS);

        // 构建FROM子句，从拆单明细表(tk_yd_src_fhmxentry)开始查询
        sqlBuilder.append("\nFROM tk_yd_src_fhmxentry ").append(ENTRY_ALIAS).append(" ");
        // 左连接发货明细表(tk_yd_fhmx)，通过主键ID(fid)关联
        sqlBuilder.append("\nLEFT JOIN tk_yd_fhmx ").append(DETAIL_ALIAS).append(" ON ").append(ENTRY_ALIAS).append(".fid = ").append(DETAIL_ALIAS).append(".fid ");
        // 添加WHERE条件，1=1是为了方便后续添加AND条件
        sqlBuilder.append("\nWHERE 1 = 1 AND (");
        // 添加合单规则的范围SQL，例如：d.fbillno IN ('DD2023001', 'DD2023002') AND d.fbillstatus = 'C'
        sqlBuilder.append(mergeRuleScopeSql);
        // 添加日期过滤条件
        sqlBuilder.append(") AND DATE(").append(SHIP_DATE_FIELD).append(") = '").append(shipDate).append("'");
        sqlBuilder.append("\n  AND NOT EXISTS (");
        sqlBuilder.append("\n    SELECT 1 ");
        sqlBuilder.append("\n    FROM tk_yd_fhmxdhqtck t1");
        sqlBuilder.append("\n    WHERE t1.fk_yd_textfield_fhmxdh COLLATE utf8mb4_unicode_ci = ").append(DETAIL_ALIAS).append(".fbillno COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t1.fk_yd_rowid COLLATE utf8mb4_unicode_ci = CAST(").append(ENTRY_ALIAS).append(".fentryid AS CHAR) COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t1.fk_yd_textfield_fhmxdh IS NOT NULL ");
        sqlBuilder.append("\n      AND t1.fk_yd_textfield_fhmxdh != ''");
        sqlBuilder.append("\n      AND t1.fk_yd_rowid IS NOT NULL ");
        sqlBuilder.append("\n      AND t1.fk_yd_rowid != ''");
        sqlBuilder.append("\n  )");
        sqlBuilder.append("\n  AND NOT EXISTS (");
        sqlBuilder.append("\n    SELECT 1 ");
        sqlBuilder.append("\n    FROM tk_yd_fhmxdh t2");
        sqlBuilder.append("\n    WHERE t2.fk_yd_textfield_fhmxdh COLLATE utf8mb4_unicode_ci = ").append(DETAIL_ALIAS).append(".fbillno COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t2.fk_yd_rowid COLLATE utf8mb4_unicode_ci = CAST(").append(ENTRY_ALIAS).append(".fentryid AS CHAR) COLLATE utf8mb4_unicode_ci");
        sqlBuilder.append("\n      AND t2.fk_yd_textfield_fhmxdh IS NOT NULL ");
        sqlBuilder.append("\n      AND t2.fk_yd_textfield_fhmxdh != ''");
        sqlBuilder.append("\n      AND t2.fk_yd_rowid IS NOT NULL ");
        sqlBuilder.append("\n      AND t2.fk_yd_rowid != ''");
        sqlBuilder.append("\n  )");

        String totalSql = sqlBuilder.toString();
        log.info("生成的按日期过滤SQL: {}", totalSql);
        return totalSql;
    }

    
//     private static String buildInClause(List<String> billNoList) {
//         StringJoiner joiner = new StringJoiner(", ");
//         for (String billNo : billNoList) {
//             joiner.add("'" + billNo + "'");
//         }
//         return joiner.toString();
//     }

    // ===================== SQL字段片段 =====================

    /** 
     * 基础信息字段
     * - e.fentryid: 拆单明细主键
     * - e.fsq: 拆单明细行号  
     * - e.fid: 主表关联ID
     * - d.fid: 发货明细主键
     * - d.fbillno: 单据编号
     * - d.fbillstatus: 单据状态 (A:暂存 B:已提交 C:已审核)
     */
    private static final String BASE_FIELDS = 
            "\n    " + ENTRY_ALIAS + ".fentryid AS " + ENTRY_PREFIX + "fentryid, " +
            "\n    " + ENTRY_ALIAS + ".fseq AS " + ENTRY_PREFIX + "fseq, " +
            "\n    " + ENTRY_ALIAS + ".fid AS " + ENTRY_PREFIX + "fid, " +
            "\n    " + DETAIL_ALIAS + ".fid AS " + DETAIL_PREFIX + "fid, " +
            "\n    " + DETAIL_ALIAS + ".fbillno AS " + DETAIL_PREFIX + "fbillno, " +
            "\n    " + DETAIL_ALIAS + ".fbillstatus AS " + DETAIL_PREFIX + "fbillstatus, ";

    /**
     * 物料信息字段
     * - e.fk_yd_materialid: 单品物料编码
     * - e.fk_yd_omsgoodsn: 货品编号(OMS明细)
     * - e.fk_yd_brandid: 品牌
     * - e.fk_yd_matgroupid: 物料类型
     * - e.fk_yd_producttype: 产品类型
     */
    private static final String MATERIAL_FIELDS = 
            "\n    " + ENTRY_ALIAS + ".fk_yd_materialid AS " + ENTRY_PREFIX + "fk_yd_materialid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_omsgoodsn AS " + ENTRY_PREFIX + "fk_yd_omsgoodsn, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_brandid AS " + ENTRY_PREFIX + "fk_yd_brandid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_matgroupid AS " + ENTRY_PREFIX + "fk_yd_matgroupid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_producttype AS " + ENTRY_PREFIX + "fk_yd_producttype, ";

    /**
     * 数量与金额信息字段
     * - e.fk_yd_qty: 数量
     * - e.fk_yd_taxprice: 含税单价
     * - e.fk_yd_totaltaxamount: 价税合计
     * - e.fk_yd_distsupplyprice: 分销供货单价
     * - e.fk_yd_sshelves_price: 商品上架单价
     * - e.fk_yd_stotaldiscountamt: 商品总折扣
     * - e.fk_yd_savlogisticscost: 均摊运费
     * - e.fk_yd_splitshareamount: 均摊金额
     */
    private static final String QUANTITY_AMOUNT_FIELDS = 
            "\n    " + ENTRY_ALIAS + ".fk_yd_qty AS " + ENTRY_PREFIX + "fk_yd_qty, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_taxprice AS " + ENTRY_PREFIX + "fk_yd_taxprice, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_totaltaxamount AS " + ENTRY_PREFIX + "fk_yd_totaltaxamount, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_distsupplyprice AS " + ENTRY_PREFIX + "fk_yd_distsupplyprice, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_sshelves_price AS " + ENTRY_PREFIX + "fk_yd_sshelves_price, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_stotaldiscountamt AS " + ENTRY_PREFIX + "fk_yd_stotaldiscountamt, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_savlogisticscost AS " + ENTRY_PREFIX + "fk_yd_savlogisticscost, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_splitshareamount AS " + ENTRY_PREFIX + "fk_yd_splitshareamount, ";

    /**
     * 组织仓库字段
     * - e.fk_yd_saleorgid: 销售组织编码
     * - e.fk_yd_invorgid: 库存组织编码
     * - e.fk_yd_cqcustomerid: 苍穹客户编码
     * - e.fk_yd_stocktypeid: 仓库类型
     * - e.fk_yd_salorgstockid: 销售组织仓库编码
     * - e.fk_yd_invorgstockid: 库存组织仓库编码
     * - d.fk_yd_customerid: 客户
     * - d.fk_yd_shoporgid: 店铺组织
     */
    private static final String ORGANIZATION_FIELDS = 
            "\n    " + ENTRY_ALIAS + ".fk_yd_saleorgid AS " + ENTRY_PREFIX + "fk_yd_saleorgid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_invorgid AS " + ENTRY_PREFIX + "fk_yd_invorgid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_cqcustomerid AS " + ENTRY_PREFIX + "fk_yd_cqcustomerid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_stocktypeid AS " + ENTRY_PREFIX + "fk_yd_stocktypeid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_salorgstockid AS " + ENTRY_PREFIX + "fk_yd_salorgstockid, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_invorgstockid AS " + ENTRY_PREFIX + "fk_yd_invorgstockid, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_customerid AS " + DETAIL_PREFIX + "fk_yd_customerid, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_shoporgid AS " + DETAIL_PREFIX + "fk_yd_shoporgid, ";

    /**
     * 状态标记字段
     * - e.fk_yd_src_ispropackage: 是否组装品
     * - e.fk_yd_src_ispagesplit: 是否已拆分
     * - e.fk_yd_isgift: 是否赠品
     * - e.fk_yd_src_error: 拆分失败原因
     * - e.fk_yd_omsrownum: OMS行号
     * - e.fk_yd_omsgoodsn: 货品编号
     * - e.fk_yd_issplitexcludemat: 剔除物料
     * - d.fk_yd_relatedordernotexis: 退单关联订单不存在
     * - d.fk_yd_salorgstocknotexist: 销售组织仓库不存在
     */
    private static final String STATUS_FIELDS = 
            "\n    " + ENTRY_ALIAS + ".fk_yd_src_ispropackage AS " + ENTRY_PREFIX + "fk_yd_src_ispropackage, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_src_ispagesplit AS " + ENTRY_PREFIX + "fk_yd_src_ispagesplit, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_isgift AS " + ENTRY_PREFIX + "fk_yd_isgift, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_src_error AS " + ENTRY_PREFIX + "fk_yd_src_error, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_omsrownum AS " + ENTRY_PREFIX + "fk_yd_omsrownum, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_issplitexcludemat AS " + ENTRY_PREFIX + "fk_yd_issplitexcludemat, " +
            "\n    " + ENTRY_ALIAS + ".fk_yd_omsgoodsn AS " + ENTRY_PREFIX + "fk_yd_omsgoodsn, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_relatedordernotexis AS " + DETAIL_PREFIX + "fk_yd_relatedordernotexis, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_salorgstocknotexist AS " + DETAIL_PREFIX + "fk_yd_salorgstocknotexist, ";

    /**
     * 订单平台及店铺信息字段
     * - d.fk_yd_combofield_pt: 平台 (1:E3 2:旺店通 3:吉客云 4:万里牛 5:新E3)
     * - d.fk_yd_textfield_ddbh: 订单编号
     * - d.fk_yd_textfield_dpbh: 店铺编号
     * - d.fk_yd_textfield_xsckdh: 下游单号
     * - d.fk_yd_datetimefield_xdsj: 平台发货时间
     * - d.fk_yd_combofield_xyd: 下游单据类型 (1:销售出库单 2:其他出库单)
     * - d.fk_yd_ordertype: 单据类型 (0:订单 1:退单)
     * - d.fk_yd_lylx: 来源类型
     * - d.fk_yd_sdid: 商店ID
     * - d.fk_yd_sdname: 店铺名称
     * - d.fk_yd_notsaleshop: 店铺
     * - d.fk_yd_isnotsale: 是否非销
     * - d.fk_yd_isbrandsplitbill: 是否品牌分单
     * - d.fk_yd_shoporgid: 店铺组织
     * - d.fk_yd_orderstatus: 订单状态
     * - d.fk_yd_paystatus: 财务状态
     * - d.fk_yd_settlestatus: 结算状态 (1:主表待结算 2:OMS明细待结算 3:拆单明细待结算 4:已结算+待下推 5:部分下推 6:完全下推)
     * - d.fk_yd_customerid: 客户 (基础资料：客户)
     * - d.fk_yd_dealcode: 交易号
     * - d.fk_yd_bz: 备注
     * - d.fk_yd_merchantremark: 商家备注
     * - d.fk_yd_checkboxfield_th: 是否退货
     */
    private static final String ORDER_FIELDS = 
            "\n    " + DETAIL_ALIAS + ".fk_yd_combofield_pt AS " + DETAIL_PREFIX + "fk_yd_combofield_pt, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_textfield_ddbh AS " + DETAIL_PREFIX + "fk_yd_textfield_ddbh, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_textfield_dpbh AS " + DETAIL_PREFIX + "fk_yd_textfield_dpbh, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_textfield_xsckdh AS " + DETAIL_PREFIX + "fk_yd_textfield_xsckdh, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_datetimefield_xdsj AS " + DETAIL_PREFIX + "fk_yd_datetimefield_xdsj, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_combofield_xyd AS " + DETAIL_PREFIX + "fk_yd_combofield_xyd, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_ordertype AS " + DETAIL_PREFIX + "fk_yd_ordertype, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_lylx AS " + DETAIL_PREFIX + "fk_yd_lylx, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_sdid AS " + DETAIL_PREFIX + "fk_yd_sdid, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_sdname AS " + DETAIL_PREFIX + "fk_yd_sdname, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_notsaleshop AS " + DETAIL_PREFIX + "fk_yd_notsaleshop, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_isnotsale AS " + DETAIL_PREFIX + "fk_yd_isnotsale, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_isbrandsplitbill AS " + DETAIL_PREFIX + "fk_yd_isbrandsplitbill, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_shoporgid AS " + DETAIL_PREFIX + "fk_yd_shoporgid, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_orderstatus AS " + DETAIL_PREFIX + "fk_yd_orderstatus, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_paystatus AS " + DETAIL_PREFIX + "fk_yd_paystatus, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_settlestatus AS " + DETAIL_PREFIX + "fk_yd_settlestatus, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_customerid AS " + DETAIL_PREFIX + "fk_yd_customerid, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_dealcode AS " + DETAIL_PREFIX + "fk_yd_dealcode, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_bz AS " + DETAIL_PREFIX + "fk_yd_bz, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_merchantremark AS " + DETAIL_PREFIX + "fk_yd_merchantremark, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_checkboxfield_th AS " + DETAIL_PREFIX + "fk_yd_checkboxfield_th, ";

    /**
     * 地址信息字段
     * - d.fk_yd_provincename: 省
     * - d.fk_yd_cityname: 市
     * - d.fk_yd_districtname: 区
     * - d.fk_yd_address: 收货地址
     */
    private static final String ADDRESS_FIELDS = 
            "\n    " + DETAIL_ALIAS + ".fk_yd_provincename AS " + DETAIL_PREFIX + "fk_yd_provincename, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_cityname AS " + DETAIL_PREFIX + "fk_yd_cityname, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_districtname AS " + DETAIL_PREFIX + "fk_yd_districtname, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_address AS " + DETAIL_PREFIX + "fk_yd_address, ";

    /**
     * 时间信息字段
     * - d.fk_yd_datefield_fhrq: 发货日期
     * - d.fk_yd_shippingtimeck: E3出库时间
     * - d.fk_yd_shippingtimefh: E3发货时间
     * - d.fk_yd_addtime: 下单时间
     * - d.fk_yd_paytime: 支付时间
     */
    private static final String DATE_FIELDS = 
            "\n    " + DETAIL_ALIAS + ".fk_yd_datefield_fhrq AS " + DETAIL_PREFIX + "fk_yd_datefield_fhrq, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_shippingtimeck AS " + DETAIL_PREFIX + "fk_yd_shippingtimeck, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_shippingtimefh AS " + DETAIL_PREFIX + "fk_yd_shippingtimefh, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_addtime AS " + DETAIL_PREFIX + "fk_yd_addtime, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_paytime AS " + DETAIL_PREFIX + "fk_yd_paytime, ";

    /**
     * 其他信息字段
     * - d.fk_yd_errorstate: 异常情况 (0:无异常 1:金额为负 2:数量缺漏 3:数量缺漏且金额为负)
     * - d.fk_yd_textfield_sbyy: 失败原因
     * - d.fk_yd_budgetcompany: 预算承担公司
     * - d.fk_yd_budgetdepart: 预算承担部门
     * - d.fk_yd_budgetaccount: 预算科目
     * - d.fk_yd_applydepart: 申请部门
     * - d.fk_yd_requisitionuse: 领用用途
     * - d.fk_yd_costcenter: 成本中心
     */
    private static final String OTHER_FIELDS = 
            "\n    " + DETAIL_ALIAS + ".fk_yd_errorstate AS " + DETAIL_PREFIX + "fk_yd_errorstate, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_textfield_sbyy AS " + DETAIL_PREFIX + "fk_yd_textfield_sbyy, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_budgetcompany AS " + DETAIL_PREFIX + "fk_yd_budgetcompany, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_budgetdepart AS " + DETAIL_PREFIX + "fk_yd_budgetdepart, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_budgetaccount AS " + DETAIL_PREFIX + "fk_yd_budgetaccount, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_applydepart AS " + DETAIL_PREFIX + "fk_yd_applydepart, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_requisitionuse AS " + DETAIL_PREFIX + "fk_yd_requisitionuse, " +
            "\n    " + DETAIL_ALIAS + ".fk_yd_costcenter AS " + DETAIL_PREFIX + "fk_yd_costcenter ";
}

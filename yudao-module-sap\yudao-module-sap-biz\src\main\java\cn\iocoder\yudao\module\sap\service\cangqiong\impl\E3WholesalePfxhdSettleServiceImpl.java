package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.*;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.*;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformMaterialKey;
import cn.iocoder.yudao.module.sap.model.delivery.PlatformWarehouseKey;
import cn.iocoder.yudao.module.sap.model.delivery.SplitSettleCacheData;
import cn.iocoder.yudao.module.sap.model.delivery.DirectwarehouseKey;
import cn.iocoder.yudao.module.sap.service.cangqiong.E3WholesalePfxhdSettleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import javax.annotation.Resource;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWholesaleNoticeBillService;

import java.util.*;

import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqChanneltoorgService;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailSettleService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDBCustomerRelationService;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqCustomerDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.customer.CqDBCustomerRelationEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialDO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqPcckService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqCkdygxService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdWarehouseService;
import cn.iocoder.yudao.module.sap.dal.dataobject.warehouse.CqBdWarehouseDO;
import cn.iocoder.yudao.module.sap.enums.BusinessSceneEnum;
import cn.iocoder.yudao.module.sap.model.delivery.MaterialMatchResult;
import cn.iocoder.yudao.module.sap.model.delivery.OutwarehouseMapKey;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqPcwlService;
//import cn.iocoder.yudao.module.sap.service.cangqiong.CqMatCombinationService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdMaterialService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqYdOutwarehousemapService;    
import cn.iocoder.yudao.module.sap.service.cangqiong.CqOrgCusPriceBillService;
import cn.iocoder.yudao.module.sap.dal.dataobject.orgcuspricebill.CqOrgCusPriceBillEntryDO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWldygxService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqCustomerService;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqDirectwarehouseService;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqBomWithEntriesDTO;
import cn.iocoder.yudao.module.sap.dal.dataobject.bom.CqBomEntryDO;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBomService;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * 批发销售单结算服务实现类
 */
@Service
@Slf4j
public class E3WholesalePfxhdSettleServiceImpl implements E3WholesalePfxhdSettleService {

    @Resource
    private CqWholesaleNoticeBillService cqWholesaleNoticeBillService;

    @Resource
    private CqChanneltoorgService cqChanneltoorgService;

    @Resource
    private DeliveryDetailSettleService deliveryDetailSettleService;

    @Resource
    private CqDBCustomerRelationService cqDBCustomerRelationService;

    @Resource
    private CqPcckService cqPcckService;

    @Resource
    private CqCkdygxService cqCkdygxService;    

    @Resource
    private CqBdWarehouseService cqBdWarehouseService;

    @Resource
    private CqPcwlService cqPcwlService;

//    @Resource
//    private CqMatCombinationService cqMatCombinationService;

    @Resource
    private CqBdMaterialService cqBdMaterialService;

    @Resource
    private CqYdOutwarehousemapService cqYdOutwarehousemapService;

    @Resource
    private CqOrgCusPriceBillService cqOrgCusPriceBillService;

    @Resource
    private CqWldygxService cqWldygxService;

    @Resource
    private CqCustomerService cqCustomerService;

    @Resource
    private CqDirectwarehouseService cqDirectwarehouseService;

    @Resource
    private CqBomService cqBomService;

    /**
     * 执行批发销售单主表结算
     * 处理流程：
     * 1. 检查输入的单据编号列表是否为空
     * 2. 根据结算状态获取需要处理的批发通知单
     * 3. 遍历每个批发通知单进行结算处理
     *
     * @param params 结算参数DTO，包含单据编号列表等参数
     */
    @Override
    public void doMainSettle(WholesalePfxhdSettleParamsDTO params) {
        // 参数校验和提取
        if (params == null) {
            log.warn("[doMainSettle][参数为空，使用默认参数]");
            params = new WholesalePfxhdSettleParamsDTO();
        }
        
        List<String> billNos = params.getBillNos();
        if (CollectionUtils.isEmpty(billNos)) {
            log.info("[doMainSettle][没有需要处理的单据]");
            return;
        }
        
        List<String> settleStatuses = Arrays.asList("1");
        List<CqWholesaleNoticeBillDetailDTO> bills = cqWholesaleNoticeBillService.getWholesaleNoticeBillDetailsBySettleStatuses(billNos, settleStatuses);
        
        if (CollectionUtils.isEmpty(bills)) {
            log.info("[doMainSettle][单据编号列表: {} 没有可结算的批发通知单]", billNos);
            return;
        }
        
        // 获取所有客户Map（以编号为键）
        Map<String, CqCustomerDO> customerNumMap = cqCustomerService.getAllCustomersByNumberMap();

        for (CqWholesaleNoticeBillDetailDTO bill : bills) {
            try {
                processWholesaleNoticeBillWithMainSettle(bill, customerNumMap);
            } catch (Exception e) {
                log.error("[doMainSettle][处理批发通知单异常，单据编号: {}, 异常信息: {}]", bill.getBill().getBillNo(), e.getMessage(), e);
            }
        }
    }

    /**
     * 处理单个批发通知单的主表结算
     * 
     * 主要处理逻辑：
     * 1. 设置业务场景（根据发货类型）
     * 2. 处理组织编码（通过渠道编码匹配苍穹渠道组织对应表）
     * 3. 处理客户映射（优先店铺客户映射表，其次客户主数据）
     * 4. 处理仓库映射（检查是否剔除仓库，匹配苍穹仓库）
     * 5. 更新结算状态和相关信息
     *
     * @param bill 批发通知单详情DTO，包含主单据和明细信息
     * @param customerNumMap 客户编号到客户对象的映射Map，用于快速查找客户信息
     */
    private void processWholesaleNoticeBillWithMainSettle(CqWholesaleNoticeBillDetailDTO bill, Map<String, CqCustomerDO> customerNumMap) {
        // 获取主单据
        CqWholesaleNoticeBillDO mainBill = bill.getBill();
        String platform = mainBill.getPlatform();
        
        // 创建更新DTO对象
        WholesaleNoticeBillSettleUpdateDTO updateDTO = new WholesaleNoticeBillSettleUpdateDTO();

        // 设置业务场景
        String businessScene = getBusinessSceneFromEnum(mainBill.getShippingType());
        updateDTO.setYdBusinessscene(businessScene);
        
        // 组织编码 取 渠道编码 对应 苍穹渠道组织对应表 组织编码
        List<CqChanneltoorgEntryDO> channelOrgRelations = cqChanneltoorgService.getAuditedChanneltoorgEntries(mainBill.getChannelNo());
        if (CollectionUtils.isEmpty(channelOrgRelations) || channelOrgRelations.size() > 1) {
            log.info("[processWholesaleNoticeBill][单据编号: {} 渠道组织不存在]", mainBill.getBillNo());
            // 渠道组织不存在
            updateDTO.setNotExistChannelOrg(true);
        } else {
            CqChanneltoorgEntryDO channelOrgRelation = channelOrgRelations.get(0);
            updateDTO.setOrgId(channelOrgRelation.getOrgId());
        }
        
        //取{E3客户编码}对应店铺客户映射表{苍穹客户编码}，
        //若取不到，将{E3客户编码}匹配{客户主数据}客户编码，若存在，取{E3客户编码}，若不存在，则为空
        List<CqDBCustomerRelationEntryDO> customerRelationEntries = cqDBCustomerRelationService
            .getCustomerRelationEntriesByPlatformAndStore(platform, mainBill.getCustomerNo(), 1);
        if (CollectionUtils.isEmpty(customerRelationEntries) || customerRelationEntries.size() > 1) {
            // 店铺客户映射表中未找到对应关系，尝试直接匹配客户主数据
            // updateDTO.setNotExistCustomer(true);
            // 根据E3客户编码从客户主数据中查找对应的苍穹客户
            CqCustomerDO cqCustomerDO = customerNumMap.get(mainBill.getCustomerNo());
            if(cqCustomerDO != null) {
                // 在客户主数据中找到匹配的客户，设置苍穹客户ID
                updateDTO.setCqCustomerId(cqCustomerDO.getFid());
            } else {
                // 客户主数据中也未找到匹配的客户，标记客户不存在
                updateDTO.setNotExistCustomer(true);
            }   
        } else {
            CqDBCustomerRelationEntryDO customerRelationEntry = customerRelationEntries.get(0);
            updateDTO.setCqCustomerId(customerRelationEntry.getKingdeeCustomerId());
            // 是否按品牌分单 客户不存在为否，将 E3客户编码 匹配 店铺客户 对应表 "平台店铺"，取"是否按照品牌分单" 
            updateDTO.setIsBrandSplitBill(customerRelationEntry.getIsBrandSeparated() == 1);
        }
        
        // 苍穹库存组织仓库编码 取{E3仓库编码}匹配仓库对应表 {苍穹仓库}
        // 库存组织仓库不存在 剔除仓库为否且{E3仓库编码}匹配仓库对应表 {苍穹仓库}，匹配不到，则为是
        boolean isExcludedWarehouse = cqPcckService.isCodeExcluded(mainBill.getWarehouseNo());
        updateDTO.setExcludeStock(isExcludedWarehouse);
        if (!isExcludedWarehouse) {
            // // 根据平台和仓库编码查询仓库对应关系
            // List<CqCkdygxEntryDO> ckdygxEntryDOs = cqCkdygxService.getCkdygxEntryByPlatformAndCode(platform, mainBill.getWarehouseNo());
            // // 如果没有找到对应关系或找到多个对应关系，则标记库存组织仓库不存在
            // if (CollectionUtils.isEmpty(ckdygxEntryDOs) || ckdygxEntryDOs.size() > 1) {
            //     updateDTO.setNotExistInvOrgStock(true);
            // } else {
            //     // 找到唯一对应关系，设置苍穹仓库ID
            //     CqCkdygxEntryDO ckdygxEntryDO = ckdygxEntryDOs.get(0);
            //     updateDTO.setCqInvOrgStockId(ckdygxEntryDO.getWarehouseId());
            //     // 库存组织 将发货仓库匹配仓库映射表，取仓库所属组织
            //     // 获取仓库的创建组织作为库存组织
            //     CqBdWarehouseDO cqBdWarehouseDO = cqBdWarehouseService.getWarehouse(ckdygxEntryDO.getWarehouseId());
            //     updateDTO.setInvOrgId(cqBdWarehouseDO.getCreateOrgId());
            // }
        }
        
        if (Boolean.TRUE.equals(updateDTO.getNotExistChannelOrg()) || Boolean.TRUE.equals(updateDTO.getNotExistCustomer())
            || Boolean.TRUE.equals(updateDTO.getExcludeStock())) {
            // 渠道组织不存在 或 客户不存在 或 剔除仓库 或 库存组织仓库不存在 则保持主表待结算
            updateDTO.setSettleStatus("1");
        } else {
            // 渠道组织存在 且 客户存在 且 剔除仓库 或 库存组织仓库存在 则转为OMS明细待结算
            updateDTO.setSettleStatus("2");
        }
        
        // 调用新方法更新批发通知单
        boolean updated = cqWholesaleNoticeBillService.updateWholesaleNoticeBillSettleInfoByBillNo(mainBill.getBillNo(), updateDTO);
        if (!updated) {
            log.error("[processWholesaleNoticeBill][更新批发通知单结算信息失败，单据编号: {}]", mainBill.getBillNo());
        }
    }

    /**
     * 根据发货类型名称获取业务场景代码
     *
     * @param fhlxName 发货类型名称
     * @return 业务场景代码
     */
    private String getBusinessSceneFromEnum(String fhlxName) {
        return BusinessSceneEnum.getSceneCodeByShippingTypeName(fhlxName);
    }

    /**
     * 执行OMS明细结算
     * 处理状态为"2"的批发销货单，进行OMS明细级别的结算处理
     * 
     * 主要功能：
     * 1. 参数校验和提取单据编号列表
     * 2. 查询状态为"2"的批发通知单详情
     * 3. 批量预加载物料对应关系、排除物料、已审核物料等缓存数据
     * 4. 遍历处理每个批发通知单的OMS明细结算逻辑
     * 5. 异常处理和日志记录
     * 
     * @param params 批发销货单结算参数DTO，包含需要处理的单据编号列表等信息
     */
    @Override
    public void doOMSDetailSettle(WholesalePfxhdSettleParamsDTO params) {
        // 参数校验和提取
        if (params == null) {
            log.warn("[doOMSDetailSettle][参数为空，使用默认参数]");
            params = new WholesalePfxhdSettleParamsDTO();
        }
        
        List<String> billNos = params.getBillNos();
        if (CollectionUtils.isEmpty(billNos)) {
            log.info("[doOMSDetailSettle][没有需要处理的单据]");
            return;
        }
        
        List<String> settleStatuses = Arrays.asList("2");
        List<CqWholesaleNoticeBillDetailDTO> bills = cqWholesaleNoticeBillService.getWholesaleNoticeBillDetailsBySettleStatuses(billNos, settleStatuses);
        
        if (CollectionUtils.isEmpty(bills)) {
            log.info("[doOMSDetailSettle][单据编号列表: {} 没有可结算的批发通知单]", billNos);
            return;
        }

        
        // 批量查询所有物料对应关系，优化性能
        Map<PlatformMaterialKey, List<CqWldygxEntryDO>> wldygxEntriesMap = cqWldygxService.getAllWldygxEntries();
        log.info("[doOMSDetailSettle][单据编号: {} 获取到 {} 个物料对应关系]", billNos, wldygxEntriesMap.size());
        
        // 批量查询所有已审核的排除物料编码，优化性能
        Set<String> excludedMaterials = cqPcwlService.getAllExcludedMaterials();
        log.info("[doSplitDetailSettle][单据编号: {} 获取到 {} 个排除物料编码]", billNos, excludedMaterials.size());

        // 批量查询所有已审核的物料，优化性能
        long startTime = System.currentTimeMillis();
        List<CqBdMaterialDO> allApprovedMaterials = cqBdMaterialService.getAllMaterialsWithGroup();
        long materialQueryTime = System.currentTimeMillis();
        log.info("[doOMSDetailSettle][单据编号: {} 获取到 {} 个已审核物料，耗时: {}ms]", billNos, allApprovedMaterials.size(), materialQueryTime - startTime);
        
        // 批量查询所有已审核的销售BOM数据，优化性能
        Map<String, CqBomWithEntriesDTO> bomStructuresMap = cqBomService.getAllAuditedBomStructuresMap();
        log.info("[doOMSDetailSettle][单据编号: {} 获取到 {} 个已审核的销售BOM数据]", billNos, bomStructuresMap.size());

        for (CqWholesaleNoticeBillDetailDTO bill : bills) {
            try {
                processWholesaleNoticeBillWithOMSDetailSettle(bill, wldygxEntriesMap, allApprovedMaterials, excludedMaterials, bomStructuresMap);
            } catch (Exception e) {
                log.error("[doOMSDetailSettle][处理批发通知单异常，单据编号: {}, 异常信息: {}]", bill.getBill().getBillNo(), e.getMessage(), e);
            }
        }
    }

    /**
     * 处理单个批发通知单的OMS明细结算
     * 对批发通知单进行明细级别的结算处理，包括物料匹配、组装品判断、排除物料检查等
     *
     * @param bill 批发通知单详情DTO，包含主单据和明细信息
     * @param wldygxEntriesMap 物料对应关系映射表，用于物料匹配
     * @param allApprovedMaterials 所有已审核的物料列表
     * @param excludedMaterials 排除物料编码集合
     */
    private void processWholesaleNoticeBillWithOMSDetailSettle(CqWholesaleNoticeBillDetailDTO bill, Map<PlatformMaterialKey, List<CqWldygxEntryDO>> wldygxEntriesMap, List<CqBdMaterialDO> allApprovedMaterials, Set<String> excludedMaterials, Map<String, CqBomWithEntriesDTO> bomStructuresMap) {
        // 获取主单据信息
        CqWholesaleNoticeBillDO mainBill = bill.getBill();
        String platform = mainBill.getPlatform();
        // 获取明细
        List<CqWholesaleNoticeBillOMSEntryDO> entries = bill.getEntries();
        if (CollectionUtils.isEmpty(entries)) {
            log.info("[processWholesaleNoticeBillWithOMSDetailSettle][单据编号: {} 没有可结算的批发通知单明细]", mainBill.getBillNo());
            return;
        }
        
        // 创建批量更新DTO
        BatchWholesaleNoticeBillEntrySettleUpdateDTO batchUpdateDTO = new BatchWholesaleNoticeBillEntrySettleUpdateDTO();
        batchUpdateDTO.setBillNo(mainBill.getBillNo());

        // 创建明细更新列表
        List<WholesaleNoticeBillOMSEntrySettleUpdateDTO> entryUpdateDTOs = new ArrayList<>(entries.size());
        
        for (CqWholesaleNoticeBillOMSEntryDO entry : entries) {
            // 创建明细更新DTO对象
            WholesaleNoticeBillOMSEntrySettleUpdateDTO entryUpdateDTO = new WholesaleNoticeBillOMSEntrySettleUpdateDTO();
            entryUpdateDTO.setEntryId(entry.getEntryId());
            
            // 剔除物料 将"货品编号"匹配 {剔除物料}表 "业务平台编码"，若存在，则为是，否则为否
            boolean isExcludedMaterial = excludedMaterials.contains(entry.getGoodsNum());
            if(isExcludedMaterial) {
                // 排除物料
                entryUpdateDTO.setIsExcludedMaterialOms(true);
            } else {
                // 是否组装品 将"货品编号"匹配【E3主商品拆分表】，若主商品编码存在，则为是，否则为否
                CqBomWithEntriesDTO bomWithEntriesDTO = bomStructuresMap.get(entry.getGoodsNum());
                if(bomWithEntriesDTO != null) {
                    // 组装品
                    entryUpdateDTO.setIsBom(true);
                    // 设置单品物料ID（为组套，设置单品物料ID为0）
                    entryUpdateDTO.setSingleMaterialId(0L);
                    // if(cqMatCombinationEntryDetailVOs.size() > 1) {
                    //     entryUpdateDTO.setIsMaterialRelationRepeat(true);
                    // }
                } else {
                    // 非组装品
                    // 调用物料匹配方法
                    MaterialMatchResult matchResult = deliveryDetailSettleService.matchOMSDetailMaterial(platform, entry.getGoodsNum(), wldygxEntriesMap, allApprovedMaterials);
                    entryUpdateDTO.setSingleMaterialId(matchResult.getMaterialId());
                    // 物料不存在
                    if (matchResult.getIsNotExitMaterial() == 1) {
                        entryUpdateDTO.setIsNotExistMaterialOms(true);
                    }
                    // 物料关系重复
                    if (matchResult.getIsMaterialRelationRepeat() == 1) {
                        entryUpdateDTO.setIsMaterialRelationRepeat(true);
                    }
                }
            }

            // 异常单据判断：实际金额小于0时标记为异常单据
            boolean isErrorBill = entry.getAmount().compareTo(BigDecimal.ZERO) < 0;
            entryUpdateDTO.setIsErrorBill(isErrorBill);
            entryUpdateDTO.setErrorReason(isErrorBill ? "1" : "0");
            if (isErrorBill) {
                batchUpdateDTO.setIsErrorBill(true);
            }
            
            // 添加到批量更新列表
            entryUpdateDTOs.add(entryUpdateDTO);
        }
        
        // 设置批量更新对象的明细列表
        batchUpdateDTO.setEntries(entryUpdateDTOs);

        // 整单剔除物料判断
        if (!entryUpdateDTOs.isEmpty()) {
            // 整单剔除物料
            batchUpdateDTO.setIsWholeOrderExcludeMaterial(entryUpdateDTOs.stream()
                    .allMatch(entry -> entry.getIsExcludedMaterialOms() != null && entry.getIsExcludedMaterialOms()));

            // 物料不存在
            batchUpdateDTO.setIsNotExistMaterial(entryUpdateDTOs.stream()
                    .anyMatch(entry -> entry.getIsNotExistMaterialOms() != null && entry.getIsNotExistMaterialOms()));

            // 物料关系重复
            batchUpdateDTO.setIsMatRepeat(entryUpdateDTOs.stream()
                    .anyMatch(entry -> entry.getIsMaterialRelationRepeat() != null && entry.getIsMaterialRelationRepeat()));

            // 单据异常
            batchUpdateDTO.setIsErrorBill(entryUpdateDTOs.stream()
                    .anyMatch(entry -> entry.getIsErrorBill() != null && entry.getIsErrorBill()));
        }

        // 根据明细处理状态决定主单据下一步状态
        String nextSettleStatus;
        if (batchUpdateDTO.getIsWholeOrderExcludeMaterial() || batchUpdateDTO.getIsNotExistMaterial() || batchUpdateDTO.getIsMatRepeat() || batchUpdateDTO.getIsErrorBill()) {
            // 存在整单剔除物料、物料不存在或物料关系重复或单据异常，保持OMS明细待结算状态
            nextSettleStatus = "2";
        } else {
            // 所有物料处理正常，进入拆单明细待结算
            nextSettleStatus = "3";
        }
        batchUpdateDTO.setMainSettleStatus(nextSettleStatus);
        // 调用批量更新方法
        boolean updated = cqWholesaleNoticeBillService.batchUpdateWholesaleNoticeBillEntriesSettleInfo(batchUpdateDTO);
        if (!updated) {
            log.error("[processWholesaleNoticeBillWithOMSDetailSettle][更新批发通知单明细结算信息失败，单据编号: {}]", mainBill.getBillNo());
        } else {
            log.info("[processWholesaleNoticeBillWithOMSDetailSettle][更新批发通知单明细结算信息成功，单据编号: {}, 下一步状态: {}]", 
                    mainBill.getBillNo(), nextSettleStatus);
        }
    }

    /**
     * 执行拆单明细结算
     * 处理状态为"3"的批发销货单，进行拆单明细级别的结算处理
     * 
     * 主要功能：
     * 1. 参数校验和提取单据编号列表
     * 2. 查询状态为"3"的批发通知单详情
     * 3. 批量预加载拆单结算所需的缓存数据（物料信息等）
     * 4. 遍历处理每个批发通知单的拆单明细结算逻辑
     * 5. 异常处理和日志记录
     * 
     * 处理流程：
     * 1. 检查输入的单据编号列表是否为空
     * 2. 根据结算状态"3"获取需要处理的批发通知单
     * 3. 加载拆单结算所需的缓存数据，避免重复查询
     * 4. 遍历每个批发通知单进行拆单明细结算处理
     *
     * @param params 批发销货单结算参数DTO，包含需要处理的单据编号列表等信息
     */
    @Override
    public void doSplitDetailSettle(WholesalePfxhdSettleParamsDTO params) {
        // 参数校验和提取
        if (params == null) {
            log.warn("[doSplitDetailSettle][参数为空，使用默认参数]");
            params = new WholesalePfxhdSettleParamsDTO();
        }
        
        List<String> billNos = params.getBillNos();
        if (CollectionUtils.isEmpty(billNos)) {
            log.info("[doSplitDetailSettle][没有需要处理的单据]");
            return;
        }
        
        List<String> settleStatuses = Arrays.asList("3");
        List<CqWholesaleNoticeBillDetailDTO> bills = cqWholesaleNoticeBillService.getWholesaleNoticeBillDetailsBySettleStatuses(billNos, settleStatuses);
        
        if (CollectionUtils.isEmpty(bills)) {
            log.info("[doSplitDetailSettle][单据编号列表: {} 没有可结算的批发通知单]", billNos);
            return;
        }
        
        // 加载缓存数据（一次性获取，避免重复查询）
        SplitSettleCacheData cacheData = loadWholesaleSplitSettleCacheData();
        
        for (CqWholesaleNoticeBillDetailDTO bill : bills) {
            try {
                processWholesaleNoticeBillWithSplitDetailSettle(bill, cacheData);
            } catch (Exception e) {
                log.error("[doSplitDetailSettle][处理批发通知单异常，单据编号: {}, 异常信息: {}]", bill.getBill().getBillNo(), e.getMessage(), e);
            }
        }
    }

    /**
     * 加载批发拆单结算所需的所有缓存数据
     * 参考DeliveryDetailSettleServiceImpl的loadSplitSettleCacheData方法实现
     * 
     * @return 拆单结算缓存数据对象
     */
    private SplitSettleCacheData loadWholesaleSplitSettleCacheData() {
        long startTime = System.currentTimeMillis();
        
        // 创建缓存数据对象
        SplitSettleCacheData cacheData = new SplitSettleCacheData();
        
        // 1. 批量查询所有物料对应关系映射表（暂时跳过，后续根据需要添加）
        // Map<PlatformMaterialKey, List<CqWldygxEntryDO>> wldygxEntriesMap = cqWldygxService.getAllWldygxEntries();
        // log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个物料对应关系]", wldygxEntriesMap.size());
        
        // 2. 批量查询所有已审核物料列表
        List<CqBdMaterialDO> allApprovedMaterials = cqBdMaterialService.getAllMaterialsWithGroup();
        cacheData.setAllApprovedMaterials(allApprovedMaterials);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个已审核物料]", allApprovedMaterials.size());
        
        // 3. 批量查询排除物料编码集合
        Set<String> excludedMaterials = cqPcwlService.getAllExcludedMaterials();
        cacheData.setExcludedMaterials(excludedMaterials);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个排除物料编码]", excludedMaterials.size());
        
        // 4. 批量查询仓库对应关系映射表
        Map<PlatformWarehouseKey, List<CqCkdygxEntryDO>> ckdygxEntriesMap = cqCkdygxService.getAllCkdygxEntries();
        cacheData.setCkdygxEntriesMap(ckdygxEntriesMap);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个仓库对应关系]", ckdygxEntriesMap.size());
        
        // 5. 批量查询委外品牌仓库映射
        Map<OutwarehouseMapKey, List<CqYdOutwarehousemapEntryDO>> outwarehouseMapEntries = cqYdOutwarehousemapService.getAllOutwarehousemapEntries();
        cacheData.setOutwarehouseMapEntries(outwarehouseMapEntries);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个委外品牌仓库映射]", outwarehouseMapEntries.size());
        
        // 6. 批量查询仓库主数据
        Map<Long, CqBdWarehouseDO> warehouseMap = cqBdWarehouseService.getAllWarehousesMap();
        cacheData.setWarehouseMap(warehouseMap);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个仓库主数据]", warehouseMap.size());
        
        // 7. 批量查询直营店仓库映射
        Map<DirectwarehouseKey, List<CqDirectwarehouseDO>> directwarehouseMap = cqDirectwarehouseService.getAllDirectwarehouseEntries();
        cacheData.setDirectwarehouseMap(directwarehouseMap);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个直营店仓库映射]", directwarehouseMap.size());
        
        // 8. 批量查询所有已审核的销售BOM数据
        Map<String, CqBomWithEntriesDTO> bomStructuresMap = cqBomService.getAllAuditedBomStructuresMap();
        cacheData.setBomStructuresMap(bomStructuresMap);
        log.info("[loadWholesaleSplitSettleCacheData][获取到 {} 个已审核的销售BOM数据]", bomStructuresMap.size());
        
        long endTime = System.currentTimeMillis();
        log.info("[loadWholesaleSplitSettleCacheData][缓存数据加载完成，耗时: {}ms]", endTime - startTime);
        
        return cacheData;
    }

    /**
     * 处理单个批发通知单的拆单明细结算
     *
     * @param bill 批发通知单详情DTO
     * @param cacheData 拆单结算缓存数据
     */
    private void processWholesaleNoticeBillWithSplitDetailSettle(CqWholesaleNoticeBillDetailDTO bill, SplitSettleCacheData cacheData) {
        // 获取主单据
        CqWholesaleNoticeBillDO mainBill = bill.getBill();
        String platform = mainBill.getPlatform(); // deliveryDetailSettleService.convertPlatform(mainBill.getPlatform());
        // 获取明细
        List<CqWholesaleNoticeBillOMSEntryDO> entries = bill.getEntries();
        if (CollectionUtils.isEmpty(entries)) { 
            log.info("[processWholesaleNoticeBillWithSplitDetailSettle][单据编号: {} 没有可结算的批发通知单明细]", mainBill.getBillNo());
            return;
        }
        
        // 创建批量更新DTO
        BatchWholesaleNoticeSplitEntrySettleUpdateDTO batchUpdateDTO = new BatchWholesaleNoticeSplitEntrySettleUpdateDTO();   
        batchUpdateDTO.setBillNo(mainBill.getBillNo());
        
        // 销售组织仓库不存在
        boolean hasNotExistSaleOrgStock = false;
        // 物料对应关系重复
        boolean hasMaterialRelationRepeat = false;
        // 库存组织仓库不存在
        boolean hasNotExistInvOrgStock = false;
        // 库存组织不存在
        boolean hasNotExistInvOrg = false;
        // 整单剔除物料
        boolean isWholeOrderExcludeMaterial = false;

        // 创建拆单明细更新列表
        List<CqWholesaleNoticeSplitEntryDO> entryUpdateDTOs = new ArrayList<>(entries.size());
        int seq = 1;
        // 遍历明细
        for (CqWholesaleNoticeBillOMSEntryDO parentEntry : entries) {
            
            // 是否剔除物料
            boolean excludeMaterial = parentEntry.getExcludeMaterialOms();
            if (excludeMaterial) {
                continue;
            }

            // 是否组装品
            boolean isBom = parentEntry.getIsBom();
            if (isBom) {
                // 组装品
                CqBomWithEntriesDTO bomWithEntriesDTO = cacheData.getBomStructuresMap().get(parentEntry.getGoodsNum());
                if(bomWithEntriesDTO != null) {
                    List<CqBomEntryDO> bomEntries = bomWithEntriesDTO.getEntries();
                    // 遍历子物料进行金额分配，按照seq升序排序
                    bomEntries.sort(Comparator.comparing(CqBomEntryDO::getSeq));
                    for (CqBomEntryDO childEntry : bomEntries) {
                        // 比例（主商品拆分比例）
                        BigDecimal rate = childEntry.getProp() != null ? childEntry.getProp() : BigDecimal.ZERO;

                        // 创建拆单明细更新DTO对象
                        CqWholesaleNoticeSplitEntryDO entryUpdateDTO = new CqWholesaleNoticeSplitEntryDO();
                        // 设置关联的批发通知单ID
                        entryUpdateDTO.setBillId(mainBill.getId());
                        // 使用IdWorker生成唯一的明细ID
                        entryUpdateDTO.setEntryId(IdWorker.getId());
                        // 设置商品货号(OMS明细)
                        entryUpdateDTO.setOmsGoodsNo(parentEntry.getGoodsNum());
                        // 设置序号(OMS明细)
                        entryUpdateDTO.setOmsRowNum(parentEntry.getSeq());
                        // 设置序号并自增，用于排序
                        entryUpdateDTO.setSeq(seq++);
                        // 物料ID - 设置拆分后的物料ID，从组合物料的子物料中获取
                        entryUpdateDTO.setSplitMaterialId(childEntry.getMaterialId());
                        // 根据物料ID获取物料信息，用于设置品牌ID
                        // CqBdMaterialDO subMaterial = cqBdMaterialService.getMaterial(childEntry.getMaterialId());
                        CqBdMaterialDO subMaterial = cacheData.getAllApprovedMaterials().stream()
                                .filter(material -> material.getId().equals(childEntry.getMaterialId()))
                                .findFirst()
                                .orElse(null);
                        if (subMaterial != null) {
                            boolean isExcludedMaterial = cacheData.getExcludedMaterials().contains(subMaterial.getNumber());
                            if(isExcludedMaterial) {
                                // 剔除物料
                                entryUpdateDTO.setExcludeMatSplit(true);
                            } else {
                                // 物料存在，设置对应的品牌ID
                                entryUpdateDTO.setBrandId(subMaterial.getBrandId());
                                // 设置物料类型
                                entryUpdateDTO.setMatGroupId(subMaterial.getMatGroupId());
                                // 设置产品类型
                                entryUpdateDTO.setProductType(subMaterial.getProType());
                            }
                        } else {
                            // 物料不存在，记录错误日志并设置品牌ID为null
                            log.error("[processWholesaleNoticeBillWithSplitDetailSettle][单据编号: {} 物料编号: {} 未找到物料信息]", mainBill.getBillNo(), childEntry.getMaterialId());
                            entryUpdateDTO.setBrandId(0L);
                            entryUpdateDTO.setMatGroupId(0L);
                            entryUpdateDTO.setProductType("");
                        }
                        // 数量
                        // 获取子物料的单位数量
                        BigDecimal subItemQty = childEntry.getQty();
                        // 计算拆分后的总数量 = 子物料单位数量 × 主物料数量
                        entryUpdateDTO.setSplitQty(subItemQty.multiply(parentEntry.getQty()));
                        // // 价税合计 = 主物料价税合计 × 拆分比例
                        // // 根据组装品的拆分比例计算子物料的价税合计金额
                        // entryUpdateDTO.setTotalTaxAmt(parentEntry.getAmount().multiply(rate));
                        // // 单价 = 主物料价税合计 ÷ 拆分后的总数量，保留2位小数，四舍五入
                        // entryUpdateDTO.setSplitPrice(parentEntry.getAmount().divide(entryUpdateDTO.getSplitQty(), 2, BigDecimal.ROUND_HALF_UP));

                        // 若E3批发通知单/批发退货单 实际金额=0，且"是否组装商品"=是， 取 物料编码 对应子商品 "单价" * "数量" 
                        if(parentEntry.getAmount() != null && parentEntry.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                            // 单价
                            entryUpdateDTO.setSplitPrice(childEntry.getPrice());
                            // 总金额
                            entryUpdateDTO.setTotalTaxAmt(childEntry.getPrice().multiply(entryUpdateDTO.getSplitQty())); 
                        } else {
                            // 单价
                            entryUpdateDTO.setSplitPrice(BigDecimal.ZERO);
                            // 总金额
                            entryUpdateDTO.setTotalTaxAmt(BigDecimal.ZERO);
                        }
                        // 总金额等于0，为赠品
                        if(entryUpdateDTO.getTotalTaxAmt().compareTo(BigDecimal.ZERO) == 0) {
                            entryUpdateDTO.setIsGift(true);
                        }

                        // 配置仓库设置信息
                        configureWarehouseSettings(entryUpdateDTO, mainBill, mainBill.getCqCustomerId(), cacheData);

                        if (entryUpdateDTO.getSplitSalOrgStockId() == null || entryUpdateDTO.getSplitSalOrgStockId() == 0) {
                            // 销售组织仓库不存在
                            hasNotExistSaleOrgStock = true;
                        }

                        if (entryUpdateDTO.getSplitInvOrgStockId() == null || entryUpdateDTO.getSplitInvOrgStockId() == 0) {
                            // 库存组织仓库不存在
                            hasNotExistInvOrgStock = true;
                        }

                        if (entryUpdateDTO.getInvOrgId() == null || entryUpdateDTO.getInvOrgId() == 0) {
                            // 库存组织不存在
                            hasNotExistInvOrg = true;
                        }

                        // 添加到批量更新列表
                        entryUpdateDTOs.add(entryUpdateDTO);
                    }
                } else {
                    hasMaterialRelationRepeat = true;
                }
            } else {
                // 非组装品
                // 创建拆单明细更新DTO对象
                CqWholesaleNoticeSplitEntryDO entryUpdateDTO = new CqWholesaleNoticeSplitEntryDO();
                // 设置关联的批发通知单ID
                entryUpdateDTO.setBillId(mainBill.getId());
                // 使用IdWorker生成唯一的明细ID
                entryUpdateDTO.setEntryId(IdWorker.getId());                
                // 设置商品货号(OMS明细)
                entryUpdateDTO.setOmsGoodsNo(parentEntry.getGoodsNum());
                // 设置序号(OMS明细)
                entryUpdateDTO.setOmsRowNum(parentEntry.getSeq());
                // 设置序号并自增，用于排序
                entryUpdateDTO.setSeq(seq++);
                // 物料ID
                entryUpdateDTO.setSplitMaterialId(parentEntry.getSingleMaterialId());
                // 根据物料ID获取物料信息，用于设置品牌ID
                // CqBdMaterialDO parentMaterial = cqBdMaterialService.getMaterial(parentEntry.getSingleMaterialId());
                CqBdMaterialDO parentMaterial = cacheData.getAllApprovedMaterials().stream()
                                .filter(material -> material.getId().equals(parentEntry.getSingleMaterialId()))
                                .findFirst()
                                .orElse(null);
                if (parentMaterial != null) {
                    boolean isExcludedMaterial = cacheData.getExcludedMaterials().contains(parentMaterial.getNumber());
                    if(isExcludedMaterial) {
                        // 剔除物料
                        entryUpdateDTO.setExcludeMatSplit(true);
                    } else {
                        // 物料存在，设置对应的品牌ID
                        entryUpdateDTO.setBrandId(parentMaterial.getBrandId());
                        // 设置物料类型
                        entryUpdateDTO.setMatGroupId(parentMaterial.getMatGroupId());
                        // 设置产品类型
                        entryUpdateDTO.setProductType(parentMaterial.getProType());
                    }
                } else {
                    // 物料不存在，记录错误日志并设置品牌ID为null
                    log.error("[processWholesaleNoticeBillWithSplitDetailSettle][单据编号: {} 物料编号: {} 未找到物料信息]", mainBill.getBillNo(), parentEntry.getSingleMaterialId());
                    entryUpdateDTO.setBrandId(0L);
                    entryUpdateDTO.setMatGroupId(0L);
                    entryUpdateDTO.setProductType("");
                }
                // 数量
                entryUpdateDTO.setSplitQty(parentEntry.getQty());
                // 处理非组装品的价格和金额
                processNonBomPriceAndAmount(entryUpdateDTO, parentEntry, mainBill);
                // 是否赠品
                // 价税合计=0为是
                if(entryUpdateDTO.getTotalTaxAmt().compareTo(BigDecimal.ZERO) == 0) {
                    entryUpdateDTO.setIsGift(true);
                }
                // 配置仓库设置信息
                configureWarehouseSettings(entryUpdateDTO, mainBill, mainBill.getCqCustomerId(), cacheData);

                if (entryUpdateDTO.getSplitSalOrgStockId() == null || entryUpdateDTO.getSplitSalOrgStockId() == 0) {
                    // 销售组织仓库不存在
                    hasNotExistSaleOrgStock = true;
                }

                if (entryUpdateDTO.getSplitInvOrgStockId() == null || entryUpdateDTO.getSplitInvOrgStockId() == 0) {
                    // 库存组织仓库不存在
                    hasNotExistInvOrgStock = true;
                }

                if (entryUpdateDTO.getInvOrgId() == null || entryUpdateDTO.getInvOrgId() == 0) {
                    // 库存组织不存在
                    hasNotExistInvOrg = true;
                }

                // 添加到批量更新列表
                entryUpdateDTOs.add(entryUpdateDTO);
            }
        }

        // 设置批量更新对象的明细列表
        batchUpdateDTO.setEntries(entryUpdateDTOs);

        // 如果全部明细都是剔除物料，则整单剔除物料
        if(entryUpdateDTOs.stream().allMatch(entry -> entry.getExcludeMatSplit() != null && entry.getExcludeMatSplit())) {
            isWholeOrderExcludeMaterial = true;
        }

        if (hasNotExistSaleOrgStock || hasMaterialRelationRepeat || hasNotExistInvOrgStock || hasNotExistInvOrg || isWholeOrderExcludeMaterial) {
            // 销售组织仓库不存在或物料对应关系重复，保持拆单明细待结算状态
            batchUpdateDTO.setMainSettleStatus("3");
        } else {
            // 所有物料处理正常，进入拆单明细待结算
            batchUpdateDTO.setMainSettleStatus("4");
        }
        
        // 设置其他标识
        // 设置物料对应关系重复标识
        batchUpdateDTO.setIsMatRepeat(hasMaterialRelationRepeat);
        // 设置销售组织仓库不存在标识
        batchUpdateDTO.setIsNotExistSaleOrgStock(hasNotExistSaleOrgStock);
        // 设置库存组织仓库不存在标识
        batchUpdateDTO.setIsNotExistInvOrgStock(hasNotExistInvOrgStock);
        // 设置库存组织不存在标识
        batchUpdateDTO.setIsNotExistInvOrg(hasNotExistInvOrg);
        // 设置整单剔除物料标识
        batchUpdateDTO.setIsWholeOrderExcludeMaterial(isWholeOrderExcludeMaterial);

        // 调用批量更新方法
        boolean updated = cqWholesaleNoticeBillService.batchUpdateSplitEntries(batchUpdateDTO);
        if (!updated) {
            log.error("[processWholesaleNoticeBillWithSplitDetailSettle][更新批发通知单拆单明细结算信息失败，单据编号: {}]", mainBill.getBillNo());
        } else {
            log.info("[processWholesaleNoticeBillWithSplitDetailSettle][更新批发通知单拆单明细结算信息成功，单据编号: {}, 明细数量: {}, 下一步状态: {}]", 
                mainBill.getBillNo(), entryUpdateDTOs.size(), batchUpdateDTO.getMainSettleStatus());
        }
        
    }

    // /**
    //  * 设置销售组织仓库
    //  *
    //  * @param entryUpdateDTO 拆单明细更新DTO
    //  * @param cqWarehouseId 苍穹仓库ID
    //  * @param brandId 品牌ID
    //  * @param splitCustomerId 拆单客户ID
    //  */
    // private void setSplitEntrySaleOrgStock(CqWholesaleNoticeSplitEntryDO entryUpdateDTO, 
    //                          Long cqWarehouseId, Long brandId, Long splitCustomerId) {

    //     List<CqYdOutwarehousemapEntryDO> cqYdOutwarehousemapEntryDOs = cqYdOutwarehousemapService.getOutwarehousemapEntriesByCondition(cqWarehouseId, brandId , splitCustomerId);
    //     if(cqYdOutwarehousemapEntryDOs != null && cqYdOutwarehousemapEntryDOs.size() > 0) {
    //         entryUpdateDTO.setSplitSalOrgStockId(cqYdOutwarehousemapEntryDOs.get(0).getActuralWarehouseId());
    //     }
    // }

    /**
     * 处理非组装品的价格和金额
     *
     * @param entryUpdateDTO 拆单明细更新DTO
     * @param parentEntry 父级明细
     * @param mainBill 主单据
     */
    private void processNonBomPriceAndAmount(CqWholesaleNoticeSplitEntryDO entryUpdateDTO, 
                                           CqWholesaleNoticeBillOMSEntryDO parentEntry,
                                           CqWholesaleNoticeBillDO mainBill) {
        // 价税合计（若E3批发通知单/批发退货单 实际金额≠0，则取实际金额）
        if(parentEntry.getAmount() != null && parentEntry.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            entryUpdateDTO.setTotalTaxAmt(parentEntry.getAmount());  
            // 单价
            entryUpdateDTO.setSplitPrice(parentEntry.getAmount().divide(entryUpdateDTO.getSplitQty(), 2, BigDecimal.ROUND_HALF_UP));                
        } else {
            // 获取当前有效的经销商供货价格
            // 若E3批发通知单/批发退货单 实际金额=0，且"是否组装商品"=否， 
            // 将物料编码+组织编码+苍穹客户编码+业务日期/退货日期 匹配 {经销商供货价格表}，取对应组织+客户+期间+物料 交易单价
            CqOrgCusPriceBillEntryDO dealerPrice = cqOrgCusPriceBillService.getCurrentValidPrice(
                parentEntry.getSingleMaterialId(),
                mainBill.getCqCustomerId(), 
                mainBill.getOrgId(), 
                mainBill.getBizDate());  

            if(dealerPrice == null) {
                log.error("[processNonBomPriceAndAmount][单据编号: {} 物料编号: {} 拆单客户ID: {} 销售组织ID: {} 交货日期: {} 当前有效价格为空]", 
                    mainBill.getBillNo(), parentEntry.getSingleMaterialId(), mainBill.getCqCustomerId(), 
                    mainBill.getOrgId(), mainBill.getBizDate());
                // deliveryDetailEntry.setDistPriceNotExist(1);
            } else {
                // 单价
                entryUpdateDTO.setSplitPrice(dealerPrice.getSalePrice());
                // 总金额
                entryUpdateDTO.setTotalTaxAmt(dealerPrice.getSalePrice().multiply(parentEntry.getQty()));
                return;
            }

            entryUpdateDTO.setTotalTaxAmt(BigDecimal.ZERO);
        }
    }

    /**
     * 解析结算任务参数
     * 
     * @param param JSON格式的参数字符串，支持格式：{"billNos": ["单号1", "单号2", ...]}
     * @return 解析后的参数对象
     */
    @Override
    public WholesalePfxhdSettleParamsDTO parseSettleParams(String param) {
        WholesalePfxhdSettleParamsDTO paramsDTO = new WholesalePfxhdSettleParamsDTO();
        
        if (StrUtil.isNotBlank(param)) {
            try {
                // 解析JSON参数
                JSONObject jsonParam = JSONUtil.parseObj(param);
                paramsDTO.setBillNos(jsonParam.getBeanList("billNos", String.class));
                
                log.info("[parseSettleParams][参数解析成功: billNos={}]", 
                        paramsDTO.getBillNos() != null ? paramsDTO.getBillNos().size() : 0);
            } catch (Exception e) {
                log.warn("[parseSettleParams][解析参数失败: {}]", param, e);
            }
        }
        
        return paramsDTO;
    }

    /**
     * 配置仓库设置信息
     * 
     * 该方法统一处理交付明细条目的仓库相关设置，包括：
     * 1. 库存组织仓库ID设置：根据平台、发货仓库、品牌、物料类型、产品类型匹配仓库对应关系
     * 2. 库存组织ID设置：根据库存组织仓库ID获取对应的仓库创建组织
     * 3. 销售组织仓库ID设置：根据库存组织仓库、品牌、客户等信息匹配委外品牌仓库映射
     * 4. 仓库类型设置：根据平台和仓库ID设置仓库类型
     * 
     * 业务逻辑说明：
     * - 库存组织仓库匹配：平台+发货仓库编码+品牌+物料类型+产品类型 -> 苍穹仓库ID
     * - 销售组织仓库匹配：库存组织仓库+品牌+客户+物料类型+产品类型 -> 实际仓库ID
     * - 支持回退策略：当包含客户的查询失败时，使用客户ID=0的通用配置
     * 
     * @param entryDO 交付明细条目对象，用于设置仓库相关字段
     * @param mainBill 主单据信息，包含平台、发货仓库编码等基础信息
     * @param splitCustomerId 拆单客户ID，用于销售组织仓库映射查询
     * @param cacheData 拆单结算缓存数据，包含各种仓库映射关系缓存
     */
    private void configureWarehouseSettings(CqWholesaleNoticeSplitEntryDO entryDO,
                                           CqWholesaleNoticeBillDO mainBill, 
                                           Long splitCustomerId,
                                           SplitSettleCacheData cacheData) {
        // ========== 设置库存组织仓库ID ==========
        // 业务逻辑：将{基本信息-发货仓库编码}+{拆单明细-品牌}+{拆单明细-物料类型}+{拆单明细-产品类型}
        // 匹配【仓库对应表】{业务平台对应仓库}+{品牌}+{物料类型}+{产品类型}取{苍穹仓库}
        // 注意：若【仓库对应表】品牌、物料类型、产品类型包含多个，则同一个字段多个值视为或的关系
        
        // 1. 构建仓库对应关系查询键
        PlatformWarehouseKey warehouseKey = new PlatformWarehouseKey(
                mainBill.getPlatform(),           // 业务平台
                mainBill.getWarehouseNo(),        // 发货仓库编码
                entryDO.getProductType(),         // 产品类型
                entryDO.getBrandId(),             // 品牌ID
                entryDO.getMatGroupId()           // 物料类型ID
        );
        
        // 2. 从缓存中获取仓库对应关系
        List<CqCkdygxEntryDO> ckdygxEntries = cacheData.getCkdygxEntriesMap().get(warehouseKey);
        
        if (ckdygxEntries != null && !ckdygxEntries.isEmpty()) {
            // 3. 获取第一个匹配的仓库对应关系（通常只有一个）
            CqCkdygxEntryDO ckdygxEntry = ckdygxEntries.get(0);
            
            // 4. 设置库存组织仓库ID为匹配到的苍穹仓库ID
            entryDO.setSplitInvOrgStockId(ckdygxEntry.getWarehouseId());

            // 5. 根据库存组织仓库ID获取对应的仓库信息，设置库存组织ID
            CqBdWarehouseDO cqBdWarehouseDO = cacheData.getWarehouseMap().get(ckdygxEntry.getWarehouseId());
            if (cqBdWarehouseDO != null) {
                // 使用仓库的创建组织作为库存组织ID
                entryDO.setInvOrgId(cqBdWarehouseDO.getCreateOrgId());
            } else {
                // 仓库信息未找到，记录警告并设置默认值
                log.warn("[configureWarehouseSettings][仓库ID: {} 在缓存中未找到，设置库存组织为默认值0]", ckdygxEntry.getWarehouseId());
                entryDO.setInvOrgId(0L);
            }

            // ========== 设置销售组织仓库ID ==========
            // 业务逻辑：将{拆单明细-库存组织仓库编码}+{拆单明细-品牌}+{拆单明细-物料类型}+{拆单明细-产品类型}+{客户}
            // 匹配【品牌品类仓库映射表】{库存组织仓库编码}+{品牌}+{物料类型}+{产品类型}+{客户}取{结算组织仓库编码}
            // 注意：若{拆单明细-客户}在映射表中找不到对应客户，则取其他维度匹配但客户为空的那条数据
            // 注意：若【仓库对应表】品牌、物料类型、产品类型包含多个，则同一个字段多个值视为或的关系
            
            // 6. 获取委外品牌仓库映射关系缓存
            Map<OutwarehouseMapKey, List<CqYdOutwarehousemapEntryDO>> outwarehouseMapEntries = cacheData.getOutwarehouseMapEntries();
            
            // 7. 构建委外仓库映射查询键（包含客户ID）
            OutwarehouseMapKey outwarehouseMapKey = new OutwarehouseMapKey(
                entryDO.getSplitInvOrgStockId(),         // 库存组织仓库ID
                entryDO.getBrandId(),               // 品牌ID
                splitCustomerId,                    // 拆单客户ID
                entryDO.getMatGroupId(),            // 物料类型ID
                entryDO.getProductType()            // 产品类型
            );
            
            // 8. 尝试根据完整条件（包含客户）查找映射关系
            List<CqYdOutwarehousemapEntryDO> outwarehouseMapEntry = outwarehouseMapEntries.get(outwarehouseMapKey);
            
            if (outwarehouseMapEntry != null && !outwarehouseMapEntry.isEmpty()) {
                // 找到匹配的映射关系，设置销售组织仓库ID
                entryDO.setSplitSalOrgStockId(outwarehouseMapEntry.get(0).getActuralWarehouseId());
            } else {
                // 9. 若包含客户的查询未找到结果，则使用回退策略：客户ID设为0进行查询
                // 这是为了处理客户在映射表中不存在的情况，使用其他维度匹配但客户为空的数据
                OutwarehouseMapKey fallbackKey = new OutwarehouseMapKey(
                    entryDO.getSplitInvOrgStockId(),     // 库存组织仓库ID
                    entryDO.getBrandId(),           // 品牌ID
                    0L,                             // 客户ID设为0（表示不限定客户）
                    entryDO.getMatGroupId(),        // 物料类型ID
                    entryDO.getProductType()        // 产品类型
                );
                
                // 10. 使用回退键再次查询
                outwarehouseMapEntry = outwarehouseMapEntries.get(fallbackKey);
                
                if (outwarehouseMapEntry != null && !outwarehouseMapEntry.isEmpty()) {
                    // 找到回退匹配的映射关系，设置销售组织仓库ID
                    entryDO.setSplitSalOrgStockId(outwarehouseMapEntry.get(0).getActuralWarehouseId());
                } else {
                    // 完全未找到匹配的映射关系，记录警告并设置默认值
                    log.warn("[configureWarehouseSettings][销售组织仓库映射未找到，查询键: {}，设置销售组织仓库为默认值0]", outwarehouseMapKey);
                    entryDO.setSplitSalOrgStockId(0L);
                }
            }
        } else {
            // 11. 仓库对应关系未找到的情况处理
            // 记录警告并设置所有相关字段为默认值
            log.warn("[configureWarehouseSettings][仓库对应关系未找到，查询键: {}，设置库存组织和销售组织仓库为默认值0]", warehouseKey);
            // 设置库存组织ID为默认值0
            entryDO.setInvOrgId(0L);
            // 设置库存组织仓库ID为默认值0
            entryDO.setSplitInvOrgStockId(0L);
            // 设置销售组织仓库ID为默认值0
            entryDO.setSplitSalOrgStockId(0L);
        }
    }
        
}

package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.*;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.*;
import cn.iocoder.yudao.module.sap.dal.mysql.cangqiong.*;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWholesaleReturnBillService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 批发退货单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
@DS("cq_scm")
public class CqWholesaleReturnBillServiceImpl implements CqWholesaleReturnBillService {

    @Resource
    private CqWholesaleReturnBillMapper wholesaleReturnBillMapper;

    @Resource
    private CqWholesaleReturnBillEntryMapper wholesaleReturnBillEntryMapper;
    
    @Resource
    private CqWholesaleReturnBillOMSEntryMapper wholesaleReturnBillOMSEntryMapper;

    @Resource
    private CqWholesaleReturnSplitEntryMapper wholesaleReturnSplitEntryMapper;

    @Override
    public CqWholesaleReturnBillDO getWholesaleReturnBill(Long id) {
        return wholesaleReturnBillMapper.selectById(id);
    }

    @Override
    public CqWholesaleReturnBillDO getWholesaleReturnBillByBillNo(String billNo) {
        return wholesaleReturnBillMapper.selectByBillNo(billNo);
    }

    @Override
    public List<CqWholesaleReturnBillOMSEntryDO> getWholesaleReturnBillEntries(Long mainId) {
        return wholesaleReturnBillOMSEntryMapper.selectListByBillId(mainId);
    }

    @Override
    public List<CqWholesaleReturnSplitEntryDO> getWholesaleReturnSplitEntries(Long mainId) {
        return wholesaleReturnSplitEntryMapper.selectListByMainId(mainId);
    }

    @Override
    public List<CqWholesaleReturnBillDO> getWholesaleReturnBillList(String billNo, String platform, 
                                                                  String channelNo, String customerNo) {
        return wholesaleReturnBillMapper.selectList(billNo, platform, channelNo, customerNo);
    }

    @Override
    public boolean existsByBillNo(String billNo) {
        return wholesaleReturnBillMapper.countByBillNo(billNo) > 0;
    }
    
    @Override
    public CqWholesaleReturnBillDetailDTO getWholesaleReturnBillDetail(String billNo) {
        // 获取主单据
        CqWholesaleReturnBillDO bill = getWholesaleReturnBillByBillNo(billNo);
        if (bill == null) {
            return null;
        }
        
        // 获取明细
        List<CqWholesaleReturnBillOMSEntryDO> entries = getWholesaleReturnBillEntries(bill.getId());
        // 获取拆分明细
        List<CqWholesaleReturnSplitEntryDO> splitEntries = getWholesaleReturnSplitEntries(bill.getId());
        
        // 组装DTO
        CqWholesaleReturnBillDetailDTO detailDTO = new CqWholesaleReturnBillDetailDTO();
        detailDTO.setBill(bill);
        detailDTO.setEntries(entries);
        detailDTO.setSplitEntries(splitEntries);
        
        return detailDTO;
    }
    
    @Override
    public List<CqWholesaleReturnBillDetailDTO> getWholesaleReturnBillDetailsBySettleStatuses(String billNo, List<String> settleStatuses) {
        // 查询符合条件的主单据
        List<CqWholesaleReturnBillDO> bills = wholesaleReturnBillMapper.selectListByBillNoAndSettleStatuses(billNo, settleStatuses);
        if (CollectionUtils.isEmpty(bills)) {
            return Collections.emptyList();
        }
        
        // 组装详情DTO
        List<CqWholesaleReturnBillDetailDTO> details = new ArrayList<>(bills.size());
        for (CqWholesaleReturnBillDO bill : bills) {
            CqWholesaleReturnBillDetailDTO detail = new CqWholesaleReturnBillDetailDTO();
            detail.setBill(bill);
            detail.setEntries(getWholesaleReturnBillEntries(bill.getId()));
            detail.setSplitEntries(getWholesaleReturnSplitEntries(bill.getId()));
            details.add(detail);
        }
        
        return details;
    }
    
    @Override
    public List<CqWholesaleReturnBillDetailDTO> getWholesaleReturnBillDetailsBySettleStatuses(List<String> billNos, List<String> settleStatuses) {
        // 查询符合条件的主单据
        List<CqWholesaleReturnBillDO> bills = wholesaleReturnBillMapper.selectListByBillNosAndSettleStatuses(billNos, settleStatuses);
        if (CollectionUtils.isEmpty(bills)) {
            return Collections.emptyList();
        }
        
        // 组装详情DTO
        List<CqWholesaleReturnBillDetailDTO> details = new ArrayList<>(bills.size());
        for (CqWholesaleReturnBillDO bill : bills) {
            CqWholesaleReturnBillDetailDTO detail = new CqWholesaleReturnBillDetailDTO();
            detail.setBill(bill);
            detail.setEntries(getWholesaleReturnBillEntries(bill.getId()));
            detail.setSplitEntries(getWholesaleReturnSplitEntries(bill.getId()));
            details.add(detail);
        }
        
        return details;
    }
    
    @Override
    public boolean updateWholesaleReturnBillSettleInfoByBillNo(String billNo, WholesaleReturnBillSettleUpdateDTO updateDTO) {
        // 1. 根据单据编号查询批发退货单
        CqWholesaleReturnBillDO bill = wholesaleReturnBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[updateWholesaleReturnBillSettleInfoByBillNo][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 2. 创建更新对象
        CqWholesaleReturnBillDO updateObj = new CqWholesaleReturnBillDO();
        updateObj.setId(bill.getId()); // 设置批发退货单ID
        updateObj.setOrgId(updateDTO.getOrgId()); // 设置组织ID
        updateObj.setNotExistChannelOrg(updateDTO.getNotExistChannelOrg()); // 设置渠道组织是否不存在标志
        updateObj.setCqCustomerId(updateDTO.getCqCustomerId()); // 设置苍穹客户ID
        updateObj.setNotExistCustomer(updateDTO.getNotExistCustomer()); // 设置客户是否不存在标志
        updateObj.setIsBrandSplitBill(updateDTO.getIsBrandSplitBill()); // 设置是否按品牌分单
        updateObj.setExcludeStock(updateDTO.getExcludeStock()); // 设置是否剔除库存
        updateObj.setSettleStatus(updateDTO.getSettleStatus()); // 设置结算状态
        updateObj.setYdBusinessscene(updateDTO.getYdBusinessscene()); // 设置业务场景

        // 3. 执行更新
        int updated = wholesaleReturnBillMapper.updateById(updateObj);
        if (updated > 0) {
            log.info("[updateWholesaleReturnBillSettleInfoByBillNo][单据编号: {} 结算信息更新成功]", billNo);
            return true;
        } else {
            log.warn("[updateWholesaleReturnBillSettleInfoByBillNo][单据编号: {} 结算信息更新失败]", billNo);
            return false;
        }
    }
    
    @Override
    public boolean updateWholesaleReturnBillEntrySettleInfo(String billNo, Long entryId, WholesaleReturnBillOMSEntrySettleUpdateDTO updateDTO) {
        // 1. 根据单据编号查询批发退货单
        CqWholesaleReturnBillDO bill = wholesaleReturnBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[updateWholesaleReturnBillEntrySettleInfo][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 2. 查询明细条目是否存在
        CqWholesaleReturnBillOMSEntryDO entry = wholesaleReturnBillOMSEntryMapper.selectById(entryId);
        if (entry == null || !entry.getBillId().equals(bill.getId())) {
            log.warn("[updateWholesaleReturnBillEntrySettleInfo][单据编号: {}, 明细ID: {} 不存在或不属于该单据]", billNo, entryId);
            return false;
        }
        
        // 3. 创建更新对象
        CqWholesaleReturnBillOMSEntryDO updateObj = new CqWholesaleReturnBillOMSEntryDO();
        updateObj.setEntryId(entryId);
        updateObj.setSingleMaterialId(updateDTO.getSingleMaterialId());
        updateObj.setNotExistMaterialOms(updateDTO.getNotExistMaterialOms());
        updateObj.setIsBom(updateDTO.getIsBom());
        updateObj.setIsMaterialRelationRepeat(updateDTO.getIsMaterialRelationRepeat());
        
        // 4. 执行更新
        int updated = wholesaleReturnBillOMSEntryMapper.updateById(updateObj);
        if (updated > 0) {
            log.info("[updateWholesaleReturnBillEntrySettleInfo][单据编号: {}, 明细ID: {} 结算信息更新成功]", billNo, entryId);
            return true;
        } else {
            log.warn("[updateWholesaleReturnBillEntrySettleInfo][单据编号: {}, 明细ID: {} 结算信息更新失败]", billNo, entryId);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateWholesaleReturnBillEntriesSettleInfo(BatchWholesaleReturnBillEntrySettleUpdateDTO batchUpdateDTO) {
        // 1. 获取参数
        String billNo = batchUpdateDTO.getBillNo();
        String mainSettleStatus = batchUpdateDTO.getMainSettleStatus();
        List<WholesaleReturnBillOMSEntrySettleUpdateDTO> entries = batchUpdateDTO.getEntries();
        
        // 2. 参数校验
        if (billNo == null || billNo.isEmpty()) {
            log.warn("[batchUpdateWholesaleReturnBillEntriesSettleInfo][单据编号为空]");
            return false;
        }
        
        if (CollectionUtils.isEmpty(entries)) {
            log.warn("[batchUpdateWholesaleReturnBillEntriesSettleInfo][明细条目为空]");
            return false;
        }
        
        // 3. 根据单据编号查询批发退货单
        CqWholesaleReturnBillDO bill = wholesaleReturnBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[batchUpdateWholesaleReturnBillEntriesSettleInfo][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 4. 更新明细条目
        boolean allSuccess = true;
        for (WholesaleReturnBillOMSEntrySettleUpdateDTO entryUpdate : entries) {
            // 创建更新对象
            CqWholesaleReturnBillOMSEntryDO updateObj = new CqWholesaleReturnBillOMSEntryDO();
            updateObj.setEntryId(entryUpdate.getEntryId());
            updateObj.setSingleMaterialId(entryUpdate.getSingleMaterialId());
            updateObj.setNotExistMaterialOms(entryUpdate.getNotExistMaterialOms());
            updateObj.setIsBom(entryUpdate.getIsBom());
            updateObj.setIsMaterialRelationRepeat(entryUpdate.getIsMaterialRelationRepeat());
            updateObj.setIsErrorBill(entryUpdate.getIsErrorBill());
            updateObj.setErrorReason(entryUpdate.getErrorReason());
            updateObj.setExcludeMaterialOms(entryUpdate.getIsExcludedMaterialOms());

            // 执行更新
            int updated = wholesaleReturnBillOMSEntryMapper.updateById(updateObj);
            if (updated <= 0) {
                log.warn("[batchUpdateWholesaleReturnBillEntriesSettleInfo][单据编号: {}, 明细ID: {} 结算信息更新失败]", 
                        billNo, entryUpdate.getEntryId());
                allSuccess = false;
            }
        }
        
        // 5. 如果主单据状态需要更新
        if (mainSettleStatus != null && !mainSettleStatus.isEmpty()) {
            CqWholesaleReturnBillDO updateObj = new CqWholesaleReturnBillDO();
            updateObj.setId(bill.getId());
            // 设置结算状态
            updateObj.setSettleStatus(mainSettleStatus);
            // 设置物料关系重复标识
            updateObj.setMatRepeat(batchUpdateDTO.getIsMatRepeat());
            // 设置修改时间为当前时间
            updateObj.setModifyTime(LocalDateTime.now());
            // 设置物料不存在标识
            updateObj.setNotExistMaterial(batchUpdateDTO.getIsNotExistMaterial());
            // 设置整单剔除物料标识
            updateObj.setExcludeMaterial(batchUpdateDTO.getIsWholeOrderExcludeMaterial());

            int updated = wholesaleReturnBillMapper.updateById(updateObj);
            if (updated <= 0) {
                log.warn("[batchUpdateWholesaleReturnBillEntriesSettleInfo][单据编号: {} 状态更新失败]", billNo);
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSplitEntries(BatchWholesaleReturnSplitEntrySettleUpdateDTO batchUpdateDTO) {
        // 记录开始时间，用于性能日志
        long startTime = System.currentTimeMillis();
        
        // 1. 获取参数
        String billNo = batchUpdateDTO.getBillNo();
        String mainSettleStatus = batchUpdateDTO.getMainSettleStatus();
        List<CqWholesaleReturnSplitEntryDO> entries = batchUpdateDTO.getEntries();
        
        log.info("[batchUpdateSplitEntries][开始处理批发退货单拆单明细, 单据编号: {}, 明细数量: {}]",
                billNo, entries != null ? entries.size() : 0);
        
        // 2. 参数校验
        if (billNo == null || billNo.isEmpty()) {
            log.warn("[batchUpdateSplitEntries][单据编号为空]");
            return false;
        }
        
        // 3. 根据单据编号查询批发退货单
        CqWholesaleReturnBillDO bill = wholesaleReturnBillMapper.selectByBillNo(billNo);
        if (bill == null) {
            log.warn("[batchUpdateSplitEntries][单据编号: {} 不存在]", billNo);
            return false;
        }
        
        // 4. 更新主表状态
        boolean updateMainSuccess = true;
        if (mainSettleStatus != null && !mainSettleStatus.isEmpty()) {
            CqWholesaleReturnBillDO updateObj = new CqWholesaleReturnBillDO();
            updateObj.setId(bill.getId());
            updateObj.setSettleStatus(mainSettleStatus);
            updateObj.setModifyTime(LocalDateTime.now());
            // 销售组织仓库不存在
            updateObj.setNotExistSalOrgStock(batchUpdateDTO.getIsNotExistSaleOrgStock());
            // 物料关系重复
            updateObj.setMatRepeat(batchUpdateDTO.getIsMatRepeat());
            // 整单剔除物料
            updateObj.setExcludeMaterial(batchUpdateDTO.getIsWholeOrderExcludeMaterial());
            // 设置库存组织仓库不存在标识
            updateObj.setNotExistInvOrgStock(batchUpdateDTO.getIsNotExistInvOrgStock());
            // 设置库存组织不存在标识
            updateObj.setInvOrgNotExist(batchUpdateDTO.getIsNotExistInvOrg());
            
            int updated = wholesaleReturnBillMapper.updateById(updateObj);
            if (updated <= 0) {
                log.warn("[batchUpdateSplitEntries][单据编号: {} 状态更新失败]", billNo);
                updateMainSuccess = false;
            } else {
                log.info("[batchUpdateSplitEntries][单据编号: {} 状态更新成功, 新状态: {}]", 
                        billNo, mainSettleStatus);
            }
        }
        
        // 5. 删除该主表ID下的所有拆单明细
        int deletedCount = wholesaleReturnSplitEntryMapper.deleteBySplitBillId(bill.getId());
        log.info("[batchUpdateSplitEntries][单据编号: {}, 已删除: {} 条拆单明细记录]", 
                billNo, deletedCount);
        
        // 6. 批量插入新的拆单明细
        boolean insertSuccess = true;
        if (CollectionUtils.isNotEmpty(entries)) {
            try {
                // 使用批量插入优化性能
                wholesaleReturnSplitEntryMapper.insertBatch(entries);
                log.info("[batchUpdateSplitEntries][单据编号: {}, 已插入: {} 条拆单明细记录]", 
                        billNo, entries.size());
            } catch (Exception e) {
                log.warn("[batchUpdateSplitEntries][单据编号: {} 批量插入拆单明细失败: {}]", 
                        billNo, e.getMessage(), e);
                insertSuccess = false;
            }
        }
        
        // 记录处理完成及耗时
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        log.info("[batchUpdateSplitEntries][单据编号: {} 处理完成, 耗时: {}ms, 删除记录数: {}, 插入记录数: {}]", 
                billNo, executionTime, deletedCount, entries != null ? entries.size() : 0);
        
        return updateMainSuccess && insertSuccess;
    }
} 
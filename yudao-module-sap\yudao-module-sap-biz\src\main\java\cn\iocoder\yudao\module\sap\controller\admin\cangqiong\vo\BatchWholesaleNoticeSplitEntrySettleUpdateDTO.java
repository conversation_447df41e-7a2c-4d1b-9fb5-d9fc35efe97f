package cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo;

import lombok.Data;
import java.util.List;

import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWholesaleNoticeSplitEntryDO;

/**
 * 批发通知单拆单明细批量结算更新 DTO
 */
@Data
public class BatchWholesaleNoticeSplitEntrySettleUpdateDTO {
    
    /**
     * 批发通知单编号
     */
    private String billNo;
    
    /**
     * 主单据新的结算状态
     */
    private String mainSettleStatus;

    /**
     * 是否存在物料对应关系重复
     */
    private Boolean isMatRepeat;

    /**
     * 是否存在销售组织仓库不存在
     */
    private Boolean isNotExistSaleOrgStock;

    /**
     * 是否存在库存组织仓库不存在
     */
    private Boolean isNotExistInvOrgStock;

    /**
     * 是否存在库存组织不存在
     */
    private Boolean isNotExistInvOrg; 

    /**
     * 是否整单剔除物料
     */
    private Boolean isWholeOrderExcludeMaterial;
    
    
    /**
     * 拆单明细条目的更新信息
     */
    private List<CqWholesaleNoticeSplitEntryDO> entries;
} 
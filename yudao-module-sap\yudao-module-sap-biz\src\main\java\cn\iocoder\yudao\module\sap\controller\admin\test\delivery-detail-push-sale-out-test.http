### 1. 测试完整参数 - 同时传入单据编号列表和合单规则编码
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "billNos": ["FHMX202412010001", "FHMX202412010002", "FHMX202412010003"],
  "mergeRuleCode": "MERGE_RULE_001"
}

### 2. 测试仅单据编号 - 只传入单据编号列表，不指定合单规则
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "billNos": ["FHMX202412010004", "FHMX202412010005"]
}

### 3. 测试仅合单规则 - 只传入合单规则编码，不指定单据编号
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "mergeRuleCode": "MERGE_RULE_002"
}

### 4. 测试单个单据编号 - 传入单个单据编号
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "billNos": ["FHMX202412010006"]
}

### 5. 测试多个单据编号 - 传入多个单据编号，测试批量处理
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "billNos": ["FHMX202412010007", "FHMX202412010008", "FHMX202412010009", "FHMX202412010010", "FHMX202412010011"]
}

### 6. 测试空参数 - 传入空的JSON对象，使用默认处理逻辑
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
}

### 7. 测试null值参数 - 传入null值的参数
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "billNos": null,
  "mergeRuleCode": null
}

### 8. 测试特殊字符参数 - 包含特殊字符的参数值
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-push-sale-out-test/push-sale-out
Content-Type: application/json

{
  "billNos": ["FHMX_TEST_001", "FHMX-TEST-002"],
  "mergeRuleCode": "MERGE_RULE_SPECIAL_#001"
} 
package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 多库存物料组信息 DO
 * 
 * 表名：tk_yd_mulstockmatgroup
 */
@TableName("tk_yd_mulstockmatgroup")
@Data
public class CqMulstockmatgroupDO {

    /**
     * 主键ID
     */
    @TableId("fpkid")
    private Long pkid;

    /**
     * 分录ID
     */
    @TableField("fentryid")
    private Long entryId;

    /**
     * 物料组ID
     */
    @TableField("fbasedataid")
    private Long matgroupId;
} 
package cn.iocoder.yudao.module.financial.service.salesoutboundorder.impl;

import cn.iocoder.yudao.module.financial.dal.mysql.erp.SalesOutboundOrderEntryMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 销售出库单汇总服务实现类单元测试
 */
@ExtendWith(MockitoExtension.class)
class SalesOutboundOrderSummaryServiceImplTest {

    @Mock
    private SalesOutboundOrderEntryMapper salesOutboundOrderEntryMapper;

    @InjectMocks
    private SalesOutboundOrderSummaryServiceImpl salesOutboundOrderSummaryService;

    /**
     * 测试完全在分界日期之前的查询
     */
    @Test
    void testExecuteSmartBrandQuery_BeforeSplit() {
        // 准备测试数据
        LocalDate startDate = LocalDate.of(2025, 6, 1);
        LocalDate endDate = LocalDate.of(2025, 7, 5);
        List<String> expectedBrandList = Arrays.asList("健安适", "健视佳", "汤臣倍健", "Yep", "倍儿健", "舒百宁");
        List<Map<String, Object>> mockResult = new ArrayList<>();
        
        // 配置Mock
        when(salesOutboundOrderEntryMapper.selectSummaryEntries(eq(startDate), eq(endDate), eq(expectedBrandList)))
            .thenReturn(mockResult);
        
        // 执行测试
        List<Map<String, Object>> result = salesOutboundOrderSummaryService.executeSmartBrandQuery(startDate, endDate);
        
        // 验证结果
        assertSame(mockResult, result);
        verify(salesOutboundOrderEntryMapper, times(1)).selectSummaryEntries(startDate, endDate, expectedBrandList);
    }

    /**
     * 测试完全在分界日期之后的查询
     */
    @Test
    void testExecuteSmartBrandQuery_AfterSplit() {
        // 准备测试数据
        LocalDate startDate = LocalDate.of(2025, 7, 15);
        LocalDate endDate = LocalDate.of(2025, 8, 1);
        List<String> expectedBrandList = Arrays.asList("健安适", "健视佳", "汤臣倍健", "倍儿健", "舒百宁");
        List<Map<String, Object>> mockResult = new ArrayList<>();
        
        // 配置Mock
        when(salesOutboundOrderEntryMapper.selectSummaryEntries(eq(startDate), eq(endDate), eq(expectedBrandList)))
            .thenReturn(mockResult);
        
        // 执行测试
        List<Map<String, Object>> result = salesOutboundOrderSummaryService.executeSmartBrandQuery(startDate, endDate);
        
        // 验证结果
        assertSame(mockResult, result);
        verify(salesOutboundOrderEntryMapper, times(1)).selectSummaryEntries(startDate, endDate, expectedBrandList);
    }

    /**
     * 测试跨越分界日期的查询
     */
    @Test
    void testExecuteSmartBrandQuery_CrossSplit() {
        // 准备测试数据
        LocalDate startDate = LocalDate.of(2025, 7, 5);
        LocalDate endDate = LocalDate.of(2025, 7, 15);
        LocalDate splitEndDate = LocalDate.of(2025, 7, 9);
        LocalDate splitStartDate = LocalDate.of(2025, 7, 10);
        
        List<String> fullBrandList = Arrays.asList("健安适", "健视佳", "汤臣倍健", "Yep", "倍儿健", "舒百宁");
        List<String> brandListWithoutYep = Arrays.asList("健安适", "健视佳", "汤臣倍健", "倍儿健", "舒百宁");
        
        List<Map<String, Object>> beforeSplitData = new ArrayList<>();
        List<Map<String, Object>> afterSplitData = new ArrayList<>();
        
        // 配置Mock
        when(salesOutboundOrderEntryMapper.selectSummaryEntries(eq(startDate), eq(splitEndDate), eq(fullBrandList)))
            .thenReturn(beforeSplitData);
        when(salesOutboundOrderEntryMapper.selectSummaryEntries(eq(splitStartDate), eq(endDate), eq(brandListWithoutYep)))
            .thenReturn(afterSplitData);
        
        // 执行测试
        List<Map<String, Object>> result = salesOutboundOrderSummaryService.executeSmartBrandQuery(startDate, endDate);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(beforeSplitData.size() + afterSplitData.size(), result.size());
        verify(salesOutboundOrderEntryMapper, times(1)).selectSummaryEntries(startDate, splitEndDate, fullBrandList);
        verify(salesOutboundOrderEntryMapper, times(1)).selectSummaryEntries(splitStartDate, endDate, brandListWithoutYep);
    }

    /**
     * 测试边界条件：刚好在分界日期
     */
    @Test
    void testExecuteSmartBrandQuery_OnSplitDate() {
        // 准备测试数据
        LocalDate startDate = LocalDate.of(2025, 7, 10);
        LocalDate endDate = LocalDate.of(2025, 7, 20);
        List<String> expectedBrandList = Arrays.asList("健安适", "健视佳", "汤臣倍健", "倍儿健", "舒百宁");
        List<Map<String, Object>> mockResult = new ArrayList<>();
        
        // 配置Mock
        when(salesOutboundOrderEntryMapper.selectSummaryEntries(eq(startDate), eq(endDate), eq(expectedBrandList)))
            .thenReturn(mockResult);
        
        // 执行测试
        List<Map<String, Object>> result = salesOutboundOrderSummaryService.executeSmartBrandQuery(startDate, endDate);
        
        // 验证结果
        assertSame(mockResult, result);
        verify(salesOutboundOrderEntryMapper, times(1)).selectSummaryEntries(startDate, endDate, expectedBrandList);
    }

    /**
     * 测试异常情况：开始日期为空
     */
    @Test
    void testExecuteSmartBrandQuery_StartDateNull() {
        // 准备测试数据
        LocalDate endDate = LocalDate.of(2025, 7, 15);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            salesOutboundOrderSummaryService.executeSmartBrandQuery(null, endDate);
        });
        
        assertEquals("开始日期和结束日期不能为空", exception.getMessage());
        verify(salesOutboundOrderEntryMapper, never()).selectSummaryEntries(any(), any(), any());
    }

    /**
     * 测试异常情况：结束日期为空
     */
    @Test
    void testExecuteSmartBrandQuery_EndDateNull() {
        // 准备测试数据
        LocalDate startDate = LocalDate.of(2025, 7, 5);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            salesOutboundOrderSummaryService.executeSmartBrandQuery(startDate, null);
        });
        
        assertEquals("开始日期和结束日期不能为空", exception.getMessage());
        verify(salesOutboundOrderEntryMapper, never()).selectSummaryEntries(any(), any(), any());
    }

    /**
     * 测试异常情况：开始日期晚于结束日期
     */
    @Test
    void testExecuteSmartBrandQuery_StartDateAfterEndDate() {
        // 准备测试数据
        LocalDate startDate = LocalDate.of(2025, 7, 20);
        LocalDate endDate = LocalDate.of(2025, 7, 15);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            salesOutboundOrderSummaryService.executeSmartBrandQuery(startDate, endDate);
        });
        
        assertEquals("开始日期不能晚于结束日期", exception.getMessage());
        verify(salesOutboundOrderEntryMapper, never()).selectSummaryEntries(any(), any(), any());
    }
} 
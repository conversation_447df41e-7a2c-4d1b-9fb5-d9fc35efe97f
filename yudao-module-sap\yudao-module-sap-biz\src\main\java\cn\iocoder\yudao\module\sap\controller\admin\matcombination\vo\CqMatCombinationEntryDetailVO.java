// package cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo;

// import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombinationEntryDO;
// import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombSubEntryDO;
// import lombok.Data;
// import lombok.NoArgsConstructor;
// import lombok.AllArgsConstructor;

// import java.util.List;

// /**
//  * E3主商品拆分表-子表与子子表组合VO
//  */
// @Data
// @NoArgsConstructor
// @AllArgsConstructor
// public class CqMatCombinationEntryDetailVO {
    
//     /**
//      * 子表数据
//      */
//     private CqMatCombinationEntryDO entry;
    
//     /**
//      * 关联的子子表数据列表
//      */
//     private List<CqMatCombSubEntryDO> subEntries;
// } 
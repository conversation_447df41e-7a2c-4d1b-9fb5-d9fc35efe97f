package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 委外品牌仓库映射物料组信息 DO
 * 
 * 表名：tk_yd_muloutwhmapmatgroup
 */
@TableName("tk_yd_muloutwhmapmatgroup")
@Data
public class CqMulOutwhMapMatgroupDO {

    /**
     * 主键ID
     */
    @TableId("fpkid")
    private Long pkid;

    /**
     * 分录ID
     */
    @TableField("fentryid")
    private Long entryId;

    /**
     * 物料组ID
     */
    @TableField("fbasedataid")
    private Long matgroupId;
} 
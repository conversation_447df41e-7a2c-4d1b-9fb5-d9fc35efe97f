### 1. 测试主表结算 - 处理结算状态为1（MAIN_PENDING_SETTLE）的发货明细
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/main-settle
Content-Type: application/json

{
  "billNos": ["FH202412010001", "FH202412010002"],
  "shopNos": ["DP001", "DP002"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 2. 测试OMS明细结算 - 处理结算状态为2（OMS_DETAIL_PENDING_SETTLE）的发货明细
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/oms-detail-settle
Content-Type: application/json

{
  "billNos": ["FH202412010003", "FH202412010004"],
  "shopNos": ["DP003", "DP004"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 3. 测试拆单明细结算 - 处理结算状态为3（SPLIT_DETAIL_PENDING_SETTLE）的发货明细
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/split-detail-settle
Content-Type: application/json

{
  "billNos": ["FH202412010005", "FH202412010006"],
  "shopNos": ["DP005", "DP006"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 5. 测试OMS明细结算 - 仅按日期范围过滤
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/oms-detail-settle
Content-Type: application/json

{
  "startDate": "2024-12-01",
  "endDate": "2024-12-07"
}

### 6. 测试拆单明细结算 - 仅按店铺编号过滤
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/split-detail-settle
Content-Type: application/json

{
  "shopNos": ["DP007", "DP008"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 7. 测试主表结算 - 空参数（使用默认值）
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/main-settle
Content-Type: application/json

{
}

### 4. 测试主表结算 - 仅按单据编号过滤
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/main-settle
Content-Type: application/json

{
  "billNos": ["E3_0_C0001_125062559663219"]
}

### 8. 测试OMS明细结算 - 单个单据编号
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/oms-detail-settle
Content-Type: application/json

{
  "billNos": ["E3_0_C0001_125062559663219"]
}


### 9. 测试拆单明细结算 - 单个单据编号
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/split-detail-settle
Content-Type: application/json

{
  "billNos": ["E3_0_C0001_125062559663219"]
}



### ===================================临时测试==========================================
### ===================================================================================

### 1. 测试主表结算 - 处理结算状态为1（MAIN_PENDING_SETTLE）的发货明细
POST http://127.0.0.1:48001/admin-api/sap/delivery-detail-settle-test/main-settle
Content-Type: application/json

{
  "billNos": ["E3_0_C0001_125030640094075"],
  "shopNos": [""],
  "startDate": "2025-03-06",
  "endDate": "2025-03-06"
}

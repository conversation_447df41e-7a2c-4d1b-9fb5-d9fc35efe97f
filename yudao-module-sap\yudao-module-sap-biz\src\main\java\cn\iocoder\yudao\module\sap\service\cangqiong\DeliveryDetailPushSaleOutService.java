package cn.iocoder.yudao.module.sap.service.cangqiong;

import java.io.IOException;
import java.util.List;

/**
 * 发货明细下推销售出库单服务
 */
public interface DeliveryDetailPushSaleOutService {

//    /**
//     * 推送销售出库单（V1版本）
//     * 根据合单规则将多个销售出库单合并为一个单据
//     */
//    void pushSaleOutV1();

    /**
     * 推送销售出库单（V2版本）
     * 根据合单规则将多个销售出库单合并为一个单据，支持更多自定义配置
     */
    void pushSaleOutV2() throws IOException;

    /**
     * 推送销售出库单（V2版本）
     * 根据合单规则将多个销售出库单合并为一个单据，支持更多自定义配置
     * 
     * @param billNos 指定需要处理的单据编号列表，为空则处理所有符合条件的单据
     * @throws IOException IO异常
     */
    void pushSaleOutV2(List<String> billNos) throws IOException;
    
    /**
     * 推送销售出库单（V2版本）
     * 根据合单规则将多个销售出库单合并为一个单据，支持更多自定义配置
     * 
     * @param billNos 指定需要处理的单据编号列表，为空则处理所有符合条件的单据
     * @param mergeRuleCode 指定需要使用的合单规则编码，为空则使用所有符合条件的合单规则
     * @throws IOException IO异常
     */
    void pushSaleOutV2(List<String> billNos, String mergeRuleCode) throws IOException;

    /**
     * 解析下推销售出库单任务参数
     * 
     * @param param JSON格式的参数字符串，支持格式：{"billNos": ["单号1", "单号2", ...], "mergeRuleCode": "规则编码"}
     * @return 解析后的参数对象，如果参数为空或解析失败则返回空的DTO对象
     */
    cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailPushSaleOutParamsDTO parsePushSaleOutParams(String param);
}

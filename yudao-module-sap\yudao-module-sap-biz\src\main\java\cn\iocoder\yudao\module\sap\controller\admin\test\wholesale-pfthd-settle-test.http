### 1. 测试主表结算 - 处理结算状态为1（MAIN_PENDING_SETTLE）的批发退货单
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/main-settle
Content-Type: application/json

{
  "billNos": ["PFTHD202412010001", "PFTHD202412010002"],
  "shopNos": ["RT001", "RT002"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 2. 测试OMS明细结算 - 处理结算状态为2（OMS_DETAIL_PENDING_SETTLE）的批发退货单
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/oms-detail-settle
Content-Type: application/json

{
  "billNos": ["PFTHD202412010003", "PFTHD202412010004"],
  "shopNos": ["RT003", "RT004"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 3. 测试拆单明细结算 - 处理结算状态为3（SPLIT_DETAIL_PENDING_SETTLE）的批发退货单
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/split-detail-settle
Content-Type: application/json

{
  "billNos": ["PFTHD202412010005", "PFTHD202412010006"],
  "shopNos": ["RT005", "RT006"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 4. 测试主表结算 - 仅按单据编号过滤
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/main-settle
Content-Type: application/json

{
  "billNos": ["PFTHD2025061200018_66f6"]
}

### 5. 测试OMS明细结算 - 仅按日期范围过滤
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/oms-detail-settle
Content-Type: application/json

{
  "startDate": "2024-12-01",
  "endDate": "2024-12-07"
}

### 6. 测试拆单明细结算 - 仅按店铺编号过滤
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/split-detail-settle
Content-Type: application/json

{
  "shopNos": ["RT007", "RT008"],
  "startDate": "2024-12-01",
  "endDate": "2024-12-31"
}

### 7. 测试主表结算 - 空参数（使用默认值）
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/main-settle
Content-Type: application/json

{
}

### 8. 测试OMS明细结算 - 单个单据编号
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/oms-detail-settle
Content-Type: application/json

{
  "billNos": ["PFTHD2025061200018_66f6"]
}

### 9. 测试拆单明细结算 - 单个单据编号
POST http://127.0.0.1:48001/admin-api/sap/wholesale-pfthd-settle-test/split-detail-settle
Content-Type: application/json

{
  "billNos": ["PFTHD2025061200018_66f6"]
} 
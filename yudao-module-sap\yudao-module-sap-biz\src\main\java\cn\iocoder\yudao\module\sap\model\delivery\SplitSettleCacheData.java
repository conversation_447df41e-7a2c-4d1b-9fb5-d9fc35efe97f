package cn.iocoder.yudao.module.sap.model.delivery;

// import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationEntryDetailVO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqCkdygxEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqDirectwarehouseDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqYdOutwarehousemapEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.orgcuspricebill.CqOrgCusPriceBillEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.warehouse.CqBdWarehouseDO;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqBomWithEntriesDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 拆单结算缓存数据
 * 
 * 用于封装拆单结算过程中需要的所有预加载缓存数据，
 * 减少方法参数传递的复杂度，提高代码可维护性。
 * 
 * <AUTHOR>
 */
@Data
public class SplitSettleCacheData {
    
    /**
     * 平台合并匹配参数
     * 用于判断是否启用平台合并匹配功能
     */
    private String platformMergeParam;
    
    /**
     * 排除物料编码集合
     * 包含所有需要排除的物料编码
     */
    private Set<String> excludedMaterials;
    
    // /**
    //  * 物料组合关系缓存映射
    //  * Key: 物料编号
    //  * Value: 物料组合明细列表
    //  */
    // private Map<String, List<CqMatCombinationEntryDetailVO>> materialCombinationMap;
    
    /**
     * 委外品牌仓库映射关系缓存
     * Key: OutwarehouseMapKey (仓库ID+品牌ID+客户ID)
     * Value: 委外品牌仓库映射条目列表
     */
    private Map<OutwarehouseMapKey, List<CqYdOutwarehousemapEntryDO>> outwarehouseMapEntries;
    
    /**
     * 所有已审核物料列表
     * 用于内存过滤优化
     */
    private List<CqBdMaterialDO> allApprovedMaterials;
    
    /**
     * 所有有效价格数据列表
     * 用于内存过滤优化
     */
    private List<CqOrgCusPriceBillEntryDO> allValidPrices;
    
    /**
     * 内部交易关系组合缓存映射
     * Key: InnerRelKey (客户ID+品牌ID)
     * Value: 内部交易关系组合信息
     */
    private Map<InnerRelKey, InnerRelCombinedInfo> innerRelCombinedMap;

    /**
     * 仓库对应关系缓存映射
     * Key: CkdygxKey (平台+仓库ID)
     * Value: 仓库对应关系条目列表
     */
    private Map<PlatformWarehouseKey, List<CqCkdygxEntryDO>> ckdygxEntriesMap;
    
    /**
     * 直营店仓库缓存映射
     * Key: DirectwarehouseKey (平台+仓库ID)
     * Value: 直营店仓库条目列表
     */
    private Map<DirectwarehouseKey, List<CqDirectwarehouseDO>> directwarehouseMap;

    /**
     * 仓库缓存映射
     * Key: WarehouseKey (仓库ID)
     * Value: 仓库条目列表
     */
    private Map<Long, CqBdWarehouseDO> warehouseMap;
    
    /**
     * 销售BOM结构缓存映射
     * Key: BOM编号（CqBomDO.number字段）
     * Value: BOM主表和子表的组合对象
     * 用于快速查找BOM结构信息，避免重复数据库查询
     */
    private Map<String, CqBomWithEntriesDTO> bomStructuresMap;
    
    /**
     * 缓存加载开始时间戳
     * 用于统计缓存加载耗时
     */
    private long cacheLoadStartTime;
    
    /**
     * 缓存加载完成时间戳
     * 用于统计缓存加载耗时
     */
    private long cacheLoadEndTime;
    
    /**
     * 获取缓存加载耗时
     * 
     * @return 加载耗时（毫秒）
     */
    public long getCacheLoadDuration() {
        return cacheLoadEndTime - cacheLoadStartTime;
    }
    
    /**
     * 检查缓存数据是否完整
     * 
     * @return true-缓存数据完整，false-缓存数据不完整
     */
    public boolean isComplete() {
        return platformMergeParam != null 
            && excludedMaterials != null 
            && outwarehouseMapEntries != null 
            && allApprovedMaterials != null 
            && allValidPrices != null 
            && innerRelCombinedMap != null 
            && directwarehouseMap != null
            && bomStructuresMap != null;
    }
} 
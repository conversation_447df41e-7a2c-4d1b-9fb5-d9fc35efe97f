package cn.iocoder.yudao.module.sap.model.delivery;

import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.CqBomWithEntriesDTO;
// import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationEntryDetailVO;
import cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong.CqWldygxEntryDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.material.CqBdMaterialDO;
import cn.iocoder.yudao.module.sap.dal.dataobject.orgcuspricebill.CqOrgCusPriceBillEntryDO;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * OMS结算缓存数据
 * 
 * 用于封装OMS结算过程中需要的所有预加载缓存数据，
 * 减少方法参数传递的复杂度，提高代码可维护性。
 * 
 * <AUTHOR>
 */
@Data
public class OMSSettleCacheData {
    
    /**
     * 平台合并匹配参数
     * 用于判断是否启用平台合并匹配功能
     */
    private String platformMergeParam;
    
    /**
     * 排除物料编码集合
     * 包含所有需要排除的物料编码
     */
    private Set<String> excludedMaterials;
    
    /**
     * 物料对应关系缓存映射
     * Key: PlatformMaterialKey (平台+物料编号)
     * Value: 物料对应关系条目列表
     */
    private Map<PlatformMaterialKey, List<CqWldygxEntryDO>> wldygxEntriesMap;
    
    /**
     * 所有已审核物料列表
     * 用于内存过滤优化
     */
    private List<CqBdMaterialDO> allApprovedMaterials;
    
    /**
     * 所有有效价格数据列表
     * 用于内存过滤优化
     */
    private List<CqOrgCusPriceBillEntryDO> allValidPrices;

    // 销售BOM  
    private Map<String, CqBomWithEntriesDTO> bomStructuresMap;
    
    /**
     * 缓存加载开始时间戳
     * 用于统计缓存加载耗时
     */
    private long cacheLoadStartTime;
    
    /**
     * 缓存加载完成时间戳
     * 用于统计缓存加载耗时
     */
    private long cacheLoadEndTime;
    
    /**
     * 获取缓存加载耗时
     * 
     * @return 加载耗时（毫秒）
     */
    public long getCacheLoadDuration() {
        return cacheLoadEndTime - cacheLoadStartTime;
    }
    
    /**
     * 检查缓存数据是否完整
     * 
     * @return true-缓存数据完整，false-缓存数据不完整
     */
    public boolean isComplete() {
        return platformMergeParam != null 
            && excludedMaterials != null 
            && bomStructuresMap != null 
            && wldygxEntriesMap != null 
            && allApprovedMaterials != null 
            && allValidPrices != null;
    }
} 
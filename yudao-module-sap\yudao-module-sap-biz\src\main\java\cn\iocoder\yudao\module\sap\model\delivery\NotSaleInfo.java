package cn.iocoder.yudao.module.sap.model.delivery;

import lombok.Data;

/**
 * 非销售信息DTO
 * 用于存储临时店铺的非销售相关信息
 */
@Data
public class NotSaleInfo {
    
    /**
     * 预算承担公司（作废）
     */
    @Deprecated
    private String ydBudgetcompany;
    
    /**
     * 店铺(非销)
     */
    private String ydNotsaleshop;
    
    /**
     * 申请部门
     */
    private String ydApplydepart;
    
    /**
     * 领料用途
     */
    private String ydRequisitionuse;
    
    /**
     * 预算承担部门（作废）
     */
    @Deprecated
    private String ydBudgetdepart;
    
    /**
     * 预算科目（作废）
     */
    @Deprecated
    private String ydBudgetaccount; 

    /**
     * 基金科目
     */
    private String ydFundaccount;

    /**
     * 成本中心
     */
    private String ydCostcenter;

    /**
     * 客户(非销)
     */
    private String ydNotsalecust;

    /**
     * 客户类型(非销)
     */
    private String ydNotsalecusttype;
    
} 
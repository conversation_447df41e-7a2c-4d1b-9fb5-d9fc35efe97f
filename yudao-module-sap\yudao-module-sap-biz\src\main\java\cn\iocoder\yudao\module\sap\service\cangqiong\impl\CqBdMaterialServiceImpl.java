package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.sap.dal.dataobject.material.*;
import cn.iocoder.yudao.module.sap.dal.mysql.material.*;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqBdMaterialService;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.util.function.Function;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.iocoder.yudao.module.sap.dal.mysql.material.CqBdMaterialGroupDetailMapper;

/**
 * 物料-主表 Service 实现类
 */
@Service
@Validated
@Slf4j
@DS("cq_sys") // 使用苍穹SCM数据源
public class CqBdMaterialServiceImpl implements CqBdMaterialService {

    @Resource
    private CqBdMaterialMapper materialMapper;
    
    @Resource
    private CqBdMaterialSMapper materialSMapper;
    
    @Resource
    private CqBdMaterialLMapper materialLMapper;
    
    @Resource
    private CqBdMatScmProapEntryMapper matScmProapEntryMapper;
    
    @Resource
    private CqBdMaterialUMapper materialUMapper;

    @Resource
    private CqBdMaterialGroupDetailMapper materialGroupDetailMapper;

    @Override
    public CqBdMaterialDO getMaterial(Long id) {
        if (id == null) {
            return null;
        }
        
        // 直接从数据库查询
        return materialMapper.selectById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public CqBdMaterialWithEntriesDTO getMaterialWithEntries(Long id) {
        // 查询物料主表信息
        CqBdMaterialDO material = materialMapper.selectById(id);
        if (material == null) {
            return null;
        }
        
        return assembleMaterialWithEntries(material);
    }

    @Override
    public CqBdMaterialDO getMaterialByNumber(String number) {
        if (number == null || number.isEmpty()) {
            log.warn("[getMaterialByNumber] 物料编号为空");
            return null;
        }
        
        // 直接从数据库查询
        return materialMapper.selectByNumber(number);
    }

    @Override
    @Transactional(readOnly = true)
    public CqBdMaterialWithEntriesDTO getMaterialWithEntriesByNumber(String number) {
        // 查询物料主表信息
        CqBdMaterialDO material = materialMapper.selectByNumber(number);
        if (material == null) {
            return null;
        }
        
        return assembleMaterialWithEntries(material);
    }

    @Override
    public List<CqBdMaterialDO> getMaterialListByName(String name) {
        if (name == null || name.isEmpty()) {
            return Collections.emptyList();
        }
        return materialMapper.selectByNameLike(name);
    }

    @Override
    public List<CqBdMaterialDO> getMaterialListByNumbers(List<String> numbers) {
        if (CollUtil.isEmpty(numbers)) {
            return Collections.emptyList();
        }
        return materialMapper.selectByNumbers(numbers);
    }

    @Override
    public List<CqBdMaterialDO> queryMaterialList(String number, String name, Long brandId, Long categoryId, String status) {
        return materialMapper.selectList(new LambdaQueryWrapperX<CqBdMaterialDO>()
                .likeIfPresent(CqBdMaterialDO::getNumber, number)
                .likeIfPresent(CqBdMaterialDO::getName, name)
                .eqIfPresent(CqBdMaterialDO::getBrandId, brandId)
                .eqIfPresent(CqBdMaterialDO::getCategoryId, categoryId)
                .eqIfPresent(CqBdMaterialDO::getStatus, status));
    }

    @Override
    public List<CqBdMaterialDO> getPendingAuditMaterialList() {
        // 状态为"已提交"的物料列表（B表示已提交，等待审核）
        return materialMapper.selectList(new LambdaQueryWrapperX<CqBdMaterialDO>()
                .eq(CqBdMaterialDO::getStatus, "B"));
    }

    @Override
    public List<CqBdMaterialDO> getMaterialListByHelpCode(String helpCode) {
        if (helpCode == null || helpCode.isEmpty()) {
            return Collections.emptyList();
        }
        return materialMapper.selectByHelpCode(helpCode);
    }

    /**
     * 获取所有已审核且可用的物料列表
     * 
     * 此方法用于查询状态为已审核且启用状态的物料数据，主要用于：
     * 1. 批量查询优化，避免重复查询数据库
     * 2. 拆单结算等业务场景中需要获取有效物料列表
     * 3. 提供给其他方法作为基础数据源
     * 
     * 查询条件说明：
     * - status = "C": 物料状态为已审核（C表示Confirmed已确认/审核通过）
     * - enable = "1": 物料启用状态为可用（1表示启用，0表示禁用）
     * 
     * @return 已审核且可用的物料列表，如果没有符合条件的物料则返回空列表
     */
    private List<CqBdMaterialDO> getAllApprovedMaterials() {
        // 查询所有已审核状态且可用的物料（状态为"C"表示已审核，enable为"1"表示可用）
        return materialMapper.selectList(new LambdaQueryWrapperX<CqBdMaterialDO>()
                .eq(CqBdMaterialDO::getStatus, "C")  // 状态为已审核
                .eq(CqBdMaterialDO::getEnable, "1")); // 启用状态为可用
    }
    
    @Override
    public Map<Long, CqBdMaterialDO> getAllMaterialsMap() {
        // 日志记录
        log.info("[getAllMaterialsMap][开始获取所有物料Map]");

        try {
            // 为了性能考虑，优先获取已审核的物料
            // 调用私有方法getAllApprovedMaterials()获取状态为"C"（已审核）且enable为"1"（可用）的物料列表
            // 这样可以避免查询所有物料数据，提高查询效率和系统性能
            List<CqBdMaterialDO> materialList = getAllApprovedMaterials();

            // 查询物料分组信息
            log.info("[getAllMaterialsMap][开始查询物料分组信息]");
            List<CqBdMaterialGroupInfoDTO> groupInfoList = materialGroupDetailMapper.selectMaterialGroupInfoByStandard();
            log.info("[getAllMaterialsMap][查询到物料分组信息数量:{}]", groupInfoList.size());

            // 构建物料ID到分组信息的映射
            Map<Long, CqBdMaterialGroupInfoDTO> groupInfoMap = groupInfoList.stream()
                    .collect(Collectors.toMap(
                            CqBdMaterialGroupInfoDTO::getMaterialId,
                            Function.identity(),
                            (existing, replacement) -> existing));

            // 为物料对象设置分组信息
            materialList.forEach(material -> {
                CqBdMaterialGroupInfoDTO groupInfo = groupInfoMap.get(material.getId());
                if (groupInfo != null) {
                    material.setMatGroupId(groupInfo.getMatGroupId());
                    material.setMatGroupNum(groupInfo.getMatGroupNum());
                    material.setMatGroupName(groupInfo.getMatGroupName());
                }
            });

            // 统计设置了分组信息的物料数量
            long materialWithGroupCount = materialList.stream()
                    .filter(material -> material.getMatGroupId() != null)
                    .count();
            log.info("[getAllMaterialsMap][成功设置分组信息的物料数量:{}]", materialWithGroupCount);

            // 将列表转换为Map
            Map<Long, CqBdMaterialDO> materialMap = materialList.stream()
                    .collect(Collectors.toMap(
                            CqBdMaterialDO::getId,  // 使用物料ID作为Map的键
                            Function.identity(),  // 物料对象本身作为Map的值
                            (existing, replacement) -> existing));  // 如果有重复键，保留第一个

            log.info("[getAllMaterialsMap][获取所有物料Map成功，总数:{}]", materialMap.size());
            return materialMap;
        } catch (Exception e) {
            log.error("[getAllMaterialsMap][获取所有物料Map异常]", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取所有物料列表（包含分组信息）
     * 
     * 该方法用于获取所有已审核的物料信息，并为每个物料设置对应的分组信息。
     * 主要用于需要物料分组信息的业务场景，如物料分类统计、分组查询等。
     * 
     * 处理流程：
     * 1. 获取所有已审核的物料基础信息
     * 2. 查询物料分组详细信息
     * 3. 构建物料ID到分组信息的映射关系
     * 4. 为每个物料对象设置对应的分组信息
     * 5. 返回包含分组信息的完整物料列表
     * 
     * @return 包含分组信息的物料列表，如果查询异常则返回空列表
     */
    @Override
    public List<CqBdMaterialDO> getAllMaterialsWithGroup() {
        // 日志记录
        log.info("[getAllMaterialsWithGroup][开始获取所有物料列表]");
        
        try {
            // 为了性能考虑，优先获取已审核的物料
            List<CqBdMaterialDO> materialList = getAllApprovedMaterials();
            // 处理proType为空的情况，设置为默认值""
            // 处理brandId为空的情况，设置为默认值0L
            materialList.forEach(material -> {
                if (material.getProType() == null || material.getProType().trim().isEmpty()) {
                    material.setProType("");
                }
                if (material.getBrandId() == null) {
                    material.setBrandId(0L);
                }
            });
            
            // 查询物料分组信息
            log.info("[getAllMaterialsWithGroup][开始查询物料分组信息]");
            List<CqBdMaterialGroupInfoDTO> groupInfoList = materialGroupDetailMapper.selectMaterialGroupInfoByStandard();
            log.info("[getAllMaterialsWithGroup][查询到物料分组信息数量:{}]", groupInfoList.size());
            
            // 构建物料ID到分组信息的映射
            Map<Long, CqBdMaterialGroupInfoDTO> groupInfoMap = groupInfoList.stream()
                    .collect(Collectors.toMap(
                            CqBdMaterialGroupInfoDTO::getMaterialId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            
            // 为物料对象设置分组信息
            materialList.forEach(material -> {
                CqBdMaterialGroupInfoDTO groupInfo = groupInfoMap.get(material.getId());
                if (groupInfo != null) {
                    material.setMatGroupId(groupInfo.getMatGroupId() != null ? groupInfo.getMatGroupId() : 0L);
                    material.setMatGroupNum(groupInfo.getMatGroupNum());
                    material.setMatGroupName(groupInfo.getMatGroupName());
                }
            });
            
            // 统计设置了分组信息的物料数量
            long materialWithGroupCount = materialList.stream()
                    .filter(material -> material.getMatGroupId() != null)
                    .count();
            log.info("[getAllMaterialsWithGroup][成功设置分组信息的物料数量:{}]", materialWithGroupCount);
            
            log.info("[getAllMaterialsWithGroup][获取所有物料列表成功，总数:{}]", materialList.size());
            return materialList;
        } catch (Exception e) {
            log.error("[getAllMaterialsWithGroup][获取所有物料列表异常]", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 组装物料完整信息（包含辅助属性）
     *
     * @param material 物料基本信息
     * @return 物料完整信息
     */
    private CqBdMaterialWithEntriesDTO assembleMaterialWithEntries(CqBdMaterialDO material) {
        if (material == null) {
            return null;
        }
        
        // 创建返回对象
        CqBdMaterialWithEntriesDTO result = new CqBdMaterialWithEntriesDTO();
        
        // 复制物料主表数据
        org.springframework.beans.BeanUtils.copyProperties(material, result);
        
        // 查询物料分表数据
        CqBdMaterialSDO materialExt = materialSMapper.selectByMaterialId(material.getId());
        result.setMaterialExt(materialExt);
        
        // 查询物料多语言数据
        List<CqBdMaterialLDO> materialLangs = materialLMapper.selectByMaterialId(material.getId());
        result.setMaterialLangs(materialLangs);
        
        // 查询物料辅助属性
        List<CqBdMatScmProapEntryDO> auxPropEntries = matScmProapEntryMapper.selectByMaterialId(material.getId());
        result.setAuxPropEntries(auxPropEntries);
        
        // 查询物料使用范围
        List<CqBdMaterialUDO> materialUseOrgs = materialUMapper.selectByDataId(material.getId());
        result.setMaterialUseOrgs(materialUseOrgs);
        
        return result;
    }
} 
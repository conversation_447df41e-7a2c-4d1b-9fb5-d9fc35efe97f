package cn.iocoder.yudao.module.sap.dal.dataobject.cangqiong;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 委外品牌仓库映射客户信息 DO
 * 
 * 表名：tk_yd_muloutwhmapcust
 */
@TableName("tk_yd_muloutwhmapcust")
@Data
public class CqMulOutwhMapCustDO {

    /**
     * 主键ID
     */
    @TableId("fpkid")
    private Long pkid;

    /**
     * 分录ID
     */
    @TableField("fentryid")
    private Long entryId;

    /**
     * 客户ID
     */
    @TableField("fbasedataid")
    private Long customerId;
} 
// package cn.iocoder.yudao.module.sap.service.cangqiong.impl;

// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;

// import org.springframework.beans.BeanUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.util.CollectionUtils;

// import com.baomidou.dynamic.datasource.annotation.DS;
// import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

// import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationEntryDetailVO;
// import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationRespVO;
// import cn.iocoder.yudao.module.sap.controller.admin.matcombination.vo.CqMatCombinationJoinResultVO;
// import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombinationDO;
// import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombinationEntryDO;
// import cn.iocoder.yudao.module.sap.dal.dataobject.matcombination.CqMatCombSubEntryDO;
// import cn.iocoder.yudao.module.sap.dal.mysql.matcombination.CqMatCombinationEntryMapper;
// import cn.iocoder.yudao.module.sap.dal.mysql.matcombination.CqMatCombinationMapper;
// import cn.iocoder.yudao.module.sap.dal.mysql.matcombination.CqMatCombSubEntryMapper;
// import cn.iocoder.yudao.module.sap.service.cangqiong.CqMatCombinationService;
// import lombok.extern.slf4j.Slf4j;

// /**
//  * E3主商品拆分 Service 实现类
//  */
// @Service
// @Slf4j
// @DS("cq_scm")
// public class CqMatCombinationServiceImpl implements CqMatCombinationService {

//     @Autowired
//     private CqMatCombinationMapper cqMatCombinationMapper;
    
//     @Autowired
//     private CqMatCombinationEntryMapper cqMatCombinationEntryMapper;
    
//     @Autowired
//     private CqMatCombSubEntryMapper cqMatCombSubEntryMapper;
    
//     @Override
//     public CqMatCombinationDO getCqMatCombination(Long id) {
//         return cqMatCombinationMapper.selectById(id);
//     }
    
//     @Override
//     public CqMatCombinationDO getCqMatCombinationByBillNo(String billNo) {
//         return cqMatCombinationMapper.selectByBillNo(billNo);
//     }
    
//     @Override
//     public List<CqMatCombinationEntryDO> getCqMatCombinationEntries(Long id) {
//         return cqMatCombinationEntryMapper.selectByHeaderId(id);
//     }
    
//     @Override
//     public List<CqMatCombSubEntryDO> getCqMatCombSubEntries(Long entryId) {
//         return cqMatCombSubEntryMapper.selectByEntryId(entryId);
//     }
    
//     @Override
//     public CqMatCombinationRespVO getCqMatCombinationDetail(Long id) {
//         // ... existing code ...
//         return null;
//     }
    
//     @Override
//     public List<CqMatCombinationEntryDO> getEntriesByMainMatNum(String mainMatNum) {
//         return cqMatCombinationEntryMapper.selectByMainMatNum(mainMatNum);
//     }
    
//     @Override
//     public List<CqMatCombinationEntryDO> getEntriesByMatType(String matType) {
//         return cqMatCombinationEntryMapper.selectByMatType(matType);
//     }
    
//     @Override
//     public boolean isMainMatNumCombined(String mainMatNum) {
//         List<CqMatCombinationEntryDO> entries = getEntriesByMainMatNum(mainMatNum);
//         return entries != null && !entries.isEmpty();
//     }
    
//     @Override
//     public List<CqMatCombinationEntryDetailVO> getEntryDetailsByMainMatNum(String mainMatNum) {
//         // 1. 使用LambdaQueryWrapper查询审核状态的主表记录
//         LambdaQueryWrapper<CqMatCombinationDO> mainQueryWrapper = new LambdaQueryWrapper<>();
//         mainQueryWrapper.eq(CqMatCombinationDO::getBillStatus, "C"); // C表示已审核
//         List<Long> auditedIds = cqMatCombinationMapper.selectList(mainQueryWrapper)
//                 .stream().map(CqMatCombinationDO::getId).collect(Collectors.toList());
        
//         // 2. 检查是否有审核状态的主表记录
//         if (CollectionUtils.isEmpty(auditedIds)) {
//             log.info("未找到审核状态的主表记录");
//             return new ArrayList<>();
//         }
        
//         // 3. 使用LambdaQueryWrapper查询符合主表ID和mainMatNum条件的子表数据
//         LambdaQueryWrapper<CqMatCombinationEntryDO> entryQueryWrapper = new LambdaQueryWrapper<>();
//         entryQueryWrapper.eq(CqMatCombinationEntryDO::getMainMatNum, mainMatNum)
//                          .in(CqMatCombinationEntryDO::getId, auditedIds);
//         List<CqMatCombinationEntryDO> entries = cqMatCombinationEntryMapper.selectList(entryQueryWrapper);
        
//         // 4. 处理空结果情况
//         if (CollectionUtils.isEmpty(entries)) {
//             log.info("未找到符合条件的子表记录, mainMatNum: {}", mainMatNum);
//             return new ArrayList<>();
//         }
        
//         // 5. 遍历Entry，获取对应的SubEntries并组装VO
//         List<CqMatCombinationEntryDetailVO> result = new ArrayList<>();
//         for (CqMatCombinationEntryDO entry : entries) {
//             // 5.1 获取子子表数据
//             List<CqMatCombSubEntryDO> subEntries = getCqMatCombSubEntries(entry.getEntryId());
            
//             // 5.2 创建并填充VO
//             CqMatCombinationEntryDetailVO detailVO = new CqMatCombinationEntryDetailVO(entry, subEntries);
            
//             // 5.3 添加到结果列表
//             result.add(detailVO);
//         }
        
//         return result;
//     }
    
//     @Override
//     public Map<String, List<CqMatCombinationEntryDetailVO>> getAllMaterialCombinations() {
//         // 直接调用优化后的方法
//         return getAllMaterialCombinationsOptimized();
//     }

//     /**
//      * 使用SQL联合查询优化的获取所有物料组合关系方法
//      *
//      * @return Map<物料编号, 组合明细列表>
//      */
//     /**
//      * 使用SQL联合查询优化的获取所有物料组合关系方法
//      * 该方法通过一次性联合查询获取所有数据，避免多次数据库查询，提高性能
//      *
//      * @return Map<物料编号, 组合明细列表> 返回以物料编号为键，组合明细列表为值的映射
//      */
//     public Map<String, List<CqMatCombinationEntryDetailVO>> getAllMaterialCombinationsOptimized() {
//         // 初始化结果Map，用于存储物料编号到组合明细列表的映射
//         Map<String, List<CqMatCombinationEntryDetailVO>> resultMap = new HashMap<>();
        
//         // 1. 一次性联合查询获取所有数据
//         // 通过Mapper执行联合查询，获取主表、子表和子子表的关联数据
//         List<CqMatCombinationJoinResultVO> joinResults = cqMatCombinationMapper.selectAllMaterialCombinationsWithJoin();
        
//         // 检查查询结果是否为空
//         if (CollectionUtils.isEmpty(joinResults)) {
//             log.info("[getAllMaterialCombinationsOptimized] 未找到符合条件的数据");
//             return resultMap;
//         }
        
//         // 记录日志，显示获取到的原始数据条数
//         log.info("[getAllMaterialCombinationsOptimized] 联合查询获取到 {} 条原始数据", joinResults.size());
        
//         // 2. 按entryId分组，将JOIN结果转换为Entry和SubEntry的映射关系
//         // 用于存储entryId到Entry对象的映射
//         Map<Long, CqMatCombinationEntryDO> entryMap = new HashMap<>();
//         // 用于存储entryId到SubEntry列表的映射
//         Map<Long, List<CqMatCombSubEntryDO>> subEntryMap = new HashMap<>();
        
//         // 遍历所有联合查询结果，构建Entry和SubEntry对象
//         for (CqMatCombinationJoinResultVO joinResult : joinResults) {
//             Long entryId = joinResult.getEntryId();
            
//             // 2.1 构建Entry对象（避免重复创建）
//             // 如果该entryId的Entry对象尚未创建，则创建并存入映射
//             if (!entryMap.containsKey(entryId)) {
//                 CqMatCombinationEntryDO entry = new CqMatCombinationEntryDO();
//                 // 设置Entry对象的各个属性值
//                 entry.setId(joinResult.getMainId());
//                 entry.setEntryId(joinResult.getEntryId());
//                 entry.setSeq(joinResult.getEntrySeq());
//                 entry.setMainMatNum(joinResult.getMainMatNum());
//                 entry.setMainMatName(joinResult.getMainMatName());
//                 entry.setMainMatAmt(joinResult.getMainMatAmt());
// //                entry.setComboField(joinResult.getComboField());
//                 entry.setMatType(joinResult.getMatType());
// //                entry.setCheckboxField(joinResult.getCheckboxField());
//                 entry.setIsSingle(joinResult.getIsSingle());
                
//                 // 将创建的Entry对象存入映射
//                 entryMap.put(entryId, entry);
//                 // 为该entryId初始化一个空的SubEntry列表
//                 subEntryMap.put(entryId, new ArrayList<>());
//             }
            
//             // 2.2 构建SubEntry对象（如果存在）
//             // 只有当detailId不为空时，才表示有对应的子子表记录
//             if (joinResult.getDetailId() != null) {
//                 CqMatCombSubEntryDO subEntry = new CqMatCombSubEntryDO();
//                 // 设置SubEntry对象的各个属性值
//                 subEntry.setEntryId(entryId);
//                 subEntry.setDetailId(joinResult.getDetailId());
//                 subEntry.setSeq(joinResult.getDetailSeq());
//                 subEntry.setMaterialId(joinResult.getMaterialId());
//                 subEntry.setQty(joinResult.getQty());
//                 subEntry.setPrice(joinResult.getPrice());
//                 subEntry.setRate(joinResult.getRate());
//                 subEntry.setMatAmt(joinResult.getMatAmt());
                
//                 // 将创建的SubEntry对象添加到对应entryId的列表中
//                 subEntryMap.get(entryId).add(subEntry);
//             }
//         }
        
//         // 3. 按mainMatNum分组组装最终结果
//         // 遍历所有Entry对象，按照mainMatNum分组
//         for (CqMatCombinationEntryDO entry : entryMap.values()) {
//             String mainMatNum = entry.getMainMatNum();
            
//             // 3.1 初始化mainMatNum对应的列表
//             // 如果该mainMatNum尚未在结果Map中创建对应的列表，则创建一个空列表
//             resultMap.putIfAbsent(mainMatNum, new ArrayList<>());
            
//             // 3.2 获取对应的子子表数据
//             // 根据entryId获取对应的SubEntry列表
//             List<CqMatCombSubEntryDO> subEntries = subEntryMap.get(entry.getEntryId());
            
//             // 3.3 创建并填充VO
//             // 创建DetailVO对象，包含Entry和SubEntry信息
//             CqMatCombinationEntryDetailVO detailVO = new CqMatCombinationEntryDetailVO(entry, subEntries);
            
//             // 3.4 添加到对应mainMatNum的列表中
//             // 将DetailVO添加到结果Map中对应mainMatNum的列表
//             resultMap.get(mainMatNum).add(detailVO);
//         }
        
//         // 记录日志，显示处理完成后的物料组合关系数量
//         log.info("[getAllMaterialCombinationsOptimized] 处理完成，获取到 {} 个物料组合关系", resultMap.size());
//         // 返回结果Map
//         return resultMap;
//     }
// } 
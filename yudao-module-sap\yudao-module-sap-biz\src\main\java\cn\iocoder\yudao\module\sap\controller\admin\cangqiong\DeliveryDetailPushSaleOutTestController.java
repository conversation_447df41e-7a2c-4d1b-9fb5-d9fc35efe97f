package cn.iocoder.yudao.module.sap.controller.admin.cangqiong;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailPushSaleOutParamsDTO;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailPushSaleOutService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.io.IOException;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 发货明细下推销售出库单测试控制器
 * 用于测试发货明细下推销售出库单功能
 */
@Tag(name = "管理后台 - 发货明细下推销售出库单测试")
@RestController
@RequestMapping("/sap/delivery-detail-push-sale-out-test")
@Validated
@Slf4j
public class DeliveryDetailPushSaleOutTestController {

    @Resource
    private DeliveryDetailPushSaleOutService deliveryDetailPushSaleOutService;

    /**
     * 测试发货明细下推销售出库单
     * 支持传入单据编号列表和合单规则编码参数
     */
    @PostMapping("/push-sale-out")
    @Operation(summary = "测试发货明细下推销售出库单", description = "测试发货明细下推销售出库单功能，支持传入单据编号列表和合单规则编码")
    @PermitAll
    public CommonResult<String> testPushSaleOut(@RequestBody @Valid DeliveryDetailPushSaleOutParamsDTO params) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[testPushSaleOut][开始测试发货明细下推销售出库单，参数：{}]", params);
            
            // 调用发货明细下推销售出库单服务
            deliveryDetailPushSaleOutService.pushSaleOutV2(params.getBillNos(), params.getMergeRuleCode());
            
            long endTime = System.currentTimeMillis();
            String successMsg = String.format("发货明细下推销售出库单测试执行成功，耗时：%dms", endTime - startTime);
            log.info("[testPushSaleOut][{}]", successMsg);
            
            return success(successMsg);
        } catch (IOException e) {
            long endTime = System.currentTimeMillis();
            String errorMsg = String.format("发货明细下推销售出库单测试执行失败，耗时：%dms，IO错误：%s", endTime - startTime, e.getMessage());
            log.error("[testPushSaleOut][{}]", errorMsg, e);
            
            return success(errorMsg);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            String errorMsg = String.format("发货明细下推销售出库单测试执行失败，耗时：%dms，错误：%s", endTime - startTime, e.getMessage());
            log.error("[testPushSaleOut][{}]", errorMsg, e);
            
            return success(errorMsg);
        }
    }
} 
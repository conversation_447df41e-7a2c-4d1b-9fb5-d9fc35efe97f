package cn.iocoder.yudao.module.sap.task;

import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import java.util.concurrent.TimeUnit;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.annotation.Resource;
import cn.iocoder.yudao.module.sap.service.cangqiong.DeliveryDetailPushSaleOutService;
import cn.iocoder.yudao.module.sap.controller.admin.cangqiong.vo.DeliveryDetailPushSaleOutParamsDTO;
import cn.iocoder.yudao.module.sap.utils.DingDingUtils;

/**
 * 发货明细下推销售出库单 - 定时任务
 */
@Component
@Slf4j
public class DeliveryDetailPushSaleOutTask {

    @Resource
    private DeliveryDetailPushSaleOutService deliveryDetailPushSaleOutService;
    
    @Resource
    private RedissonClient redissonClient;
    
    @Resource
    private DingDingUtils dingDingUtils;
    
    /**
     * 发货明细下推销售出库单任务锁键名
     */
    private static final String PUSH_SALE_OUT_LOCK_KEY = "sap:task:delivery_detail_push_sale_out";

    /**
     * 默认锁超时时间（2小时）
     */
    private static final long LOCK_TIMEOUT = 2 * 60 * 60 * 1000L;

    /**
     * 执行发货明细下推销售出库单
     */
    @XxlJob("jsp.executePushSaleOut")
    @TenantIgnore
    public void executePushSaleOut() {
        log.info("[executePushSaleOut][开始执行发货明细下推销售出库单]");
        
        // 获取XxlJob参数，支持JSON格式：{"billNos": ["单号1", "单号2", ...], "mergeRuleCode": "规则编码"}
        String param = XxlJobHelper.getJobParam();
        
        // 解析参数
        DeliveryDetailPushSaleOutParamsDTO paramsDTO = deliveryDetailPushSaleOutService.parsePushSaleOutParams(param);
        
        // 创建Redis锁对象
        RLock lock = redissonClient.getLock(PUSH_SALE_OUT_LOCK_KEY);
        
        boolean lockAcquired = false;
        try {
            // 尝试获取锁，等待10秒，锁定30分钟
            lockAcquired = lock.tryLock(10, LOCK_TIMEOUT, TimeUnit.MILLISECONDS);
            
            if (!lockAcquired) {
                log.info("[executePushSaleOut][获取锁失败，任务已被其他实例执行]");
                sendLockFailureNotification("发货明细下推销售出库单", "executePushSaleOut", param, PUSH_SALE_OUT_LOCK_KEY);
                return;
            }
            
            log.info("[executePushSaleOut][成功获取锁，开始执行任务]");
            
            // 执行任务逻辑，传入解析后的参数
            deliveryDetailPushSaleOutService.pushSaleOutV2(paramsDTO.getBillNos(), paramsDTO.getMergeRuleCode());
            
        } catch (InterruptedException e) {
            log.error("[executePushSaleOut][获取锁被中断]", e);
            Thread.currentThread().interrupt();
        } catch (IOException e) {
            log.error("[executePushSaleOut][调用pushSaleOutV2异常]", e);
        } catch (Exception e) {
            log.error("[executePushSaleOut][执行任务发生异常]", e);
        } finally {
            // 在finally块中释放锁，确保锁一定会被释放
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("[executePushSaleOut][释放锁成功]");
            }
        }
        
        log.info("[executePushSaleOut][结束执行发货明细下推销售出库单]");
    }

    /**
     * 发送锁获取失败通知到钉钉群
     * 
     * @param taskName 任务名称
     * @param methodName 方法名称
     * @param param 任务参数
     * @param lockKey 锁键名
     */
    private void sendLockFailureNotification(String taskName, String methodName, String param, String lockKey) {
        try {
            // 格式化当前时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 构建Markdown格式的通知消息
            String title = "⚠️ 任务锁获取失败通知";
            String content = String.format(
                "## ⚠️ 任务锁获取失败通知\n\n" +
                "**发生时间**: %s\n\n" +
                "**任务名称**: %s\n\n" +
                "**执行方法**: `%s`\n\n" +
                "**锁键名**: `%s`\n\n" +
                "**失败原因**: 锁已被其他实例占用，任务无法执行\n\n" +
                "**传入参数**: %s\n\n" +
                "---\n\n" +
                "**处理建议**: 请检查是否有其他实例正在执行相同任务，或考虑调整任务调度时间避免冲突。\n\n" +
                "*此消息由任务调度系统自动发送*",
                currentTime,
                taskName,
                methodName,
                lockKey,
                param != null && !param.trim().isEmpty() ? "```json\n" + param + "\n```" : "无参数"
            );
            
            // 发送钉钉消息到异常通知机器人
            boolean success = dingDingUtils.sendMarkdownMessage(title, content, "exception-notify");
            
            if (success) {
                log.info("[{}][锁获取失败通知发送成功]", methodName);
            } else {
                log.warn("[{}][锁获取失败通知发送失败]", methodName);
            }
        } catch (Exception e) {
            log.error("[{}][发送锁获取失败通知异常]", methodName, e);
        }
    }

}

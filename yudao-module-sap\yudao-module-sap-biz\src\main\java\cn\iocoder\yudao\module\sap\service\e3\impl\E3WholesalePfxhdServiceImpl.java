package cn.iocoder.yudao.module.sap.service.e3.impl;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.sap.config.CangQiongConfig;
import cn.iocoder.yudao.module.sap.config.E3Config;
import cn.iocoder.yudao.module.sap.enums.BusinessSceneEnum;
import cn.iocoder.yudao.module.sap.enums.CompanyEnum;
import cn.iocoder.yudao.module.sap.model.e3.E3Response;
import cn.iocoder.yudao.module.sap.model.e3.E3WholesalePfxhdGetRequest;
import cn.iocoder.yudao.module.sap.model.e3.E3WholesalePfxhdGetResponse;
import cn.iocoder.yudao.module.sap.model.wholesale.WholesaleNoticeBillSaveResponse;
import cn.iocoder.yudao.module.sap.model.wholesale.WholesaleNoticeBillEntryRequest;
import cn.iocoder.yudao.module.sap.model.wholesale.WholesaleNoticeBillSaveRequest;
import cn.iocoder.yudao.module.sap.service.cangqiong.CangQiongApiService;
import cn.iocoder.yudao.module.sap.service.e3.E3ConfigService;
import cn.iocoder.yudao.module.sap.service.e3.E3WholesalePfxhdService;
import cn.iocoder.yudao.module.sap.utils.E3ApiUtils;
import cn.iocoder.yudao.module.sap.utils.E3DateUtils;
import cn.iocoder.yudao.module.sap.utils.E3SqlApiUtils;
import cn.iocoder.yudao.module.sap.utils.SqlResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.MediaType;
import okhttp3.Response;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.module.sap.service.cangqiong.CqWholesaleNoticeBillService;
import org.apache.commons.lang3.StringUtils;

/**
 * E3批发销货单服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class E3WholesalePfxhdServiceImpl implements E3WholesalePfxhdService {

    @Resource
    private E3ConfigService e3ConfigService;
    
    @Resource
    private CangQiongConfig cangQiongConfig;
    
    @Resource
    private CangQiongApiService cangQiongApiService;

    @Resource
    private CqWholesaleNoticeBillService cqWholesaleNoticeBillService;

    /**
     * E3批发通知单列表查询接口方法名
     */
    private static final String METHOD_PFTZD_GET = "e3oms.wholesale.pftzd.get";
    
    /**
     * 批发通知单新增接口URL
     */
    private static final String URL_WHOLESALE_NOTICE_BILL_ADD = "/kapi/v2/yd/im/yd_wholesalenoticebill/ec_yd_wholesalenoticebill_add";
    
    /**
     * 媒体类型常量
     */
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    /**
     * E3批发销货单列表查询接口方法名
     */
    private static final String METHOD_PFXHD_GET = "e3oms.wholesale.pfxhd.list.get";
    
    private static final String COMPANY_MAIYOU = "maiyou";
    private static final String COMPANY_BAIYUE = "baiyue";

    /**
     * OkHttpClient实例
     */
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();
    
    /**
     * 重试相关常量
     */
    private static final int MAX_RETRY_TIMES = 3; // 最大重试次数
    private static final long BASE_RETRY_DELAY_MS = 2000L; // 基础重试延迟时间，单位毫秒

    /**
     * 获取E3批发销货单列表
     *
     * @param company 公司代码，如"maiyou"或"baiyue"
     * @param rqStart 开始日期，格式为yyyy-MM-dd
     * @param rqEnd 结束日期，格式为yyyy-MM-dd
     * @param pageNo 页码，默认为1
     * @param pageSize 每页记录数，默认为1000
     * @param djbh 单据编号，可选参数
     * @param pftzdDjbh 批发通知单单据编号，可选参数
     * @param qdCode 渠道编码，可选参数
     * @param djzt 单据状态，可选参数
     * @return E3批发销货单列表查询结果
     */
    @Override
    public Object getPfxhdList(String company,
                                             String rqStart, String rqEnd,
                                             Integer pageNo, Integer pageSize,
                                             String djbh, String pftzdDjbh,
                                             String qdCode, String djzt) {
        // 将公司名称转换为枚举
        CompanyEnum companyEnum = CompanyEnum.getByName(company);
        if (companyEnum == null) {
            log.error("未找到公司名称[{}]对应的枚举", company);
            throw new RuntimeException("未找到公司名称[" + company + "]对应的枚举");
        }
        
        // 根据公司枚举获取E3配置
        E3Config.E3 e3 = e3ConfigService.getE3ConfigByCompany(companyEnum);
        if (e3 == null) {
            log.error("未找到公司[{}]的E3配置", company);
            throw new RuntimeException("未找到公司[" + company + "]的E3配置");
        }
        
        String e3BaseUrl = e3.getIpPort();
        String key = e3.getKey();
        String secret = e3.getSecret();
        
        // 构建请求参数
        E3WholesalePfxhdGetRequest request = new E3WholesalePfxhdGetRequest();
        request.setRqStart(rqStart);
        request.setRqEnd(rqEnd);
        request.setPageNo(pageNo != null ? pageNo : 1);
        request.setPageSize(pageSize != null ? pageSize : 1000);
        
        // 添加可选参数
        if (djbh != null && !djbh.isEmpty()) {
            request.setDjbh(djbh);
        }
        if (pftzdDjbh != null && !pftzdDjbh.isEmpty()) {
            request.setPftzdDjbh(pftzdDjbh);
        }
        if (qdCode != null && !qdCode.isEmpty()) {
            request.setQdCode(qdCode);
        }
        if (djzt != null && !djzt.isEmpty()) {
            request.setDjzt(djzt);
        }

        // 记录请求参数
        log.info("调用E3获取批发销货单列表，请求参数: {}", JSON.toJSONString(request));

        // 实现重试机制
        RuntimeException lastException = null;
        for (int retry = 0; retry < MAX_RETRY_TIMES; retry++) {
            try {
                // 如果非首次尝试，记录重试信息并等待
                if (retry > 0) {
                    long delayMs = BASE_RETRY_DELAY_MS << (retry - 1); // 2秒→4秒→8秒
                    log.info("调用E3获取批发销货单列表，第 {} 次重试，等待 {} 毫秒", retry, delayMs);
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("重试等待被中断", e);
                        throw new RuntimeException("重试等待被中断", e);
                    }
                }

                // 调用E3 API
                JSONObject jsonParam = (JSONObject) JSON.toJSON(request);
                String jsonResult = E3ApiUtils.sendE3Api(e3BaseUrl, key, secret, METHOD_PFXHD_GET, jsonParam);
                
                // 解析响应结果
                E3Response e3Response = JSON.parseObject(jsonResult, E3Response.class);
                
                // 检查响应状态
                if (!"api-success".equals(e3Response.getStatus())) {
                    log.error("调用E3获取批发销货单列表失败，错误信息: {}", e3Response.getMessage());
                    throw new RuntimeException("获取E3批发销货单列表失败: " + e3Response.getMessage());
                }
                
                // 转换响应数据
                E3WholesalePfxhdGetResponse response = new E3WholesalePfxhdGetResponse();
                Object data = e3Response.getData();
                
                // 添加日志记录data的类型，帮助调试
                log.debug("E3 response data type: {}", data != null ? data.getClass().getName() : "null");
                
                // 类型检查后处理
                JSONObject jsonObject;
                if (data == null) {
                    log.warn("E3 response data is null");
                    jsonObject = new JSONObject();
                } else if (data instanceof String) {
                    // 如果data是字符串类型，直接解析
                    jsonObject = JSON.parseObject((String) data);
                } else {
                    // 其他类型先转为JSON字符串再解析
                    jsonObject = JSON.parseObject(JSON.toJSONString(data));
                }
                
                // 处理分页信息
                if (jsonObject.containsKey("page")) {
                    JSONObject pageObj = jsonObject.getJSONObject("page");
                    E3WholesalePfxhdGetResponse.Page page = JSON.toJavaObject(pageObj, E3WholesalePfxhdGetResponse.Page.class);
                    response.setPage(page);
                }
                
                // 处理批发销货单列表
                if (jsonObject.containsKey("pfxhdListGet")) {
                    List<E3WholesalePfxhdGetResponse.PfxhdListGet> pfxhdList =
                            JSON.parseArray(jsonObject.getString("pfxhdListGet"),
                                    E3WholesalePfxhdGetResponse.PfxhdListGet.class);
                    response.setPfxhdListGets(pfxhdList);
                }
                
                // 记录响应结果
                if (retry > 0) {
                    log.info("调用E3获取批发销货单列表重试成功，实际重试次数: {}，获取到批发销货单数量: {}", 
                            retry, response.getPfxhdListGets() != null ? response.getPfxhdListGets().size() : 0);
                } else {
                    log.info("调用E3获取批发销货单列表成功，获取到批发销货单数量: {}", 
                            response.getPfxhdListGets() != null ? response.getPfxhdListGets().size() : 0);
                }
                
                return response;
                
            } catch (RuntimeException e) {
                // 保留最后一次异常用于最终抛出
                lastException = e;
                log.error("调用E3获取批发销货单列表异常，准备第 {} 次重试", retry + 1, e);
            }
        }

        // 所有重试都失败了，抛出最后一次的异常
        log.error("调用E3获取批发销货单列表重试 {} 次后仍然失败", MAX_RETRY_TIMES);
        if (lastException != null) {
            throw lastException;
        } else {
            throw new RuntimeException("获取E3批发销货单列表失败，未知原因");
        }
    }
    
    // 在内部实现中还是使用强类型便于代码编写和维护
    private E3WholesalePfxhdGetResponse getPfxhdListInternal(String company,
                                         String rqStart, String rqEnd,
                                         Integer pageNo, Integer pageSize,
                                         String djbh, String pftzdDjbh,
                                         String qdCode, String djzt) {
        return (E3WholesalePfxhdGetResponse) getPfxhdList(company, rqStart, rqEnd, pageNo, pageSize, djbh, pftzdDjbh, qdCode, djzt);
    }
    
    @Override
    public void syncE3WholesalePfxhd(String companyCode) {
        String logPrefix = "[" + companyCode + "]";
        log.info("[{}][开始同步E3批发销货单]", logPrefix);

        // 设置开始日期为两个月前的1号，确保能够同步最近两个月的数据
        LocalDate startDate = LocalDate.of(2025, 1, 1);
        LocalDate endDate = LocalDate.now();

        // 按月降序遍历
        LocalDate currentMonthStart = endDate.withDayOfMonth(1);
        while (!currentMonthStart.isBefore(startDate)) {
            // 计算当月结束日期
            LocalDate currentMonthEnd;
            if (currentMonthStart.getMonth() == endDate.getMonth() && 
                currentMonthStart.getYear() == endDate.getYear()) {
                // 如果是当前月份，使用今天作为结束日期
                currentMonthEnd = endDate;
            } else {
                // 否则使用月末作为结束日期
                currentMonthEnd = currentMonthStart.plusMonths(1).minusDays(1);
            }
            
            log.info("[{}][开始处理月份数据: {} 至 {}]", 
                    logPrefix,
                    currentMonthStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                    currentMonthEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

            // 将日期转换为时间戳
            long dayStartTimestamp = E3DateUtils.localDateToStartTimestamp(currentMonthStart);
            long dayEndTimestamp = E3DateUtils.localDateToEndTimestamp(currentMonthEnd);

            // 将日期格式化为yyyyMMdd格式
            String rqStart = currentMonthStart.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String rqEnd = currentMonthEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 构建基础SQL查询
            String baseSql = buildOrderQuerySql(dayStartTimestamp, dayEndTimestamp, null);

            // 处理分页查询结果
            processPagedQueryResults(logPrefix, baseSql, companyCode, rqStart, rqEnd, currentMonthStart, currentMonthEnd);
            
            // 移动到上一个月的开始
            currentMonthStart = currentMonthStart.minusMonths(1);
        }

        log.info("[{}][结束同步E3批发销货单]", logPrefix);
    }

    /**
     * 根据单据编号同步E3批发销货单数据
     *
     * @param companyCode 公司编码
     * @param djbh 单据编号
     */
    @Override
    public void syncE3WholesalePfxhdByDjbh(String companyCode, String djbh) {
        String logPrefix = "[" + companyCode + "]";
        log.info("[{}][开始同步E3批发销货单，单据编号:{}]", logPrefix, djbh);

        try {
            // 固定起始日期为2025年1月1日
            LocalDate startDate = LocalDate.of(2025, 1, 1);
            LocalDate endDate = LocalDate.now();
            
            // 将日期转换为时间戳
            long startTimestamp = E3DateUtils.localDateToStartTimestamp(startDate);
            long endTimestamp = E3DateUtils.localDateToEndTimestamp(endDate);

            // 将日期格式化为yyyyMMdd格式
            String rqStart = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String rqEnd = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 构建带有指定单据编号的SQL
            String baseSql = buildOrderQuerySql(startTimestamp, endTimestamp, djbh);
            
            // 处理查询结果
            processPagedQueryResults(logPrefix, baseSql, companyCode, rqStart, rqEnd, startDate, endDate);
            
            log.info("[{}][结束同步E3批发销货单，单据编号:{}]", logPrefix, djbh);
        } catch (Exception e) {
            log.error("[{}][同步E3批发销货单异常，单据编号:{}]", logPrefix, djbh, e);
            throw new RuntimeException("同步E3批发销货单异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理分页查询结果
     *
     * @param logPrefix 日志前缀
     * @param baseSql 基础SQL查询
     * @param companyCode 公司代码
     * @param rqStart 开始日期
     * @param rqEnd 结束日期
     * @param currentMonthStart 当前月份开始日期
     * @param currentMonthEnd 当前月份结束日期
     */
    private void processPagedQueryResults(String logPrefix, String baseSql, String companyCode, 
                                         String rqStart, String rqEnd, 
                                         LocalDate currentMonthStart, LocalDate currentMonthEnd) {
        // 分页获取订单号并立即处理
        int pageSize = 1000;
        int offset = 0;
        while (true) {
            String paginatedSql = baseSql + " LIMIT " + offset + ", " + pageSize;
            log.info("[{}][执行SQL分页查询，日期:{}-{}, offset={}, pageSize={}]", 
                    logPrefix,
                    E3DateUtils.formatLocalDate(currentMonthStart),
                    E3DateUtils.formatLocalDate(currentMonthEnd),
                    offset, pageSize);

            SqlResult result = E3SqlApiUtils.sqlExec(
                    paginatedSql,
                    new com.alibaba.fastjson.TypeReference<SqlResult>() {},
                    companyCode.equalsIgnoreCase(COMPANY_MAIYOU) ? false : true
            );

            // 检查查询结果
            if (result == null || result.getData() == null || result.getData().isEmpty()) {
                log.info("[{}][分页查询无更多数据，日期:{}-{}, 结束]", 
                        logPrefix,
                        E3DateUtils.formatLocalDate(currentMonthStart),
                        E3DateUtils.formatLocalDate(currentMonthEnd));
                break;
            }

            List<Map<String, Object>> rows = result.getData();
            // 按照rq降序排序
            sortRowsByRqDescending(rows);
            for (Map<String, Object> row : rows) {
                String djbh = (String) row.get("billNum");
                if (StrUtil.isBlank(djbh)) {
                    log.warn("[{}][批发销货单:{}无单据编号，跳过]", logPrefix, row);
                    continue;
                }
                Object djztObj = row.get("djzt");
                String djzt = djztObj instanceof Integer ? String.valueOf(djztObj) : (String) djztObj;
                String fhlxName = (String) row.get("fhlx_name");
                log.info("[{}][开始处理批发销货单:{}], 单据状态:{}", logPrefix, djbh, djzt);
                
                try {
                    processWholesalePfxhdByDjbh(companyCode, rqStart, rqEnd, djbh, fhlxName);
                } catch (Exception e) {
                    log.error("[{}][处理批发销货单:{}异常]", logPrefix, djbh, e);
                }
            }
            
            // 更新偏移量，准备获取下一页数据
            offset += pageSize;
        }
    }

    /**
     * 按照rq字段降序排序行数据
     *
     * @param rows 需要排序的行数据列表
     */
    private void sortRowsByRqDescending(List<Map<String, Object>> rows) {
        rows.sort((a, b) -> {
            Object rqAObj = a.get("rq");
            Object rqBObj = b.get("rq");
            
            // 处理不同类型的rq值
            Long rqA = null;
            Long rqB = null;
            
            if (rqAObj instanceof Long) {
                rqA = (Long) rqAObj;
            } else if (rqAObj instanceof String) {
                rqA = Long.parseLong((String) rqAObj);
            }
            
            if (rqBObj instanceof Long) {
                rqB = (Long) rqBObj;
            } else if (rqBObj instanceof String) {
                rqB = Long.parseLong((String) rqBObj);
            }
            
            // 防止空指针异常
            if (rqA == null && rqB == null) {
                return 0;
            } else if (rqA == null) {
                return 1; // null值放在后面
            } else if (rqB == null) {
                return -1; // null值放在后面
            }
            
            return Long.compare(rqB, rqA); // 降序排列
        });
    }

    /**
     * 构建批发销货单查询SQL
     * 
     * @param startTimestamp 开始时间戳
     * @param endTimestamp 结束时间戳
     * @param djbh 单据编号，可选参数
     * @return 查询SQL语句
     */
    private String buildOrderQuerySql(long startTimestamp, long endTimestamp, String djbh) {
        // 查询已验收的批发销货单，关联渠道、客户、仓库和发货类型信息
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT a.djzt, a.ysrq AS rq, a.id, c.ckdm AS warehouseNum, c.ckmc AS warehouseName, ")
           .append("a.djbh AS billNum, kh.khdm AS customerNum, kh.khmc AS customerName, ")
           .append("qd.khdm AS storageOrgNum, qd.khmc AS storageOrgName, a.pftzd_djbh AS saleOrderNum, ")
           .append("a.ysrq AS bizDate, f.fhlx_code, f.fhlx_name ")
           .append("FROM drp_pfxhd AS a ")
           .append("LEFT JOIN kehu AS qd ON qd.id = a.qd_id ") // 关联渠道信息
           .append("LEFT JOIN kehu AS kh ON kh.id = a.kh_id ") // 关联客户信息
           .append("LEFT JOIN cangku AS c ON c.id = a.ck_id ") // 关联仓库信息
           .append("LEFT JOIN fhlx AS f ON f.fhlx_id = a.fhlx_id ") // 关联发货类型信息
           .append("WHERE ys = 1 AND rq BETWEEN ").append(startTimestamp).append(" AND ").append(endTimestamp); // 筛选已验收且在指定时间范围内的单据
        
        // 如果djbh不为空，添加单据编号过滤条件
        if (StrUtil.isNotBlank(djbh)) {
            sql.append(" AND a.djbh = '").append(djbh).append("'");
        }
        
        return sql.toString();
    }

    @Override
    public WholesaleNoticeBillSaveResponse saveWholesalePfxhd(List<WholesaleNoticeBillSaveRequest> wholesaleNoticeBills) throws IOException {
        log.info("[saveWholesaleNoticeBill][开始保存批发通知单，数量: {}, 配置信息：baseUrl={}]", 
                wholesaleNoticeBills.size(), cangQiongConfig.getBaseUrl());
        
        // 检查URL配置
        String baseUrl = cangQiongConfig.getUrl();
        if (StrUtil.isBlank(baseUrl)) {
            log.error("[saveWholesaleNoticeBill][仓穹API的URL配置为空]");
            throw new ServiceException("仓穹API的URL配置为空");
        }
        
        // 获取缓存的令牌
        String accessToken = cangQiongApiService.getTokenWithCache();
        
        // 构建请求数据
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("data", wholesaleNoticeBills);
        
        // 使用 FastJSON 进行序列化
        String requestBody = JSONObject.toJSONString(requestData);
        
        // 添加重试机制
        IOException lastException = null;
        for (int retry = 0; retry < MAX_RETRY_TIMES; retry++) {
            try {
                // 如果非首次尝试，记录重试信息
                if (retry > 0) {
                    long delayMs = BASE_RETRY_DELAY_MS << (retry - 1); // 2秒→4秒→8秒
                    log.info("[saveWholesaleNoticeBill][第 {} 次重试保存批发通知单，等待 {} 毫秒]", retry, delayMs);
                    // 增加重试延迟，避免立即重试
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("[saveWholesaleNoticeBill][重试等待被中断]", e);
                    }
                }
                
                // 创建POST请求
                Request request = new Request.Builder()
                        .url(baseUrl + URL_WHOLESALE_NOTICE_BILL_ADD)
                        .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                        .addHeader("accesstoken", accessToken)
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Idempotency-Key", String.valueOf(IdUtil.getSnowflakeNextId())) // 幂等性参数
                        .build();
                
                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        throw new ServiceException("保存批发通知单失败，状态码：" + response.code());
                    }
                    
                    String responseBody = response.body().string();
                    log.debug("[saveWholesaleNoticeBill][保存批发通知单响应：{}]", responseBody);
                    
                    try {
                        JSONObject jsonResp = JSONObject.parseObject(responseBody);
                        
                        // 检查响应状态
                        if (!jsonResp.getBooleanValue("status")) {
                            log.error("[saveWholesaleNoticeBill][保存批发通知单失败：{}]", responseBody);
                            throw new ServiceException("保存批发通知单失败：" + jsonResp.getString("message"));
                        }
                        
                        // 返回data对象
                        return JSONObject.parseObject(jsonResp.getJSONObject("data").toJSONString(), 
                                WholesaleNoticeBillSaveResponse.class);
                    } catch (Exception e) {
                        log.error("[saveWholesaleNoticeBill][解析响应失败：{}]", responseBody, e);
                        throw new ServiceException("解析响应失败：" + e.getMessage());
                    }
                }
            } catch (IOException e) {
                // 捕获IO异常（连接超时等），准备重试
                lastException = e;
                log.error("[saveWholesaleNoticeBill][请求异常，准备第 {} 次重试]", retry + 1, e);
            }
        }
        
        // 所有重试都失败了，抛出最后一次的异常
        log.error("[saveWholesaleNoticeBill][重试 {} 次后仍然失败]", MAX_RETRY_TIMES);
        if (lastException != null) {
            throw lastException;
        } else {
            throw new IOException("保存批发通知单失败，未知原因");
        }
    }

    /**
     * 根据单据编号处理批发销货单
     *
     * @param companyCode 公司代码
     * @param rqStart 开始日期
     * @param rqEnd 结束日期
     * @param djbh 单据编号
     * @param fhlxName 发货类型名称
     */
    private void processWholesalePfxhdByDjbh(String companyCode, String rqStart, String rqEnd, 
                                            String djbh, String fhlxName) {
        String logPrefix = "[" + companyCode + "]";
        E3WholesalePfxhdGetResponse response = getPfxhdListInternal(companyCode,
                rqStart, rqEnd,
                1, 50,
                djbh, null,
                null, "4");
        
        if (response != null && response.getPfxhdListGets() != null && !response.getPfxhdListGets().isEmpty()) {
            for (E3WholesalePfxhdGetResponse.PfxhdListGet pfxhd : response.getPfxhdListGets()) {
                log.info("[{}][处理批发销货单:{}], 单据状态:{}", 
                        logPrefix, pfxhd.getDjbh(), pfxhd.getDjzt());
                
                // 处理批发销货单并添加到列表中
                List<WholesaleNoticeBillSaveRequest> wholesaleNoticeBills = processWholesalePfxhd(pfxhd, fhlxName, companyCode);
                
                if (wholesaleNoticeBills != null && !wholesaleNoticeBills.isEmpty()) {
                    // 保存批发通知单
                    try {
                        saveWholesalePfxhd(wholesaleNoticeBills);
                    } catch (IOException e) {
                        log.error("[{}][保存批发销货单:{}异常]", logPrefix, pfxhd.getDjbh(), e);
                    }
                }
            }
        } else {
            log.warn("[{}][批发销货单:{}未获取到数据]", logPrefix, djbh);
        }
    }

    /**
     * 处理批发销货单数据，转换为批发通知单请求
     *
     * @param pfxhd 批发销货单数据
     * @param fhlxName 发货类型名称
     * @param companyCode 公司代码
     * @return 批发通知单请求列表
     */
    private List<WholesaleNoticeBillSaveRequest> processWholesalePfxhd(
            E3WholesalePfxhdGetResponse.PfxhdListGet pfxhd, 
            String fhlxName, 
            String companyCode) {
        
        String logPrefix = "[" + companyCode + "]";
        List<WholesaleNoticeBillSaveRequest> wholesaleNoticeBills = new ArrayList<>();
        
        try {
            // 生成单据编号并检查是否已存在
            String billno = generateBillNo(pfxhd);
            if (cqWholesaleNoticeBillService.existsByBillNo(billno)) {
                log.info("[{}][批发销货单:{}已存在，跳过]", logPrefix, billno);
                return wholesaleNoticeBills;
            }
            
            // 创建批发通知单请求
            WholesaleNoticeBillSaveRequest wholesaleNoticeBill = createWholesaleNoticeBill(pfxhd, fhlxName, billno, companyCode);
            
            // 处理明细数据
            List<WholesaleNoticeBillEntryRequest> entryentity = processMxList(pfxhd.getMxList());
            wholesaleNoticeBill.setEntryentity(entryentity);
            
            wholesaleNoticeBills.add(wholesaleNoticeBill);
        } catch (Exception e) {
            log.error("[{}][处理批发销货单:{}异常]", logPrefix, pfxhd.getDjbh(), e);
        }
        
        return wholesaleNoticeBills;
    }
    
    /**
     * 生成批发通知单单据编号
     *
     * @param pfxhd 批发销货单数据
     * @return 单据编号
     */
    private String generateBillNo(E3WholesalePfxhdGetResponse.PfxhdListGet pfxhd) {
        String qdCode = pfxhd.getQdCode();
        String qdCodePrefix = qdCode != null && qdCode.length() >= 4 ? 
                qdCode.substring(qdCode.length() - 4) : qdCode;
        return pfxhd.getDjbh() + "_" + qdCodePrefix;
    }
    
    /**
     * 根据发货类型名称获取业务场景代码
     *
     * @param fhlxName 发货类型名称
     * @return 业务场景代码
     */
    private String getBusinessSceneFromEnum(String fhlxName) {
        return BusinessSceneEnum.getSceneCodeByShippingTypeName(fhlxName);
    }
    
    /**
     * 创建批发通知单请求
     *
     * @param pfxhd 批发销货单数据
     * @param fhlxName 发货类型名称
     * @param billno 单据编号
     * @return 批发通知单请求
     */
    private WholesaleNoticeBillSaveRequest createWholesaleNoticeBill(
            E3WholesalePfxhdGetResponse.PfxhdListGet pfxhd, 
            String fhlxName, 
            String billno,
            String companyCode) {
        
        WholesaleNoticeBillSaveRequest wholesaleNoticeBill = new WholesaleNoticeBillSaveRequest();
        
        // 设置基本信息
        wholesaleNoticeBill.setBillno(billno);
        wholesaleNoticeBill.setYdShippingtype(fhlxName);
        
        // 设置业务场景
        String businessScene = getBusinessSceneFromEnum(fhlxName);
        wholesaleNoticeBill.setYdBusinessscene(businessScene);
        
        // 设置业务日期
        String rqStr = pfxhd.getRq();
        long rqLong = (rqStr != null && !rqStr.isEmpty()) ? Long.parseLong(rqStr) : 0L;
        wholesaleNoticeBill.setYdBizdate(E3DateUtils.formatTimestamp(rqLong));
        
        // 设置渠道信息
        wholesaleNoticeBill.setYdChannelno(pfxhd.getQdCode());
        wholesaleNoticeBill.setYdChannelname(pfxhd.getQdName());
        
        // 设置客户信息
        wholesaleNoticeBill.setYdCustomerno(pfxhd.getKhCode());
        wholesaleNoticeBill.setYdCustomername(pfxhd.getKhName());
        
        // 设置仓库信息
        wholesaleNoticeBill.setYdWarehouseno(pfxhd.getCkCode());
        wholesaleNoticeBill.setYdWarehousename(pfxhd.getCkName());
        
        // 设置平台和通知单号
        wholesaleNoticeBill.setYdPlatform("5");
        wholesaleNoticeBill.setYdNoticebillno(pfxhd.getPftzdDjbh());

        // 设置备注
        wholesaleNoticeBill.setYdDescription(pfxhd.getBz());

        // sap单号
        wholesaleNoticeBill.setYdSapBilllno(pfxhd.getSapDh());
        
        // 查询批发通知单获取制单人信息
        String e3Creator = getE3CreatorFromPftzd(pfxhd.getPftzdDjbh(), companyCode);
        if (StrUtil.isNotBlank(e3Creator)) {
            wholesaleNoticeBill.setYdE3creator(e3Creator);
        }

        return wholesaleNoticeBill;
    }
    
    /**
     * 从批发通知单获取制单人信息
     *
     * @param pftzdDjbh 批发通知单单据编号
     * @param companyCode 公司代码
     * @return 制单人信息，如果未找到则返回null
     */
    private String getE3CreatorFromPftzd(String pftzdDjbh, String companyCode) {
        if (StrUtil.isBlank(pftzdDjbh)) {
            return null;
        }
        
        String querySql = "SELECT zdr FROM drp_pftzd WHERE djbh = '" + pftzdDjbh + "' LIMIT 1";
        SqlResult result = E3SqlApiUtils.sqlExec(
                querySql,
                new com.alibaba.fastjson.TypeReference<SqlResult>() {},
                !COMPANY_MAIYOU.equalsIgnoreCase(companyCode)
        );
        
        // 设置E3创建人
        if (result != null && result.getData() != null && !result.getData().isEmpty()) {
            Map<String, Object> row = result.getData().get(0);
            String zdr = (String) row.get("zdr");
            if (StrUtil.isNotBlank(zdr)) {
                return zdr;
            }
        }
        
        return null;
    }
    
    /**
     * 处理明细列表
     *
     * @param mxList 明细列表
     * @return 批发通知单明细请求列表
     */
    private List<WholesaleNoticeBillEntryRequest> processMxList(List<E3WholesalePfxhdGetResponse.PfxhdListGet.MxList> mxList) {
        List<WholesaleNoticeBillEntryRequest> entryentity = new ArrayList<>();
        
        if (mxList == null || mxList.isEmpty()) {
            return entryentity;
        }
        
        for (E3WholesalePfxhdGetResponse.PfxhdListGet.MxList mx : mxList) {
            try {
                WholesaleNoticeBillEntryRequest entry = new WholesaleNoticeBillEntryRequest();
                
                // 设置商品信息
                entry.setYdMaterialno(mx.getSku());
                entry.setYdGoodsnum(mx.getSku());
                
                // 设置数量
                entry.setYdQty(parseBigDecimal(mx.getSl()));
                
                // 设置单价
                entry.setYdPrice(parseBigDecimal(mx.getDj()));
                
                // 设置实际金额
                BigDecimal jeBigDecimal = parseBigDecimal(mx.getJe());
                entry.setYdAmount(jeBigDecimal);
                
                // 设置异常信息
                boolean isError = jeBigDecimal.compareTo(BigDecimal.ZERO) < 0;
                entry.setYdErrorreason(isError ? "1" : "0");
                entry.setYdIserrorbill(isError);

                // sap行号  
                String zdyZd1 = mx.getZdyZd1();
                int sapRowIndex = 0;
                if (zdyZd1 != null && !zdyZd1.isEmpty()) {
                    try {
                        sapRowIndex = Integer.parseInt(zdyZd1);
                    } catch (NumberFormatException e) {
                        log.warn("[处理明细][SAP行号解析失败][SKU:{}][zdyZd1:{}]", mx.getSku(), zdyZd1);
                        sapRowIndex = 0;
                    }
                }
                entry.setYdSapRowindex(sapRowIndex);
                
                entryentity.add(entry);
            } catch (Exception e) {
                log.error("[处理明细异常][SKU:{}]", mx.getSku(), e);
            }
        }
        
        return entryentity;
    }
    
    /**
     * 解析字符串为BigDecimal
     *
     * @param value 字符串值
     * @return BigDecimal值
     */
    private BigDecimal parseBigDecimal(String value) {
        return (value != null && !value.isEmpty()) ? new BigDecimal(value) : BigDecimal.ZERO;
    }

} 
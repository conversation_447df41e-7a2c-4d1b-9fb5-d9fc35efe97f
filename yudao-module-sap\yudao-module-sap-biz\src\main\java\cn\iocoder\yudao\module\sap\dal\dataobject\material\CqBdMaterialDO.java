package cn.iocoder.yudao.module.sap.dal.dataobject.material;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料-主表 DO
 * 
 * 对应表：t_bd_material
 */
@TableName("t_bd_material")
@KeySequence("t_bd_material_seq") // 可能需要根据实际情况调整序列名
@Data
@ToString(callSuper = true)
public class CqBdMaterialDO {

    // ==================== 基础信息 ====================
    /**
     * 主键ID
     */
    @TableId(value = "FID")
    private Long id;

    /**
     * 编码
     */
    @TableField(value = "FNUMBER")
    private String number;

    /**
     * 名称
     */
    @TableField(value = "FNAME")
    private String name;

    /**
     * 规格型号
     */
    @TableField(value = "FMODEL")
    private String model;

    /**
     * 助记码
     */
    @TableField(value = "FHELPCODE")
    private String helpCode;

    /**
     * 简拼
     */
    @TableField(value = "FSIMPLEPINYIN")
    private String simplePinyin;

    /**
     * 图片
     */
    @TableField(value = "FLOGO")
    private String logo;

    // ==================== 组织信息 ====================
    /**
     * 管理组织ID
     * 基础资料：业务单元 bos_org
     */
    @TableField(value = "FADMINORGID")
    private Long adminOrgId;
    
    /**
     * 创建组织
     * 基础资料：业务单元 bos_org
     */
    @TableField(value = "FCREATEORGID")
    private Long createOrgId;
    
    /**
     * 管理组织
     * 基础资料：业务单元 bos_org
     */
    @TableField(value = "FORGID")
    private Long orgId;
    
    /**
     * 物料分组
     * 基础资料：物料分类 bd_materialgroup
     */
    @TableField(value = "FGROUPID")
    private Long groupId;
    
    /**
     * 控制策略
     * 枚举:1:逐级分配 2:自由分配 5:全局共享 6:管控范围内共享 7:私有
     */
    @TableField(value = "FCTRLSTRATEGY")
    private String ctrlStrategy;
    
    /**
     * 主数据内码
     */
    @TableField(value = "FMASTERID")
    private Long masterId;
    
    /**
     * 原资料id
     */
    @TableField(value = "FSOURCEDATAID")
    private Long sourceDataId;
    
    /**
     * 原资料位图
     */
    @TableField(value = "FSOURCEBITINDEX")
    private Integer sourceBitIndex;

    // ==================== 单位信息 ====================
    /**
     * 基本单位
     * 基础资料：计量单位 bd_measureunits
     */
    @TableField(value = "FBASEUNIT")
    private Long baseUnitId;
    
    /**
     * 辅助单位
     * 基础资料：计量单位 bd_measureunits
     */
    @TableField(value = "FAUXPTYUNIT")
    private Long auxptyUnitId;
    
    /**
     * 尺寸单位
     * 基础资料：计量单位 bd_measureunits
     */
    @TableField(value = "FLENGTHUNIT")
    private Long lengthUnitId;
    
    /**
     * 重量单位
     * 基础资料：计量单位 bd_measureunits
     */
    @TableField(value = "FWEIGHTUNIT")
    private Long weightUnitId;
    
    /**
     * 体积单位
     * 基础资料：计量单位 bd_measureunits
     */
    @TableField(value = "FVOLUMNUNIT")
    private Long volumeUnitId;

    // ==================== 物理信息 ====================
    /**
     * 毛重
     */
    @TableField(value = "FGROSSWEIGHT")
    private BigDecimal grossWeight;
    
    /**
     * 净重
     */
    @TableField(value = "FNETWEIGHT")
    private BigDecimal netWeight;
    
    /**
     * 长度
     */
    @TableField(value = "FLENGTH")
    private BigDecimal length;
    
    /**
     * 宽度
     */
    @TableField(value = "FWIDTH")
    private BigDecimal width;
    
    /**
     * 高度
     */
    @TableField(value = "FHEIGHT")
    private BigDecimal height;
    
    /**
     * 体积
     */
    @TableField(value = "FVOLUME")
    private BigDecimal volume;

    // ==================== 状态标识 ====================
    /**
     * 使用状态
     * 枚举:0:禁用 1:可用
     */
    @TableField(value = "FENABLE")
    private String enable;
    
    /**
     * 数据状态
     * 枚举:A:暂存 B:已提交 C:已审核
     */
    @TableField(value = "FSTATUS")
    private String status;
    
    /**
     * 位图
     */
    @TableField(value = "FBITINDEX")
    private Integer bitIndex;
    
    /**
     * 是否资产
     */
    @TableField(value = "FISASSETS")
    private String isAssets;
    
    /**
     * 是否资产属性
     */
    @TableField(value = "FISASSTATTR")
    private String isAsstAttr;
    
    /**
     * 是否库存
     */
    @TableField(value = "FISINVENTORY")
    private String isInventory;
    
    /**
     * 是否生产
     */
    @TableField(value = "FISMANUFACTURE")
    private String isManufacture;
    
    /**
     * 是否计划
     */
    @TableField(value = "FISPLAN")
    private String isPlan;
    
    /**
     * 是否采购
     */
    @TableField(value = "FISPURCHASING")
    private String isPurchasing;
    
    /**
     * 是否销售
     */
    @TableField(value = "FISSALES")
    private String isSales;
    
    /**
     * 是否委外
     */
    @TableField(value = "FISSUBCONTRACT")
    private String isSubContract;

    // ==================== 默认信息 ====================
    /**
     * 默认税率(%)
     * 基础资料：税率 bd_taxrate
     */
    @TableField(value = "FTAXRATEID")
    private Long taxRateId;
    
    /**
     * Offering
     * 基础资料：产品目录 bd_productsummary
     */
    @TableField(value = "FOFFERINGCODE")
    private Long offeringCode;

    // ==================== 审计信息 ====================
    /**
     * 审核日期
     */
    @TableField(value = "FAPPROVEDATE")
    private LocalDateTime approveDate;
    
    /**
     * 审核人
     * 基础资料：人员 bos_user
     */
    @TableField(value = "FAPPROVERID")
    private Long approverId;
    
    /**
     * 禁用时间
     */
    @TableField(value = "FDISABLEDATE")
    private LocalDateTime disableDate;
    
    /**
     * 禁用人
     * 基础资料：人员 bos_user
     */
    @TableField(value = "FDISABLERID")
    private Long disablerId;

    // ==================== 自定义字段 ====================
    /**
     * 品牌
     * 基础资料：品牌 yd_pinpai
     */
    @TableField(value = "fk_yd_basedatafield")
    private Long brandId;
    
    /**
     * 品类
     * 基础资料：品类 yd_pinlei
     */
    @TableField(value = "fk_yd_basedatafield1")
    private Long categoryId;
    
    /**
     * 剂型
     * 基础资料：剂型 yd_jixing
     */
    @TableField(value = "fk_yd_basedatafield_pinpa")
    private Long dosageFormId;
    
    /**
     * 是否s级
     */
    @TableField(value = "fk_yd_checkboxfield")
    private String isSLevel;
    
    /**
     * 非JIT
     */
    @TableField(value = "fk_yd_isjit")
    private String isNotJit;
    
    /**
     * 物料级别
     * 枚举:a:A级 b:B级 c:C级 d:D级
     */
    @TableField(value = "fk_yd_materiallevel")
    private String materialLevel;
    
    /**
     * 产品类别
     * 基础资料：产品类别 yd_procategory
     */
    @TableField(value = "fk_yd_procategoryid")
    private Long proCategoryId;
    
    /**
     * 实体仓库
     * 基础资料：实体仓库 yd_warehouse
     */
    @TableField(value = "fk_yd_warehouseid")
    private Long warehouseId;

    /**
     * 产品类型
     */
    @TableField(value = "fk_yd_protype")
    private String proType;

    // ==================== 物料分组临时信息（不映射到数据库） ====================
    /**
     * 物料分组ID（临时字段）
     */
    @TableField(exist = false)
    private Long matGroupId;

    /**
     * 物料分组编号（临时字段）
     */
    @TableField(exist = false)
    private String matGroupNum;

    /**
     * 物料分组名称（临时字段）
     */
    @TableField(exist = false)
    private String matGroupName;

} 
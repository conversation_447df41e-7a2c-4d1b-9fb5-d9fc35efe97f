package cn.iocoder.yudao.module.sap.model.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 平台仓库对应关系查询组合键
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class PlatformWarehouseKey {
    
    /**
     * 默认产品类型常量
     */
    public static final String DEFAULT_PRODUCT_TYPE = "";
    
    /**
     * 默认品牌ID常量
     */
    public static final Long DEFAULT_BRAND_ID = 0L;
    
    /**
     * 默认物料组ID常量
     */
    public static final Long DEFAULT_MATGROUP_ID = 0L;
    
    /**
     * 平台类型
     */
    private String platform;
    
    /**
     * 仓库编码
     */
    private String warehouseCode;
    
    /**
     * 产品类型
     */
    private String productType;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 物料组ID
     */
    private Long matgroupId;
    
    /**
     * 二参数构造函数（向后兼容）
     * 
     * @param platform 平台类型
     * @param warehouseCode 仓库编码
     */
    public PlatformWarehouseKey(String platform, String warehouseCode) {
        this.platform = platform;
        this.warehouseCode = warehouseCode;
        this.productType = DEFAULT_PRODUCT_TYPE;
        this.brandId = DEFAULT_BRAND_ID;
        this.matgroupId = DEFAULT_MATGROUP_ID;
    }
    
    /**
     * 三参数构造函数（向后兼容）
     * 
     * @param platform 平台类型
     * @param warehouseCode 仓库编码
     * @param productType 产品类型
     */
    public PlatformWarehouseKey(String platform, String warehouseCode, String productType) {
        this.platform = platform;
        this.warehouseCode = warehouseCode;
        this.productType = productType;
        this.brandId = DEFAULT_BRAND_ID;
        this.matgroupId = DEFAULT_MATGROUP_ID;
    }
    
    /**
     * 创建带默认产品类型的组合键
     * 
     * @param platform 平台类型
     * @param warehouseCode 仓库编码
     * @return 平台仓库组合键
     */
    public static PlatformWarehouseKey of(String platform, String warehouseCode) {
        return new PlatformWarehouseKey(platform, warehouseCode);
    }
    
    /**
     * 创建完整的三维组合键
     * 
     * @param platform 平台类型
     * @param warehouseCode 仓库编码
     * @param productType 产品类型
     * @return 平台仓库产品类型组合键
     */
    public static PlatformWarehouseKey of(String platform, String warehouseCode, String productType) {
        return new PlatformWarehouseKey(platform, warehouseCode, productType);
    }
    
    /**
     * 创建完整的五维组合键
     * 
     * @param platform 平台类型
     * @param warehouseCode 仓库编码
     * @param productType 产品类型
     * @param brandId 品牌ID
     * @param matgroupId 物料组ID
     * @return 完整的五维组合键
     */
    public static PlatformWarehouseKey of(String platform, String warehouseCode, String productType, Long brandId, Long matgroupId) {
        return new PlatformWarehouseKey(platform, warehouseCode, productType, brandId, matgroupId);
    }
    
    @Override
    public String toString() {
        return String.format("PlatformWarehouseKey{platform='%s', warehouseCode='%s', productType='%s', brandId=%d, matgroupId=%d}", 
                platform, warehouseCode, productType, brandId, matgroupId);
    }
} 